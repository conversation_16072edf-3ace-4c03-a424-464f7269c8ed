<!-- Main Container -->
<div class="container">

  <!-- Close Button -->
  <div class="close-button-wrapper">
    <button type="button" class="close-btn" (click)="dialogRef.close()" aria-label="Close">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Scrollable Content Area -->
  <mat-dialog-content>
    <div class="form-wrapper">
      <h2>Create Your Ad</h2>

      <!-- Form Progress Indicator -->
      <div class="form-progress-container">
        <div class="progress">
          <div class="progress-bar" role="progressbar" [style.width]="getFormProgress() + '%'"
               [attr.aria-valuenow]="getFormProgress()" aria-valuemin="0" aria-valuemax="100">
          </div>
        </div>
        <small class="text-muted mt-1 d-block text-center">{{ getFormProgress() }}% complete - Fill all required fields to submit</small>
      </div>

      <form [formGroup]="adForm" (ngSubmit)="onSubmit()">
        <!-- Title -->
        <div class="form-group mb-3">
          <label for="title" class="form-label fw-bold">Title</label>
          <input id="title" type="text" formControlName="title" class="form-control rounded" required>
        </div>

        <!-- Description -->
        <div class="form-group mb-3">
          <label for="description" class="form-label fw-bold">Description</label>
          <textarea id="description" formControlName="description" class="form-control rounded" rows="4" required></textarea>
        </div>

        <!-- Upload Multiple Images -->
        <div class="form-group">
          <label for="image" class="form-label">Upload Images</label>
          <div class="custom-file-upload" (click)="fileInput.click()">
            <div class="upload-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">Drag & drop images here or click to browse</div>
            <div class="upload-hint">You can select multiple images (max 5)</div>
            <input #fileInput id="image" type="file" (change)="onFileSelected($event)" style="display: none;" multiple>
          </div>

          <div *ngIf="selectedFiles.length > 0" class="selected-files">
            <div *ngFor="let file of selectedFiles; let i = index" class="file-preview">
              <i class="fas fa-image"></i>
              <span class="file-preview-name">{{ file.name }}</span>
              <button type="button" class="remove-file-btn" (click)="removeFile(i)">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Category -->
        <div class="form-group mb-3">
          <label for="category" class="form-label fw-bold">Category</label>
          <select id="category" formControlName="category" class="form-control rounded">
            <option value="" disabled selected>Choisir une catégorie</option>
            <option value="CARPOOLING">Carpooling</option>
            <option value="FASTPOST">Fast-Post</option>
            <option value="PRODUCT">Product</option>
            <option value="OTHER">Other</option>
          </select>
        </div>

        <!-- Price -->
        <div class="form-group">
          <label for="price" class="form-label">Price</label>
          <div class="price-input-container">
            <span class="price-currency">TND</span>
            <input id="price" type="number" step="0.01" min="0" formControlName="price" class="form-control" placeholder="Enter price">
          </div>
          <div class="price-preview">
            <div class="price-tag-preview" [ngClass]="{'price-na': !adForm.get('price')?.value}">
              {{ adForm.get('price')?.value ? adForm.get('price')?.value + ' TND' : 'N/A' }}
            </div>
            <small class="text-muted">This is how your price will appear in the ad listing</small>
          </div>
          <small class="text-muted">Leave empty if not applicable</small>
        </div>

        <!-- Date and Time Selection -->
        <div class="row">
          <!-- Start Date -->
          <div class="col-md-6">
            <div class="form-group mb-3">
              <label for="startDate" class="form-label fw-bold">Start Date</label>
              <input id="startDate" type="date" formControlName="startDate" class="form-control rounded" required>
            </div>
          </div>

          <!-- Start Time -->
          <div class="col-md-6">
            <div class="form-group mb-3">
              <label for="startTime" class="form-label fw-bold">Start Time</label>
              <input id="startTime" type="time" formControlName="startTime" class="form-control rounded" required>
            </div>
          </div>
        </div>

        <div class="row">
          <!-- End Date -->
          <div class="col-md-6">
            <div class="form-group mb-3">
              <label for="endDate" class="form-label fw-bold">End Date</label>
              <input id="endDate" type="date" formControlName="endDate" class="form-control rounded" required>
            </div>
          </div>

          <!-- End Time -->
          <div class="col-md-6">
            <div class="form-group mb-3">
              <label for="endTime" class="form-label fw-bold">End Time</label>
              <input id="endTime" type="time" formControlName="endTime" class="form-control rounded" required>
            </div>
          </div>
        </div>

        <!-- Toggle for using End Date as Expiry Date -->
        <div class="form-group mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="useEndDateAsExpiry" formControlName="useEndDateAsExpiry">
            <label class="form-check-label fw-bold" for="useEndDateAsExpiry">
              Use End Date as Expiration Date
              <small class="text-muted ms-2">(When the ad will no longer be visible)</small>
            </label>
          </div>
          <small class="form-text text-muted">When enabled, the ad will automatically expire on the end date.</small>
        </div>

        <!-- Tags -->
        <div class="form-group mb-3">
          <label for="tags" class="form-label fw-bold">Tags</label>
          <input id="tags" type="text" formControlName="tags" class="form-control rounded" placeholder="Enter tags separated by commas">
          <small class="text-muted">Example: car, transport, delivery</small>
        </div>

        <!-- Location Section -->
        <div class="form-group">
          <label class="form-label">Location</label>

          <!-- Location Buttons -->
          <div class="d-flex gap-2 mb-3 flex-wrap">
            <button type="button" class="toggle-map-btn" (click)="toggleMap()">
              <i class="fas" [ngClass]="showMap ? 'fa-map-marked-alt' : 'fa-map-marker-alt'"></i>
              {{ showMap ? 'Hide Map' : 'Show Map Picker' }}
            </button>

            <!-- Use My Current Location Button -->
            <button type="button" class="current-location-btn" (click)="useCurrentLocation()" [disabled]="isLocationLoading">
              <i class="fas" [ngClass]="isLocationLoading ? 'fa-spinner fa-spin' : 'fa-crosshairs'"></i>
              Use My Current Location
            </button>

            <!-- Quick Location Button -->
            <button type="button" class="quick-location-btn" (click)="showQuickLocationPicker()">
              <i class="fas fa-map-pin"></i>
              Quick Location
            </button>
          </div>

          <!-- Map Container -->
          <div *ngIf="showMap" class="map-container">
            <!-- Search Box -->
            <div class="map-search-box">
              <div class="input-group">
                <input type="text" class="form-control" placeholder="Search for a location" #searchInput>
                <button class="btn btn-outline-secondary" type="button" (click)="searchLocation(searchInput.value)">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>

            <div #mapContainer style="width: 100%; height: 100%;"></div>

            <div class="map-controls">
              <small class="text-muted d-block">Click on the map to set location or drag the marker</small>
              <div *ngIf="isLocationLoading" class="location-loading">
                <i class="fas fa-spinner fa-spin"></i> Getting your location...
              </div>
            </div>
          </div>

          <!-- Location Name -->
          <div class="form-group mb-3">
            <label for="locationName" class="form-label">
              Location Name
              <span *ngIf="locationSet" class="location-set-badge">
                <i class="fas fa-check-circle"></i> Current location set
              </span>
              <span *ngIf="locationAccuracyText" class="location-accuracy-badge" [ngClass]="{
                'high-accuracy': locationAccuracy && locationAccuracy <= 50,
                'medium-accuracy': locationAccuracy && locationAccuracy > 50 && locationAccuracy <= 500,
                'low-accuracy': locationAccuracy && locationAccuracy > 500
              }">
                <i class="fas fa-crosshairs"></i> {{ locationAccuracyText }}
              </span>
            </label>
            <input id="locationName" type="text" formControlName="locationName" class="form-control rounded" placeholder="Enter a location name">
          </div>

          <!-- Coordinates -->
          <div class="row">
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label for="longitude" class="form-label">Longitude</label>
                <input id="longitude" type="number" step="0.000001" formControlName="longitude" class="form-control rounded" placeholder="e.g. 10.181667">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label for="latitude" class="form-label">Latitude</label>
                <input id="latitude" type="number" step="0.000001" formControlName="latitude" class="form-control rounded" placeholder="e.g. 36.806389">
              </div>
            </div>
          </div>
        </div>

        <!-- Loader -->
        <div *ngIf="isLoading" class="loading-container">
          <div class="spinner"></div>
          <span class="loading-text">Chargement...</span>
        </div>

        <!-- Messages -->
        <div *ngIf="rejectedByAI" class="alert alert-danger text-center">
          ❌ Votre annonce a été rejetée automatiquement par notre système d'analyse.<br>
          ⚠️ Raison : {{ createdAd?.rejectionReason }}
        </div>

        <div *ngIf="pendingReview" class="alert alert-warning text-center">
          🕒 Votre annonce est en cours de révision par un administrateur.
        </div>

        <div *ngIf="adAccepted" class="alert alert-success text-center">
          ✅ Votre annonce a été acceptée et publiée avec succès.
        </div>

        <!-- Form Summary -->
        <div class="form-summary">
          <h5 class="form-label">Ad Summary</h5>
          <div class="summary-container p-3 border rounded bg-light">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Title:</strong> {{ adForm.get('title')?.value || 'Not set' }}</p>
                <p><strong>Category:</strong> {{ adForm.get('category')?.value || 'Not set' }}</p>
                <p><strong>Price:</strong> {{ adForm.get('price')?.value ? adForm.get('price')?.value + ' TND' : 'N/A' }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Start:</strong> {{ adForm.get('startDate')?.value || 'Not set' }}</p>
                <p><strong>End:</strong> {{ adForm.get('endDate')?.value || 'Not set' }}</p>
                <p><strong>Images:</strong> {{ selectedFiles.length }} selected</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex justify-content-center gap-3">
          <button type="submit" class="btn btn-primary" [disabled]="adForm.invalid || selectedFiles.length === 0">
            <i class="fas fa-paper-plane me-2"></i> Publish Ad
          </button>
          <button type="button" class="btn btn-secondary" (click)="dialogRef.close()">
            <i class="fas fa-times me-2"></i> Cancel
          </button>
        </div>
      </form>
    </div>
  </mat-dialog-content>
</div>
