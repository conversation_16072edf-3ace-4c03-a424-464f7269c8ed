.notification-bell-container {
  position: relative;
  display: inline-block;
}

/* Style for the badge */
::ng-deep .mat-badge-content {
  font-weight: 600;
  font-size: 12px;
  font-family: Robot<PERSON>, "Helvetica Neue", sans-serif;
}

/* Make the bell icon more visible */
.notification-bell-container i.fe-bell {
  font-size: 20px;
}

/* Style for the nav-link to match header */
.notification-bell-container .nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  cursor: pointer;
}

/* Position the badge properly */
::ng-deep .notification-bell-container .mat-badge-above.mat-badge-after .mat-badge-content {
  top: -5px;
  right: -5px;
  font-size: 10px;
  height: 18px;
  width: 18px;
  line-height: 18px;
  background: linear-gradient(135deg, #ff7676, #f44336);
  box-shadow: 0 2px 5px rgba(244, 67, 54, 0.3);
  font-weight: 700;
  transition: all 0.3s ease;
}

/* Add a subtle pulse animation to the badge when there are unread notifications */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

::ng-deep .notification-bell-container .mat-badge-content:not(.mat-badge-hidden) {
  animation: pulse 2s infinite;
}

/* Override mat-menu styling for a more elegant popup */
::ng-deep .notification-menu {
  max-width: 350px !important;
  width: 350px;
  border-radius: 8px !important;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12) !important;
}

/* Style the menu panel */
::ng-deep .mat-mdc-menu-panel.notification-menu {
  background-color: #ffffff;
}

/* Notification header styling */
.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #6ca3dc, #a3b1cf);
  color: white;
}

.notification-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.2px;
}

/* Style the mark all as read button */
.notification-header .btn-link {
  color: white;
  font-weight: 500;
  padding: 4px 10px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 11px;
}

.notification-header .btn-link:hover:not([disabled]) {
  background-color: rgba(255, 255, 255, 0.3);
}

.notification-header .btn-link:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.notification-header .btn-link i {
  font-size: 12px;
}

/* Notification list container */
.notification-list-container {
  max-height: 350px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #a3b1cf transparent;
  padding: 4px 0;
}

/* Custom scrollbar for webkit browsers */
.notification-list-container::-webkit-scrollbar {
  width: 4px;
}

.notification-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.notification-list-container::-webkit-scrollbar-thumb {
  background-color: #a3b1cf;
  border-radius: 4px;
}

.notification-list {
  padding: 0;
}

/* Notification item styling */
.notification-item {
  display: flex;
  padding: 16px 20px;
  width: 100%;
  transition: all 0.2s ease;
}

/* Add more space between notification items */
::ng-deep .mat-mdc-list-item {
  height: auto !important;
  margin-bottom: 8px;
}

/* Remove default list item padding */
::ng-deep .mat-mdc-list-item-content {
  padding: 0 !important;
}

/* Icon container */
.notification-icon {
  margin-right: 16px;
  display: flex;
  align-items: flex-start;
}

/* Icon styling */
.notification-icon i {
  font-size: 16px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

/* Different icon styles based on notification type */
.notification-icon-report {
  color: #ffffff;
  background-color: #2196F3 !important; /* Primary color for reports */
}

.notification-icon-warning {
  color: #ffffff;
  background-color: #FF9800 !important; /* Accent color for warnings */
}

.notification-icon-delete {
  color: #ffffff;
  background-color: #F44336 !important; /* Warn color for deletions */
}

.notification-icon-like {
  color: #ffffff;
  background-color: #E91E63 !important; /* Pink color for likes */
}

/* Notification content */
.notification-content {
  flex: 1;
}

/* Notification title */
.notification-content .notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.3;
}

/* Notification message */
.notification-message {
  color: rgba(0, 0, 0, 0.6);
  font-size: 13px;
  margin: 2px 0 6px;
  line-height: 1.4;
  max-width: 280px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Notification timestamp */
.notification-time {
  color: rgba(0, 0, 0, 0.4);
  font-size: 11px;
  display: flex;
  align-items: center;
}

/* Footer styling */
.notification-footer {
  display: flex;
  justify-content: center;
  padding: 8px 0;
  background-color: #f9f9f9;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* View all notifications button */
.notification-footer .btn-link {
  color: #6ca3dc;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 12px;
}

.notification-footer .btn-link:hover {
  color: #4a7ab0;
  text-decoration: none;
}

/* Empty state styling */
.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 16px;
  color: rgba(0, 0, 0, 0.4);
  background-color: #f9f9f9;
  margin: 10px;
  border-radius: 6px;
}

.no-notifications i {
  font-size: 36px;
  margin-bottom: 12px;
  color: #a3b1cf;
  opacity: 0.7;
}

.no-notifications p {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #666;
}

.no-notifications small {
  font-size: 11px;
  color: #999;
  margin-bottom: 10px;
  text-align: center;
}

/* Test notification button styling */
.no-notifications .btn-outline-primary {
  background-color: transparent;
  border: 1px solid #6ca3dc;
  color: #6ca3dc;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.no-notifications .btn-outline-primary:hover {
  background-color: #6ca3dc;
  color: white;
}

/* Styling for unread notifications */
.unread {
  background-color: rgba(108, 163, 220, 0.05);
  position: relative;
}

/* Simpler unread indicator */
.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #6ca3dc;
}

/* Remove border-left for notification types since we use the unread indicator */
.notification-item[data-type="AD_REPORT"],
.notification-item[data-type="AD_WARNING"],
.notification-item[data-type="AD_DELETION"] {
  border-left: none;
}

/* Make list items clickable with hover effect */
::ng-deep .mat-mdc-list-item {
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 0 !important;
  height: auto !important;
}

/* Fix Material list item styling */
::ng-deep .mat-mdc-list-item-unscoped-content {
  width: 100% !important;
  padding: 0 !important;
}

::ng-deep .mat-mdc-list-base {
  padding: 0 !important;
}

::ng-deep .mat-mdc-list-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* Add animation to the menu */
::ng-deep .mat-mdc-menu-content {
  animation: fadeIn 0.2s ease-out;
  padding: 0 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
