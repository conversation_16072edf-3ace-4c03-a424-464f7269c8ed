/* Container principal */
.container {
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem;
  background: linear-gradient(135deg, #6ca3dc, #a3b1cf);  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #fff;
  text-align: center;
}

/* Titre principal */
h2 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

/* Texte indiquant le montant */
p {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
}

/* Label du select */
label {
  display: block;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #e0e0e0;
}

/* Select pour le type de paiement */
select {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1.5rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 1rem;
  background-color: #fff;
  color: #333;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

select:focus {
  outline: none;
  border-color: #0074D9;
  box-shadow: 0 0 5px rgba(0, 116, 217, 0.5);
}

/* Bouton de paiement */
button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 25px;
  background-color: #28a745;
  color: #fff;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

button:hover {
  background-color: #218838;
  transform: scale(1.05);
}
