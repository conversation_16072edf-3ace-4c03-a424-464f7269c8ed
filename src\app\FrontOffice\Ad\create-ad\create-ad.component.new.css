/* Enhanced Create Ad Dialog Styles */

/* Dialog Container Styles */
::ng-deep .custom-dialog-container .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
  background-color: #f9f9fa !important;
}

/* Dialog Backdrop */
::ng-deep .custom-backdrop {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px) !important;
}

/* Main Container */
.container {
  max-width: 100%;
  padding: 0;
  position: relative;
}

/* Close Button Styling */
.close-button-wrapper {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
}

.close-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  color: #4CAF50;
  border: 2px solid #4CAF50;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
  background-color: #4CAF50;
  color: white;
  transform: rotate(90deg);
}

/* Dialog Content */
mat-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: calc(90vh - 40px) !important;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #4CAF50 #f0f0f0;
}

mat-dialog-content::-webkit-scrollbar {
  width: 6px;
}

mat-dialog-content::-webkit-scrollbar-track {
  background: #f0f0f0;
}

mat-dialog-content::-webkit-scrollbar-thumb {
  background-color: #4CAF50;
  border-radius: 6px;
}

/* Form Wrapper */
.form-wrapper {
  background-color: #fff;
  padding: 40px;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

/* Form Header */
.form-wrapper h2 {
  font-size: 28px;
  font-weight: 700;
  color: #4CAF50;
  margin-bottom: 30px;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.form-wrapper h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 2px;
}

/* Progress Bar */
.form-progress-container {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.progress {
  height: 12px;
  border-radius: 6px;
  background-color: #e9ecef;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-bar {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 6px;
  transition: width 0.5s ease;
}

/* Form Groups */
.form-group {
  margin-bottom: 25px;
}

.form-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

/* Form Controls */
.form-control {
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  padding: 12px 15px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #f9f9fa;
}

.form-control:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  background-color: #fff;
}

textarea.form-control {
  min-height: 120px;
  resize: vertical;
}

/* Select Styling */
select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%234CAF50' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  padding-right: 40px;
}

/* Date and Time Inputs */
input[type="date"], input[type="time"] {
  padding-right: 15px;
}

/* File Upload Area */
.custom-file-upload {
  border: 2px dashed #4CAF50;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  background-color: rgba(76, 175, 80, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 20px;
}

.custom-file-upload:hover {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: #2E7D32;
}

.upload-icon {
  font-size: 40px;
  color: #4CAF50;
  margin-bottom: 15px;
}

.upload-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.upload-hint {
  font-size: 14px;
  color: #666;
}

/* Selected Files */
.selected-files {
  margin-top: 15px;
}

.file-preview {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e9ecef;
}

.file-preview-name {
  flex-grow: 1;
  font-size: 14px;
  color: #333;
  margin-left: 10px;
}

.remove-file-btn {
  background-color: #f8f9fa;
  color: #dc3545;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  background-color: #dc3545;
  color: white;
}

/* Checkboxes and Radios */
.form-check {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.form-check-input {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  cursor: pointer;
}

.form-check-input:checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.form-check-label {
  font-size: 16px;
  color: #333;
  cursor: pointer;
}

/* Price Input */
.price-input-container {
  position: relative;
}

.price-input-container .form-control {
  padding-left: 40px;
}

.price-currency {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #4CAF50;
  font-weight: 600;
}

/* Price Tag Preview */
.price-preview {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 15px;
  margin-top: 10px;
  border: 1px solid #e9ecef;
}

.price-tag-preview {
  display: inline-block;
  background-color: #FF9800;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Map Container */
.map-container {
  height: 300px;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
  border: 2px solid #e0e0e0;
}

/* Toggle Map Button */
.toggle-map-btn {
  background-color: #f8f9fa;
  color: #4CAF50;
  border: 1px solid #4CAF50;
  border-radius: 8px;
  padding: 8px 15px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.toggle-map-btn:hover {
  background-color: #4CAF50;
  color: white;
}

/* Form Actions */
.d-flex.justify-content-center {
  margin-top: 40px;
}

/* Submit Button */
.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #9E9E9E, #BDBDBD);
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Cancel Button */
.btn-secondary {
  background-color: transparent;
  color: #666;
  border: 2px solid #e0e0e0;
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: #f5f5f5;
  border-color: #bdbdbd;
}

/* Alert Messages */
.alert {
  border-radius: 10px;
  padding: 15px 20px;
  margin-bottom: 25px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.alert-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #4CAF50;
}

.alert-danger {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #f44336;
}

.alert-warning {
  background-color: #fff8e1;
  color: #f57f17;
  border-left: 4px solid #ffc107;
}

.alert-info {
  background-color: #e3f2fd;
  color: #0277bd;
  border-left: 4px solid #2196f3;
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(76, 175, 80, 0.2);
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-wrapper {
    padding: 25px 20px;
  }
  
  .form-wrapper h2 {
    font-size: 24px;
  }
  
  .btn-primary, .btn-secondary {
    padding: 10px 20px;
    font-size: 15px;
  }
  
  .close-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .form-wrapper {
    padding: 20px 15px;
  }
  
  .form-wrapper h2 {
    font-size: 22px;
  }
  
  .form-label {
    font-size: 15px;
  }
  
  .form-control {
    padding: 10px 12px;
    font-size: 15px;
  }
  
  .d-flex.justify-content-center {
    flex-direction: column;
    gap: 10px !important;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
  }
}
