/* ✅ Centered Page Title */
h2 {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
  }

  /* ✅ Card Styling */
  .card {
    border-radius: 12px;
    background: white;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  }

  /* ✅ Text Container */
  p {
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 0;
  }

  /* ✅ Status Badge Colors */
  .badge {
    font-size: 16px;
    padding: 8px 14px;
    border-radius: 12px;
  }

  .bg-warning.text-dark {
    background-color: #ffc107 !important;
    color: #000 !important;
  }

  .bg-primary {
    background-color: #007bff !important;
  }

  .bg-success {
    background-color: #28a745 !important;
  }

  .bg-danger {
    background-color: #dc3545 !important;
  }

  /* Evidence Styling */
  .evidence-count .badge {
    font-size: 14px;
    padding: 8px 16px;
  }

  .evidence-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    height: 100%;
  }

  .image-container {
    max-height: 200px;
    overflow: hidden;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 10px;
  }

  .image-container img {
    max-height: 200px;
    object-fit: contain;
    width: 100%;
  }

  .file-icon {
    color: #3f51b5;
    margin-bottom: 10px;
  }

  .file-label {
    font-weight: 500;
    color: #555;
  }

  .action-buttons {
    margin-top: 10px;
  }

  /* ✅ Buttons */
  .btn-lg {
    font-size: 18px;
    padding: 12px 24px;
    border-radius: 8px;
  }

  .btn-primary:hover {
    background-color: #0056b3;
  }

  .btn-success:hover {
    background-color: #218838;
  }

  .btn-danger:hover {
    background-color: #c0392b;
  }

  /* ✅ Sidebar Styling */
  .sidebar {
    width: 250px;
    min-height: 100vh;
    background: #f8f9fa;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 56px;
    left: 0;
    padding: 15px;
    overflow-y: auto;
  }

  /* ✅ Main Content Adjustments */
  .content-container {
    margin-left: 260px;
    padding: 20px;
    flex: 1;
  }

  /* ✅ Responsive Design */
  @media (max-width: 992px) {
    .sidebar {
      width: 200px;
      padding: 10px;
    }

    .content-container {
      margin-left: 210px;
    }
  }

  @media (max-width: 768px) {
    .sidebar {
      position: absolute;
      width: 100%;
      height: auto;
      min-height: auto;
    }

    .content-container {
      margin-left: 0;
    }

    .btn-lg {
      font-size: 16px;
      padding: 10px 20px;
    }
  }
