<app-navbar-back></app-navbar-back>

<div class="d-flex">
  <!-- Sidebar (Fixed on the Left) -->
  <div class="sidebar">
    <app-sidebar-back></app-sidebar-back>
  </div>

  <!-- Main Content (Takes remaining space) -->
  <div class="flex-grow-1">
    <h2 class="text-center mb-4">
      <i class="fas fa-clipboard-list me-2"></i>Complaints Management Dashboard
    </h2>

    <!-- Filter Section -->
    <div class="filter-section mb-5">
      <!-- Filter Title -->
      <div class="filter-title mb-3">
        <i class="fas fa-filter me-2"></i>Filter Complaints
      </div>

      <!-- Categories Row with staggered animation -->
      <div class="filter-label mb-2">
        <i class="fas fa-tag me-2"></i>By Category
      </div>
      <div class="d-flex flex-wrap justify-content-center gap-3 mb-4">
        <div *ngFor="let cat of categories"
             (click)="selectCategory(cat)"
             class="category-card text-center rounded"
             [class.selected]="selectedCategory === cat">
          <div class="fw-bold">{{ formatCategoryLabel(cat) }}</div>
          <div class="fs-5 mt-2">{{ categoryCounts[cat] || 0 }}</div>
        </div>
      </div>

      <!-- Priority Filter -->
      <div class="filter-label mb-2">
        <i class="fas fa-thermometer-half me-2"></i>By Priority
      </div>
      <div class="d-flex justify-content-center gap-3 mb-4">
        <div class="priority-filter-btn"
             [class.selected]="selectedPriority === 'ALL'"
             (click)="selectPriority('ALL')">
          <i class="fas fa-layer-group"></i>
          <span>All</span>
        </div>
        <div class="priority-filter-btn priority-high"
             [class.selected]="selectedPriority === 'HIGH'"
             (click)="selectPriority('HIGH')">
          <i class="fas fa-thermometer-full"></i>
          <span>High</span>
        </div>
        <div class="priority-filter-btn priority-medium"
             [class.selected]="selectedPriority === 'MEDIUM'"
             (click)="selectPriority('MEDIUM')">
          <i class="fas fa-thermometer-half"></i>
          <span>Medium</span>
        </div>
        <div class="priority-filter-btn priority-low"
             [class.selected]="selectedPriority === 'LOW'"
             (click)="selectPriority('LOW')">
          <i class="fas fa-thermometer-quarter"></i>
          <span>Low</span>
        </div>
      </div>

      <!-- Reset Button -->
      <div class="d-flex justify-content-center">
        <button mat-button (click)="resetFilters()">
          <i class="fas fa-undo-alt me-2"></i>Reset All Filters
        </button>
      </div>
    </div>

    <!-- Pie Chart in a container -->
    <div class="chart-container mb-5">
      <h4 class="text-center mb-3">
        <i class="fas fa-chart-pie me-2"></i>Complaints Distribution
      </h4>
      <canvas id="complaintChart"></canvas>
    </div>

    <!-- Complaints Table Section -->
    <div class="table-container mt-4">
      <h4 class="text-center mb-4">
        <ng-container *ngIf="!isReportedAdsView">
          <i class="fas fa-clipboard-list me-2"></i>Complaints List
        </ng-container>
        <ng-container *ngIf="isReportedAdsView">
          <i class="fas fa-flag me-2"></i>Reported Ads
        </ng-container>
      </h4>

      <div class="table-responsive">
        <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8 table table-hover">
          <!-- Columns for normal complaints view -->
          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <div class="header-content">
                <i class="fas fa-tag"></i>
                <span>Category</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let complaint">
              <div class="category-badge" [ngClass]="'category-' + (complaint.category || 'OTHER').toLowerCase().replace('_', '-')">
                <i class="fas" [ngClass]="{
                  'fa-shopping-cart': complaint.category === 'RETARD_LIVRAISON' || complaint.category === 'LIVRAISON_MAUVAISE_ADRESSE',
                  'fa-box-open': complaint.category === 'PRODUIT_ENDOMMAGE' || complaint.category === 'ARTICLE_MANQUANT',
                  'fa-headset': complaint.category === 'MAUVAIS_SERVICE_CLIENT',
                  'fa-file-invoice-dollar': complaint.category === 'ERREUR_FACTURATION' || complaint.category === 'FRAIS_INATTENDUS',
                  'fa-clipboard-list': complaint.category === 'COMMANDE_INCOMPLETE',
                  'fa-star-half-alt': complaint.category === 'MAUVAISE_QUALITE',
                  'fa-user-tie': complaint.category === 'COMPORTEMENT_PERSONNEL',
                  'fa-tools': complaint.category === 'PROBLEME_TECHNIQUE',
                  'fa-money-bill-wave': complaint.category === 'PROBLEME_REMBOURSEMENT',
                  'fa-ad': complaint.category === 'PUBLICITE_MENSONGERE' || complaint.category === 'ADVERTISEMENT',
                  'fa-shield-alt': complaint.category === 'SECURITE_HARCELEMENT',
                  'fa-question-circle': complaint.category === 'AUTRE' || !complaint.category
                }"></i>
                <span>{{ formatCategoryLabel(complaint.category || 'AUTRE') }}</span>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <div class="header-content">
                <i class="fas fa-align-left"></i>
                <span>Description</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let complaint">
              <div class="description-container">
                <div class="description-text">{{ complaint.description }}</div>
                <div class="description-overlay"></div>
                <button class="view-more-btn" mat-button (click)="openComplaint(complaint.id)" *ngIf="complaint.description?.length > 100">
                  <i class="fas fa-eye"></i> View More
                </button>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="priority">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <div class="header-content">
                <i class="fas fa-thermometer-half"></i>
                <span>Priority</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let complaint">
              <div class="priority-badge" [ngClass]="'priority-' + (complaint.priority || 'MEDIUM').toLowerCase()">
                <i class="fas" [ngClass]="{
                  'fa-thermometer-quarter': complaint.priority === 'LOW',
                  'fa-thermometer-half': complaint.priority === 'MEDIUM' || !complaint.priority,
                  'fa-thermometer-full': complaint.priority === 'HIGH'
                }"></i>
                <span>{{ complaint.priority || 'MEDIUM' }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Contact Information Column -->
          <ng-container matColumnDef="contactInfo">
            <th mat-header-cell *matHeaderCellDef>
              <div class="header-content">
                <i class="fas fa-address-card"></i>
                <span>Contact Info</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let complaint">
              <div class="contact-info-container">
                <div *ngIf="complaint.contactEmail" class="contact-info-item">
                  <i class="fas fa-envelope"></i>
                  <span class="contact-text" [matTooltip]="complaint.contactEmail">{{ complaint.contactEmail }}</span>
                </div>
                <div *ngIf="complaint.contactPhone" class="contact-info-item">
                  <i class="fas fa-phone"></i>
                  <span class="contact-text">{{ complaint.contactPhone }}</span>
                </div>
                <div *ngIf="!complaint.contactEmail && !complaint.contactPhone" class="no-info">
                  <i class="fas fa-ban"></i>
                  <span>No contact info</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Evidence Column -->
          <ng-container matColumnDef="evidence">
            <th mat-header-cell *matHeaderCellDef>
              <div class="header-content">
                <i class="fas fa-file-alt"></i>
                <span>Evidence</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let complaint">
              <div *ngIf="complaint.evidence && complaint.evidence.length > 0" class="evidence-container">
                <button mat-flat-button color="primary" (click)="viewEvidence(complaint)" class="evidence-button">
                  <i class="fas fa-paperclip"></i>
                  <span>{{ complaint.evidence.length }} file(s)</span>
                </button>
              </div>
              <div *ngIf="!complaint.evidence || complaint.evidence.length === 0" class="no-info">
                <i class="fas fa-ban"></i>
                <span>No evidence</span>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <div class="header-content">
                <i class="fas fa-info-circle"></i>
                <span>Status</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let complaint">
              <div class="status-badge" [ngClass]="'status-' + (complaint.status || 'PENDING').toLowerCase()">
                <i class="fas" [ngClass]="{
                  'fa-hourglass-half': complaint.status === 'PENDING',
                  'fa-check-circle': complaint.status === 'TREATED',
                  'fa-times-circle': complaint.status === 'REJECTED',
                  'fa-eye': complaint.status === 'OPENED'
                }"></i>
                <span>{{ complaint.status || 'PENDING' }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Columns for reported ads view -->
          <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <div class="header-content">
                <i class="fas fa-ad"></i>
                <span>Ad Title</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let ad">
              <div class="ad-title-container">
                <i class="fas fa-ad"></i>
                <span class="ad-title-text">{{ ad.title }}</span>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="reportCount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <div class="header-content">
                <i class="fas fa-flag"></i>
                <span>Reports</span>
              </div>
            </th>
            <td mat-cell *matCellDef="let ad">
              <button
                class="report-count-button"
                (click)="openAdComplaintsPopup(ad.id!, ad.reportCount)"
                [matTooltip]="'View ' + ad.reportCount + ' reports'"
              >
                <span class="report-count">{{ ad.reportCount }}</span>
                <i class="fas fa-eye"></i>
              </button>
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
              <div class="header-content">
                <i class="fas fa-cogs"></i>
                <span>Actions</span>
              </div>
            </th>
            <!-- For normal complaints -->
            <td mat-cell *matCellDef="let item">
              <div class="actions-container">
                <!-- If we're in the reported ads view -->
                <ng-container *ngIf="isReportedAdsView && item.id">
                  <button class="action-button edit" [routerLink]="['/admin/ad', item.id]" matTooltip="Edit Ad">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="action-button view" (click)="openAdComplaintsPopup(item.id, item.reportCount)" matTooltip="View Reports">
                    <i class="fas fa-flag"></i>
                  </button>
                </ng-container>

                <!-- If we're in the normal complaints view -->
                <ng-container *ngIf="!isReportedAdsView && item.id">
                  <button class="action-button view" (click)="openComplaint(item.id)" matTooltip="Open Complaint">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button class="action-button treat" (click)="treatComplaint(item.id)" matTooltip="Mark as Treated">
                    <i class="fas fa-check-circle"></i>
                  </button>
                  <button class="action-button delete" (click)="deleteComplaint(item.id)" matTooltip="Delete Complaint">
                    <i class="fas fa-trash"></i>
                  </button>
                </ng-container>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"
              [ngClass]="{'advertisement-row': !isReportedAdsView && row.category === 'ADVERTISEMENT'}"></tr>
        </table>
      </div>

      <!-- No data message -->
      <div *ngIf="dataSource?.data?.length === 0" class="text-center py-5">
        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No complaints found</h5>
        <p class="text-muted">There are no complaints matching your current filter criteria.</p>
      </div>

      <!-- Pagination inside the card -->
      <div class="d-flex justify-content-center mt-4">
        <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 25]" showFirstLastButtons
                      aria-label="Select page of complaints"></mat-paginator>
      </div>

    </div>
  </div>
</div>

<app-footer-back></app-footer-back>
