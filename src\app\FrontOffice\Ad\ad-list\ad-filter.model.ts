export interface AdFilter {
  categories: string[];
  priceMin?: number;
  priceMax?: number;
  dateFilter?: 'last24h' | 'lastWeek' | 'lastMonth' | 'custom';
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  location?: string;
  locationRadius?: number;
}

export const defaultFilter: AdFilter = {
  categories: [],
  priceMin: undefined,
  priceMax: undefined,
  dateFilter: undefined,
  startDateFrom: undefined,
  startDateTo: undefined,
  endDateFrom: undefined,
  endDateTo: undefined,
  location: undefined,
  locationRadius: 10 // Default radius in km
};
