/* Container principal avec fond en dégradé bleu */
.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1rem;
  background: linear-gradient(135deg, #001f3f, #0074D9); /* Dégradé de bleu */
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #fff;
}

/* Style de la carte */
.card {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 1rem auto;
  animation: fadeInUp 0.6s ease-out;
}

/* Animation d'apparition */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style de l'image de la carte */
.card-img-top {
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-img-top:hover {
  transform: scale(1.05);
}

/* Corps de la carte */
.card-body {
  padding: 1.5rem;
}

.card-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #333;
}

.card-text {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #555;
}

/* Liste des informations produit */
.list-group-item {
  background: transparent;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  color: #333;
}

/* Pied de la carte */
.card-footer {
  padding: 1rem;
  background: transparent;
  border-top: none;
  text-align: right;
}

/* Bouton Retour */
.btn.btn-primary {
  background-color: #0074D9;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  transition: background-color 0.3s ease, transform 0.3s ease;
  color: #fff;
}

.btn.btn-primary:hover {
  background-color: #0056b3;
  transform: scale(1.05);
}

/* Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}
