import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { Complaint, ComplaintControllerService } from 'src/app/openapi';

interface ComplaintType {
  value: string;
  label: string;
  icon: string;
  description: string;
}

type TabType = 'details' | 'evidence' | 'contact';

@Component({
  selector: 'app-ad-complaint-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatButtonToggleModule
  ],
  templateUrl: './ad-complaint-dialog.component.html',
  styleUrls: ['./ad-complaint-dialog.component.css']
})
export class AdComplaintDialogComponent {
  complaintForm: FormGroup;
  submitting = false;
  error = '';

  // Tab navigation
  activeTab: TabType = 'details';

  // File upload properties
  hasFile = false;
  fileName = '';
  fileData: File | null = null;

  // Selected complaint type
  selectedComplaintType = 'INAPPROPRIATE_CONTENT';

  // Complaint types - made more compact for the new design
  complaintTypes: ComplaintType[] = [
    {
      value: 'INAPPROPRIATE_CONTENT',
      label: 'Inappropriate',
      icon: 'block',
      description: 'Content that violates guidelines'
    },
    {
      value: 'MISLEADING_INFO',
      label: 'Misleading',
      icon: 'info',
      description: 'False information'
    },
    {
      value: 'SPAM',
      label: 'Spam',
      icon: 'report',
      description: 'Unwanted content'
    },
    {
      value: 'FRAUD',
      label: 'Fraud',
      icon: 'warning',
      description: 'Scam activity'
    }
  ];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AdComplaintDialogComponent>,
    private complaintService: ComplaintControllerService,
    @Inject(MAT_DIALOG_DATA) public data: { adId: string, adTitle: string }
  ) {
    this.complaintForm = this.fb.group({
      title: [data.adTitle ? `Complaint about ad: ${data.adTitle}` : 'Ad Complaint', Validators.required],
      description: ['', [
        Validators.required,
        Validators.minLength(10),
        Validators.maxLength(500)
      ]],
      email: ['', [Validators.email]],
      phone: [''],
      urgency: ['MEDIUM'],
      status: ['PENDING'],
      complaintType: ['INAPPROPRIATE_CONTENT']
    });
  }

  /**
   * Set the active tab
   */
  setActiveTab(tab: TabType): void {
    this.activeTab = tab;
  }

  /**
   * Set urgency level
   */
  setUrgency(level: string): void {
    this.complaintForm.patchValue({ urgency: level });
  }

  /**
   * Select a complaint type
   */
  selectComplaintType(type: string): void {
    this.selectedComplaintType = type;
    this.complaintForm.patchValue({ complaintType: type });
  }

  /**
   * Check if description length is approaching the limit
   */
  isDescriptionLengthWarning(): boolean {
    const length = this.complaintForm.get('description')?.value?.length || 0;
    return length > 400 && length <= 500;
  }

  /**
   * Handle file selection
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.error = 'File size exceeds 5MB limit.';
        return;
      }

      this.fileData = file;
      this.fileName = file.name;
      this.hasFile = true;
    }
  }

  /**
   * Remove selected file
   */
  removeFile(event: Event): void {
    event.stopPropagation();
    this.fileData = null;
    this.fileName = '';
    this.hasFile = false;
  }

  /**
   * Submit the complaint form
   */
  onSubmit(): void {
    if (this.complaintForm.valid) {
      this.submitting = true;
      this.error = '';

      // Get form values
      const formValues = this.complaintForm.value;

      // Create complaint object with correct field mappings
      const newComplaint: Complaint = {
        title: formValues.title,
        description: formValues.description,
        category: 'ADVERTISEMENT', // Set category to ADVERTISEMENT for ad reports
        status: formValues.status,
        priority: formValues.urgency, // Map urgency to priority
        contactEmail: formValues.email, // Map email to contactEmail
        contactPhone: formValues.phone, // Map phone to contactPhone
        adId: this.data.adId, // Set the ad ID
        evidence: [] // Initialize empty evidence array
      };

      // Upload evidence if available
      if (this.hasFile && this.fileData) {
        // Convert file to base64 for storage
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            // Add the base64 file to the evidence array
            const base64File = e.target.result.toString();
            newComplaint.evidence = [base64File];

            // Now submit the complaint with the evidence
            this.submitComplaint(newComplaint);
          }
        };
        reader.readAsDataURL(this.fileData);
      } else {
        // No file to upload, submit the complaint as is
        this.submitComplaint(newComplaint);
      }
    } else {
      // If form is invalid, show the details tab where most validation errors would be
      this.activeTab = 'details';

      // Mark all fields as touched to trigger validation messages
      Object.keys(this.complaintForm.controls).forEach(key => {
        this.complaintForm.get(key)?.markAsTouched();
      });
    }
  }

  /**
   * Submit the complaint to the backend
   */
  private submitComplaint(complaint: Complaint): void {
    console.log('Submitting complaint:', complaint);

    this.complaintService.createComplaint(complaint).subscribe({
      next: () => {
        this.submitting = false;
        this.dialogRef.close(true);
      },
      error: (err) => {
        this.submitting = false;
        this.error = 'Failed to submit complaint. Please try again.';
        console.error('Error submitting complaint', err);
      }
    });
  }

  /**
   * Cancel and close the dialog
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }
}
