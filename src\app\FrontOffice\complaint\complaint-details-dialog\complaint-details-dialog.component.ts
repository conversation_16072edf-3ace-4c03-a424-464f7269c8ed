import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { Complaint, ComplaintControllerService } from 'src/app/openapi';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-complaint-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatIconModule
  ],
  templateUrl: './complaint-details-dialog.component.html',
  styleUrls: ['./complaint-details-dialog.component.css']
})
export class ComplaintDetailsDialogComponent implements OnInit {
  complaint: Complaint = { title: '', description: '', category: Complaint.CategoryEnum.Autre, status: 'PENDING' };
  loading = true;
  error = '';

  constructor(
    private dialogRef: MatDialogRef<ComplaintDetailsDialogComponent>,
    private complaintService: ComplaintControllerService,
    private sanitizer: DomSanitizer,
    @Inject(MAT_DIALOG_DATA) public data: { complaintId: string }
  ) {}

  ngOnInit(): void {
    this.loadComplaintDetails();
  }

  loadComplaintDetails(): void {
    if (!this.data.complaintId) {
      this.error = 'No complaint ID provided';
      this.loading = false;
      return;
    }

    this.complaintService.getComplaint(this.data.complaintId).subscribe({
      next: async (response) => {
        if (response instanceof Blob) {
          const text = await response.text();
          this.complaint = JSON.parse(text);
        } else {
          this.complaint = response;
        }
        this.loading = false;
        console.log("Complaint details:", this.complaint);
      },
      error: (err) => {
        console.error('Error retrieving complaint details', err);
        this.error = 'Failed to load complaint details';
        this.loading = false;
      }
    });
  }

  close(): void {
    this.dialogRef.close();
  }

  // Format category name for better display
  formatCategoryName(category: string | undefined): string {
    if (!category) return 'Unknown';

    // Convert from enum format (e.g., RETARD_LIVRAISON) to readable format (e.g., Delivery Delay)
    const formatted = category
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, char => char.toUpperCase());

    return formatted;
  }

  // Format date for better display
  formatDate(dateString: string | Date): string {
    if (!dateString) return 'Unknown';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Check if complaint has any contact information
  hasContactInfo(): boolean {
    return !!(
      this.complaint.contactPreference ||
      this.complaint.contactEmail ||
      this.complaint.contactPhone
    );
  }

  // Check if a file is an image (based on base64 data)
  isImageFile(base64String: string): boolean {
    if (!base64String) return false;

    // Check if it's a base64 image
    return base64String.startsWith('data:image/');
  }

  // View full-size image in a new tab
  viewFullImage(base64Image: string): void {
    if (!base64Image) return;

    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>Evidence Image</title>
            <style>
              body {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                background-color: #1a1a1a;
              }
              img {
                max-width: 95%;
                max-height: 95%;
                object-fit: contain;
                box-shadow: 0 0 20px rgba(0,0,0,0.5);
              }
              .close-btn {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(255,255,255,0.8);
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 20px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            </style>
          </head>
          <body>
            <button class="close-btn" onclick="window.close()">×</button>
            <img src="${base64Image}" alt="Evidence Image">
          </body>
        </html>
      `);
    }
  }

  // Download file from base64 string
  downloadFile(base64Data: string, index: number): void {
    if (!base64Data) return;

    try {
      // Extract MIME type and actual base64 data
      const matches = base64Data.match(/^data:(.+);base64,(.+)$/);

      if (matches && matches.length === 3) {
        const contentType = matches[1];
        const base64 = matches[2];

        // Convert base64 to blob
        const byteCharacters = atob(base64);
        const byteArrays = [];

        for (let offset = 0; offset < byteCharacters.length; offset += 512) {
          const slice = byteCharacters.slice(offset, offset + 512);

          const byteNumbers = new Array(slice.length);
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }

          const byteArray = new Uint8Array(byteNumbers);
          byteArrays.push(byteArray);
        }

        const blob = new Blob(byteArrays, { type: contentType });

        // Create download link
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // Generate filename based on content type and index
        const extension = contentType.split('/')[1] || 'file';
        a.download = `evidence-file-${index + 1}.${extension}`;

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);
      } else {
        console.error('Invalid base64 format');
      }
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  }
}
