<div class="notifications-container">
  <div class="notifications-header">
    <h1>Notifications</h1>
    <button mat-raised-button color="primary" [disabled]="!notifications.length || loading" (click)="markAllAsRead()">
      Mark All as Read
    </button>
  </div>

  <mat-divider></mat-divider>

  <div class="notifications-content">
    <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>

    <div *ngIf="error" class="error-message">
      <mat-icon>error</mat-icon>
      <p>{{ error }}</p>
    </div>

    <div *ngIf="!loading && !error && notifications.length === 0" class="empty-state">
      <mat-icon>notifications_off</mat-icon>
      <p>You don't have any notifications yet.</p>
    </div>

    <mat-card *ngFor="let notification of notifications"
              class="notification-card"
              [ngClass]="{'unread': !notification.read}"
              [attr.data-type]="notification.type">
      <mat-card-header>
        <div mat-card-avatar class="notification-icon">
          <mat-icon [color]="getNotificationColor(notification.type)">{{ getNotificationIcon(notification.type) }}</mat-icon>
        </div>
        <mat-card-title>{{ notification.title }}</mat-card-title>
        <mat-card-subtitle>{{ formatDate(notification.date) }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>{{ notification.message }}</p>
      </mat-card-content>
      <mat-card-actions align="end">
        <button mat-button *ngIf="!notification.read" (click)="markAsRead(notification)">MARK AS READ</button>
        <button mat-button color="warn" (click)="deleteNotification(notification)">DELETE</button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
