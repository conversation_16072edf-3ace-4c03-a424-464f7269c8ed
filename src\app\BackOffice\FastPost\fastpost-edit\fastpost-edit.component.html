<app-navbar-back></app-navbar-back>

<app-sidebar-back></app-sidebar-back>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">Modify deliver fastPost Request</h2>
    <form [formGroup]="fastPostForm" (ngSubmit)="onSubmit()">
      <div class="form-group mb-3">
        <label for="receiverName" class="form-label fw-bold">receiver Name</label>
        <input type="text" id="receiverName" formControlName="receiverName" class="form-control" readonly />
      </div>
      <div class="mb-3">
        <label for="receiverAddress" class="form-label">receiver Address</label>
        <input type="text" id="receiverAddress" class="form-control" formControlName="receiverAddress" readonly >
      </div>
      <div class="mb-3">
        <label for="receiverTelNbr" class="form-label">receiver Telephone number</label>
        <input type="text" id="receiverTelNbr" class="form-control" formControlName="receiverTelNbr" readonly>
      </div>
      <div class="mb-3">
        <label for="packageWeight" class="form-label">package Weight with kg</label>
        <input type="text" id="packageWeight" class="form-control" formControlName="packageWeight" readonly>
      </div>

      <div class="form-group mb-3">
        <label for="fastPostStatus" class="form-label">fastPost Status</label>
        <select id="fastPostStatus" formControlName="fastPostStatus" class="form-control">
          <option value="" disabled selected>-- Select Vehicle add Status --</option>
          <option *ngFor="let addstatus of fastPostStatus" [value]="addstatus">{{ addstatus }}</option>
        </select>
      </div>

      <div class="d-flex justify-content-center gap-3">
        <button type="submit" class="btn btn-primary btn-lg px-4">
          <i class="fas fa-save"></i> Update
        </button>
        <button type="button" class="btn btn-secondary btn-lg px-4" (click)="cancelEdit()">
          <i class="fas fa-times"></i> Cancel
        </button>
      </div>

    </form>
  </div>
</div>

