<app-header-front></app-header-front>

<div class="container mt-4 mb-5">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-clipboard-list me-2"></i>My Complaints
        <span class="badge bg-white text-primary ms-2">{{ complaints.length }}</span>
      </h2>
      <p class="page-subtitle">View and manage your submitted complaints and track their status</p>
    </div>
    <a class="btn btn-primary add-btn" [routerLink]="['/complaintadd']">
      <i class="fas fa-plus me-2"></i>New Complaint
    </a>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <div class="search-box">
      <i class="fas fa-search search-icon"></i>
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (input)="applyFilters()"
        placeholder="Search by title or description..."
        class="search-input">
      <button
        *ngIf="searchTerm"
        (click)="searchTerm = ''; applyFilters()"
        class="clear-search-btn"
        matTooltip="Clear search">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="filters-container">
      <div class="filter-group">
        <label for="statusFilter" class="filter-label">
          <i class="fas fa-tasks me-2"></i>Status
        </label>
        <select
          id="statusFilter"
          [(ngModel)]="selectedStatus"
          (change)="applyFilters()"
          class="filter-select">
          <option *ngFor="let option of statusOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <div class="filter-group">
        <label for="categoryFilter" class="filter-label">
          <i class="fas fa-tag me-2"></i>Category
        </label>
        <select
          id="categoryFilter"
          [(ngModel)]="selectedCategory"
          (change)="applyFilters()"
          class="filter-select">
          <option *ngFor="let option of categoryOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <button
        (click)="resetFilters()"
        class="reset-btn"
        [disabled]="selectedStatus === 'ALL' && selectedCategory === 'ALL' && !searchTerm"
        matTooltip="Reset all filters">
        <i class="fas fa-undo me-2"></i> Reset Filters
      </button>

      <div class="view-toggle">
        <button
          (click)="viewMode = 'grid'"
          class="view-btn"
          [class.active]="viewMode === 'grid'"
          matTooltip="Grid View">
          <i class="fas fa-th-large"></i>
        </button>
        <button
          (click)="viewMode = 'list'"
          class="view-btn"
          [class.active]="viewMode === 'list'"
          matTooltip="List View">
          <i class="fas fa-list"></i>
        </button>
      </div>
    </div>

    <!-- Active Filters -->
    <div class="active-filters" *ngIf="selectedStatus !== 'ALL' || selectedCategory !== 'ALL' || searchTerm">
      <div class="active-filters-label"><i class="fas fa-filter me-2"></i>Active Filters</div>
      <div class="filter-tags">
        <div class="filter-tag" *ngIf="searchTerm">
          <i class="fas fa-search me-1"></i>
          "{{ searchTerm }}"
          <button class="tag-remove" (click)="searchTerm = ''; applyFilters()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="filter-tag" *ngIf="selectedStatus !== 'ALL'">
          <i class="fas fa-filter me-1"></i>
          Status: {{ selectedStatus }}
          <button class="tag-remove" (click)="selectedStatus = 'ALL'; applyFilters()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="filter-tag" *ngIf="selectedCategory !== 'ALL'">
          <i class="fas fa-tag me-1"></i>
          Category: {{ formatCategoryName(selectedCategory) }}
          <button class="tag-remove" (click)="selectedCategory = 'ALL'; applyFilters()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      <div class="results-count">
        <i class="fas fa-clipboard-check me-2"></i>{{ filteredComplaints.length }} result{{ filteredComplaints.length !== 1 ? 's' : '' }} found
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="loading-text">Loading complaints...</p>
  </div>

  <!-- No Results Message -->
  <div *ngIf="!isLoading && filteredComplaints.length === 0" class="no-results">
    <div class="no-results-content">
      <i class="fas fa-search fa-3x mb-3"></i>
      <h4>No complaints found</h4>
      <p *ngIf="searchTerm || selectedStatus !== 'ALL' || selectedCategory !== 'ALL'">
        Try adjusting your filters or create a new complaint
      </p>
      <p *ngIf="!searchTerm && selectedStatus === 'ALL' && selectedCategory === 'ALL'">
        You haven't submitted any complaints yet
      </p>
      <div class="no-results-actions">
        <button *ngIf="searchTerm || selectedStatus !== 'ALL' || selectedCategory !== 'ALL'"
                class="btn btn-outline-primary me-3"
                (click)="resetFilters()">
          <i class="fas fa-undo me-1"></i> Reset Filters
        </button>
        <a class="btn btn-primary" [routerLink]="['/complaintadd']">
          <i class="fas fa-plus me-1"></i> New Complaint
        </a>
      </div>
    </div>
  </div>

  <!-- Grid View -->
  <div *ngIf="!isLoading && filteredComplaints.length > 0 && viewMode === 'grid'" class="complaints-grid">
    <div class="row">
      <div *ngFor="let complaint of filteredComplaints" class="col-lg-4 col-md-6 mb-4">
        <div class="complaint-card" (click)="viewComplaint(complaint.id!)">
          <!-- Status Badge -->
          <div class="status-badge" [ngClass]="getStatusColorClass(complaint.status!)">
            <i class="fas" [ngClass]="{
              'fa-hourglass-half': complaint.status === 'PENDING',
              'fa-eye': complaint.status === 'OPENED',
              'fa-check-circle': complaint.status === 'TREATED'
            }"></i>
            {{ complaint.status }}
          </div>

          <!-- Card Content -->
          <div class="card-content">
            <h3 class="complaint-title">{{ complaint.title }}</h3>

            <div class="complaint-meta">
              <div class="complaint-category">
                <i class="fas fa-tag me-1"></i>
                {{ formatCategoryName(complaint.category!) }}
              </div>

              <div class="complaint-date">
                <i class="far fa-calendar-alt me-1"></i>
                {{ getCurrentDate() }}
              </div>
            </div>

            <p class="complaint-description">
              {{ truncateText(complaint.description, 100) }}
            </p>
          </div>

          <!-- Card Actions -->
          <div class="card-actions">
            <button class="action-btn view-btn" (click)="viewComplaint(complaint.id!); $event.stopPropagation()">
              <i class="fas fa-eye"></i>
            </button>
            <button *ngIf="complaint.status === 'PENDING'" class="action-btn edit-btn" (click)="editComplaint(complaint.id!, $event)">
              <i class="fas fa-edit"></i>
            </button>
            <button *ngIf="complaint.status === 'PENDING'" class="action-btn delete-btn" (click)="deleteComplaint(complaint.id!, $event)">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- List View -->
  <div *ngIf="!isLoading && filteredComplaints.length > 0 && viewMode === 'list'" class="complaints-list">
    <div *ngFor="let complaint of filteredComplaints" class="complaint-list-item" (click)="viewComplaint(complaint.id!)">
      <div class="list-item-content">
        <div class="list-item-header">
          <h3 class="complaint-title">{{ complaint.title }}</h3>
          <div class="status-badge" [ngClass]="getStatusColorClass(complaint.status!)">
            <i class="fas" [ngClass]="{
              'fa-hourglass-half': complaint.status === 'PENDING',
              'fa-eye': complaint.status === 'OPENED',
              'fa-check-circle': complaint.status === 'TREATED'
            }"></i>
            {{ complaint.status }}
          </div>
        </div>

        <div class="list-item-body">
          <div class="complaint-meta">
            <div class="complaint-category">
              <i class="fas fa-tag me-1"></i>
              {{ formatCategoryName(complaint.category!) }}
            </div>

            <div class="complaint-date">
              <i class="far fa-calendar-alt me-1"></i>
              {{ getCurrentDate() }}
            </div>
          </div>

          <p class="complaint-description">
            {{ truncateText(complaint.description, 150) }}
          </p>
        </div>
      </div>

      <div class="list-item-actions">
        <button class="action-btn view-btn" (click)="viewComplaint(complaint.id!); $event.stopPropagation()">
          <i class="fas fa-eye"></i>
        </button>
        <button *ngIf="complaint.status === 'PENDING'" class="action-btn edit-btn" (click)="editComplaint(complaint.id!, $event)">
          <i class="fas fa-edit"></i>
        </button>
        <button *ngIf="complaint.status === 'PENDING'" class="action-btn delete-btn" (click)="deleteComplaint(complaint.id!, $event)">
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<app-footer-front></app-footer-front>
