/* Dialog Styling */
.complaint-edit-dialog {
  max-width: 700px;
  width: 100%;
  animation: scaleIn 0.3s ease-out;
  padding: 20px;
}

/* Dialog Title */
h2[mat-dialog-title] {
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
  display: flex;
  align-items: center;
}

/* Dialog Content */
mat-dialog-content {
  min-height: 200px;
  max-height: 70vh;
  overflow-y: auto;
}

/* Loading Spinner */
mat-spinner {
  margin: 2rem auto;
}

/* Form Styling */
.complaint-form {
  padding: 10px;
}

/* Form Labels */
.form-label {
  font-size: 16px;
  color: #2c3e50;
  font-weight: bold;
}

/* Input Fields */
.form-control {
  font-size: 16px;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #3498db;
  border-color: #3498db;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #95a5a6;
  border-color: #95a5a6;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  border-color: #7f8c8d;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Custom scrollbar */
mat-dialog-content::-webkit-scrollbar {
  width: 8px;
}

mat-dialog-content::-webkit-scrollbar-track {
  background: rgba(241, 241, 241, 0.5);
  border-radius: 10px;
  margin: 10px 0;
}

mat-dialog-content::-webkit-scrollbar-thumb {
  background: #3498db;
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

mat-dialog-content::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
  border: 2px solid transparent;
  background-clip: padding-box;
}

/* Error message styling */
.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

/* Validation error messages */
.text-danger {
  font-size: 12px;
}

/* Character counter */
.character-counter {
  font-size: 12px;
  color: #6c757d;
  text-align: right;
  margin-top: 2px;
}

/* Priority options */
.priority-options {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.priority-option {
  position: relative;
}

.priority-radio {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.priority-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  text-align: center;
}

.priority-label i {
  font-size: 20px;
  margin-bottom: 5px;
}

.priority-label[data-priority="LOW"] i {
  color: #28a745;
}

.priority-label[data-priority="MEDIUM"] i {
  color: #ffc107;
}

.priority-label[data-priority="HIGH"] i {
  color: #dc3545;
}

.priority-radio:checked + .priority-label {
  border-color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Contact options */
.contact-options {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.contact-option {
  position: relative;
}

.contact-radio {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.contact-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  text-align: center;
}

.contact-label i {
  font-size: 20px;
  margin-bottom: 5px;
  color: #3498db;
}

.contact-radio:checked + .contact-label {
  border-color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* File upload area */
.custom-file-upload {
  border: 2px dashed #3498db;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(52, 152, 219, 0.05);
  margin-top: 10px;
}

.custom-file-upload:hover {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: #2980b9;
}

.upload-icon {
  font-size: 40px;
  color: #3498db;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 5px;
}

.upload-hint {
  font-size: 12px;
  color: #7f8c8d;
}

/* Selected files preview */
.selected-files {
  margin-top: 15px;
}

.file-preview {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8f9fa;
  margin-bottom: 10px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.file-preview:hover {
  background-color: #f1f3f5;
}

.file-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
  color: #3498db;
}

.file-info {
  flex-grow: 1;
  margin-left: 10px;
}

.file-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.file-size {
  font-size: 12px;
  color: #7f8c8d;
}

.file-preview button {
  background-color: #e74c3c;
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.file-preview button:hover {
  background-color: #c0392b;
  transform: scale(1.1);
}
