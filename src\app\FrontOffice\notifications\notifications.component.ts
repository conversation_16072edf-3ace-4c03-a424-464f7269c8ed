import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService } from '../../services/notification.service';
import { ExtendedNotification } from '../../models/notification.model';
import { AuthService } from '../../services/auth.service';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatProgressBarModule
  ]
})
export class NotificationsComponent implements OnInit {
  notifications: ExtendedNotification[] = [];
  loading = false;
  error: string | null = null;

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.loadNotifications();
  }

  async loadNotifications(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      // Get the current user ID
      const userId = await this.getCurrentUserId();

      if (userId) {
        // Load notifications for the current user
        this.notificationService.getNotificationsByUserId(userId).subscribe({
          next: (notifications) => {
            this.notifications = notifications;
            this.loading = false;
          },
          error: (error) => {
            console.error('Error loading notifications:', error);
            this.error = 'Failed to load notifications. Please try again later.';
            this.loading = false;
          }
        });
      } else {
        this.error = 'User not logged in';
        this.loading = false;
      }
    } catch (error) {
      console.error('Error getting user ID:', error);
      this.error = 'Failed to authenticate user. Please try again later.';
      this.loading = false;
    }
  }

  markAsRead(notification: ExtendedNotification): void {
    if (!notification.read && notification.id) {
      this.notificationService.markNotificationAsRead(notification.id).subscribe({
        next: () => {
          if (notification.read !== undefined) {
            notification.read = true;
          }
        },
        error: (error: any) => {
          console.error('Error marking notification as read:', error);
        }
      });
    }
  }

  markAllAsRead(): void {
    const unreadNotifications = this.notifications.filter(n => !n.read);

    if (unreadNotifications.length === 0) {
      return;
    }

    // Create an array of observables for each notification
    const markReadRequests = unreadNotifications.map(notification =>
      this.notificationService.markNotificationAsRead(notification.id!)
    );

    // Execute all requests - filter out any undefined IDs
    const notificationIds = unreadNotifications.map(n => n.id).filter((id): id is string => id !== undefined);
    this.notificationService.markAllNotificationsAsRead(notificationIds).subscribe({
      next: () => {
        // Update the local notifications
        this.notifications.forEach(notification => {
          if (notification.read !== undefined) {
            notification.read = true;
          }
        });
      },
      error: (error: any) => {
        console.error('Error marking all notifications as read:', error);
      }
    });
  }

  deleteNotification(notification: ExtendedNotification): void {
    if (notification.id) {
      this.notificationService.deleteNotification(notification.id).subscribe({
      next: () => {
        // Remove the notification from the local array
        this.notifications = this.notifications.filter(n => n.id !== notification.id);
      },
      error: (error: any) => {
        console.error('Error deleting notification:', error);
      }
    });
    }
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'Unknown date';

    const notificationDate = new Date(date);
    const now = new Date();

    // Check if the date is valid
    if (isNaN(notificationDate.getTime())) {
      return 'Invalid date';
    }

    // Calculate the difference in milliseconds
    const diffMs = now.getTime() - notificationDate.getTime();

    // Convert to minutes, hours, days
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);

    if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      // Format as date
      return notificationDate.toLocaleDateString();
    }
  }

  getNotificationIcon(type: string | undefined): string {
    if (!type) return 'notifications';

    switch (type) {
      case 'AD_REPORT':
        return 'report';
      case 'AD_WARNING':
        return 'warning';
      case 'AD_DELETION':
        return 'delete';
      case 'AD_LIKE':
        return 'favorite';
      default:
        return 'notifications';
    }
  }

  getNotificationColor(type: string | undefined): string {
    if (!type) return '';

    switch (type) {
      case 'AD_REPORT':
        return 'primary';
      case 'AD_WARNING':
        return 'accent';
      case 'AD_DELETION':
        return 'warn';
      case 'AD_LIKE':
        return 'accent'; // Using accent color (usually pink/purple) for likes
      default:
        return '';
    }
  }

  private async getCurrentUserId(): Promise<string> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();

      if (isLoggedIn) {
        // Get the user info from Keycloak token
        const token = this.authService.getToken();
        if (token) {
          // Extract user ID from the token if possible
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            if (tokenData && tokenData.sub) {
              console.log('Got user ID from token:', tokenData.sub);
              return tokenData.sub;
            }
          } catch (e) {
            console.warn('Error parsing token:', e);
          }
        }

        // Try to get user profile
        const userProfile = await this.authService.getUserProfile();
        if (userProfile && userProfile.email) {
          console.log('Using email as user ID:', userProfile.email);
          return userProfile.email;
        }

        // If we couldn't get the user ID from Keycloak, use the default
        console.log('Could not get user ID from Keycloak, using default');
        return 'test-user-id';
      } else {
        console.log('User is not logged in');
        return '';
      }
    } catch (error) {
      // If there's an error or the auth service is not available, return a default value
      console.warn('Could not get user ID from auth service, using default');
      return 'test-user-id';
    }
  }
}
