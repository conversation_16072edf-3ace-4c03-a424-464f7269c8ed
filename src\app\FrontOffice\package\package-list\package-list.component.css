/* ✅ General Container */
.container {
    margin-top: 2rem;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
  
  /* ✅ Card Styling */
  .card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    padding: 1.5rem;
  }
  
  /* ✅ Table Styling */
  .table {
    background: #ffffff;
    border-radius: 10px;
    overflow: hidden;
  }
  
  .table th {
    background: #2c3e50;
    color: white;
    text-transform: uppercase;
    text-align: center;
  }
  
  .table td {
    padding: 12px;
    text-align: center;
  }
  
  /* ✅ Delete Button */
  .btn-danger {
    background-color: #dc3545;
    border: none;
  }
  
  .btn-danger:hover {
    background-color: #c82333;
  }
  
  /* ✅ Responsive Design */
  @media (max-width: 768px) {
    .table {
      font-size: 12px;
    }
  
    .btn {
      font-size: 12px;
      padding: 5px;
    }
  }
  