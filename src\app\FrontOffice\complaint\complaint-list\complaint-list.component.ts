import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Complaint, ComplaintControllerService } from 'src/app/openapi';
import { FooterFrontComponent } from '../../footer-front/footer-front.component';
import { HeaderFrontComponent } from '../../header-front/header-front.component';
import { Router, RouterLink } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ComplaintDetailsDialogComponent } from '../complaint-details-dialog/complaint-details-dialog.component';
import { ComplaintEditDialogComponent } from '../complaint-edit-dialog/complaint-edit-dialog.component';
import { AuthService } from 'src/app/services/auth.service';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-complaint-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FooterFrontComponent,
    HeaderFrontComponent,
    RouterLink,
    MatSnackBarModule,
    MatIconModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatDialogModule
  ],
  templateUrl: './complaint-list.component.html',
  styleUrl: './complaint-list.component.css'
})
export class ComplaintListComponent implements OnInit {
  complaints: Complaint[] = [];
  filteredComplaints: Complaint[] = [];
  isLoading: boolean = true;
  searchTerm: string = '';
  selectedStatus: string = 'ALL';
  selectedCategory: string = 'ALL';

  // Status options
  statusOptions = [
    { value: 'ALL', label: 'All Statuses' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'OPENED', label: 'Opened' },
    { value: 'TREATED', label: 'Treated' }
  ];

  // Category options (will be populated from the data)
  categoryOptions: { value: string, label: string }[] = [
    { value: 'ALL', label: 'All Categories' }
  ];

  // View mode (grid or list)
  viewMode: 'grid' | 'list' = 'grid';

  // Store the current user ID
  private currentUserId: string | null = null;
  // Store the user roles
  private userRoles: string[] = [];
  // Flag to indicate if the user is an admin
  isAdmin: boolean = false;

  constructor(
    private complaintService: ComplaintControllerService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private authService: AuthService,
    private http: HttpClient
  ) {}

  async ngOnInit(): Promise<void> {
    // Get user information
    await this.loadUserInfo();

    // Get complaints
    this.getAllComplaints();
  }

  /**
   * Load user information (ID and roles)
   */
  async loadUserInfo(): Promise<void> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();

      if (isLoggedIn) {
        // Get the user ID
        this.currentUserId = await this.getCurrentUserId();

        // Get user roles
        const token = this.authService.getToken();
        if (token) {
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            if (tokenData && tokenData.realm_access && tokenData.realm_access.roles) {
              this.userRoles = tokenData.realm_access.roles;
              console.log('User roles:', this.userRoles);

              // Check if the user is an admin
              this.isAdmin = this.userRoles.some(role => role.toLowerCase() === 'admin');
              console.log('Is admin:', this.isAdmin);
            }
          } catch (e) {
            console.warn('Error parsing token for roles:', e);
          }
        }
      } else {
        this.currentUserId = null;
        this.userRoles = [];
        this.isAdmin = false;
      }
    } catch (error) {
      console.error('Error loading user info:', error);
    }
  }

  /**
   * Get the current user ID from the auth service
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();

      if (isLoggedIn) {
        // Get the user info from Keycloak token
        const token = this.authService.getToken();
        if (token) {
          // Extract user ID from the token if possible
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            if (tokenData && tokenData.sub) {
              return tokenData.sub;
            }
          } catch (e) {
            console.warn('Error parsing token:', e);
          }
        }

        // Try to get user profile
        const userProfile = await this.authService.getUserProfile();
        if (userProfile && userProfile.email) {
          return userProfile.email;
        }

        // If we couldn't get the user ID from Keycloak, use a default
        return 'test-user-id';
      } else {
        return null;
      }
    } catch (error) {
      console.warn('Could not get user ID from auth service, using default');
      return null;
    }
  }

  getAllComplaints(): void {
    this.isLoading = true;

    // Check if we have a valid user ID
    if (!this.currentUserId && !this.isAdmin) {
      console.warn("No user ID available, attempting to reload user info");
      this.loadUserInfo().then(() => {
        this.fetchComplaints();
      }).catch(error => {
        console.error("Failed to load user info:", error);
        this.isLoading = false;
        this.showNotification('Authentication error. Please log in again.', 'error');
      });
    } else {
      this.fetchComplaints();
    }
  }

  // Separate method to fetch complaints
  fetchComplaints(): void {
    console.log("Fetching complaints. isAdmin:", this.isAdmin, "userId:", this.currentUserId);

    // Try to use the myComplaints endpoint directly if available
    try {
      // Check if the myComplaints method exists in the service
      if (typeof (this.complaintService as any).getMyComplaints === 'function') {
        console.log("Using getMyComplaints endpoint");
        this.fetchMyComplaints();
        return;
      }
    } catch (error) {
      console.warn("getMyComplaints method not available, falling back to custom implementation");
    }

    // Custom implementation using HTTP client to call the endpoint directly
    if (!this.isAdmin && this.currentUserId) {
      console.log("Using custom implementation to fetch user complaints");
      this.fetchUserComplaintsDirectly();
      return;
    }

    // Fallback to listComplaints for admins or if other methods fail
    console.log("Using listComplaints endpoint with client-side filtering");
    this.fetchComplaintsWithClientSideFiltering();
  }

  deleteComplaint(id: string, event: Event): void {
    event.stopPropagation(); // Prevent card click event

    if (confirm("Are you sure you want to delete this complaint?")) {
      this.complaintService.deleteComplaint(id).subscribe({
        next: () => {
          this.showNotification('Complaint deleted successfully', 'success');
          this.getAllComplaints();
        },
        error: (err: any) => {
          console.error("Error during deletion", err);
          this.showNotification('Failed to delete complaint', 'error');
        }
      });
    }
  }

  // ✅ Open Complaint Details Dialog
  viewComplaint(id: string): void {
    // Open the complaint details in a dialog instead of navigating to a new page
    const dialogRef = this.dialog.open(ComplaintDetailsDialogComponent, {
      width: '700px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      panelClass: 'styled-dialog-container',
      disableClose: false,
      backdropClass: 'styled-backdrop',
      data: { complaintId: id },
      enterAnimationDuration: '300ms',
      exitAnimationDuration: '200ms'
    });

    // Add a class to the body to indicate a dialog is open (for additional styling)
    document.body.classList.add('dialog-open');

    // Remove the class when the dialog is closed
    dialogRef.afterClosed().subscribe(() => {
      document.body.classList.remove('dialog-open');
    });
  }

  // ✅ Open Complaint Edit Dialog
  editComplaint(id: string, event: Event): void {
    event.stopPropagation(); // Prevent card click event

    // Open the complaint edit dialog instead of navigating to a new page
    const dialogRef = this.dialog.open(ComplaintEditDialogComponent, {
      width: '700px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      panelClass: 'styled-dialog-container',
      disableClose: false,
      backdropClass: 'styled-backdrop',
      data: { complaintId: id },
      enterAnimationDuration: '300ms',
      exitAnimationDuration: '200ms'
    });

    // Add a class to the body to indicate a dialog is open (for additional styling)
    document.body.classList.add('dialog-open');

    // Remove the class when the dialog is closed
    dialogRef.afterClosed().subscribe(result => {
      document.body.classList.remove('dialog-open');

      // If the complaint was updated successfully, refresh the list
      if (result === true) {
        this.showNotification('Complaint updated successfully', 'success');
        this.getAllComplaints();
      }
    });
  }

  // ✅ Apply filters based on search term, status, and category
  applyFilters(): void {
    this.filteredComplaints = this.complaints.filter(complaint => {
      // Filter by search term
      const matchesSearch = !this.searchTerm ||
        (complaint.title && complaint.title.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
        (complaint.description && complaint.description.toLowerCase().includes(this.searchTerm.toLowerCase()));

      // Filter by status
      const matchesStatus = this.selectedStatus === 'ALL' ||
        complaint.status === this.selectedStatus;

      // Filter by category
      const matchesCategory = this.selectedCategory === 'ALL' ||
        complaint.category === this.selectedCategory;

      return matchesSearch && matchesStatus && matchesCategory;
    });
  }

  // ✅ Reset all filters
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = 'ALL';
    this.selectedCategory = 'ALL';
    this.applyFilters();
  }

  // ✅ Toggle view mode between grid and list
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid';
  }

  // ✅ Format category name for display
  formatCategoryName(category: string): string {
    if (!category) return 'Unknown';

    // Replace underscores with spaces and capitalize each word
    return category
      .replace(/_/g, ' ')
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // ✅ Get appropriate icon for complaint status
  getStatusIcon(status: string): string {
    switch (status) {
      case 'PENDING': return 'hourglass_empty';
      case 'OPENED': return 'visibility';
      case 'TREATED': return 'check_circle';
      default: return 'help';
    }
  }

  // ✅ Get appropriate color class for complaint status
  getStatusColorClass(status: string): string {
    switch (status) {
      case 'PENDING': return 'status-pending';
      case 'OPENED': return 'status-opened';
      case 'TREATED': return 'status-treated';
      default: return '';
    }
  }

  // ✅ Truncate text with ellipsis if it exceeds the specified length
  truncateText(text: string | undefined, maxLength: number): string {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  // ✅ Get current date for display
  getCurrentDate(): string {
    // Return current date in a nice format
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return new Date().toLocaleDateString('en-US', options);
  }

  // ✅ Show notification using MatSnackBar
  showNotification(message: string, type: 'success' | 'error' | 'info'): void {
    const panelClass = {
      'success': ['success-snackbar'],
      'error': ['error-snackbar'],
      'info': ['info-snackbar']
    }[type];

    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass
    });
  }

  // Method to fetch complaints using the getMyComplaints endpoint
  fetchMyComplaints(): void {
    (this.complaintService as any).getMyComplaints().subscribe({
      next: async (response: any) => {
        try {
          if (response instanceof Blob) {
            const text = await response.text();
            this.complaints = JSON.parse(text);
          } else {
            this.complaints = response;
          }
          console.log("My complaints loaded:", this.complaints);

          // Extract unique categories from complaints
          const categories = new Set<string>();
          this.complaints.forEach(complaint => {
            if (complaint.category) {
              categories.add(complaint.category.toString());
            }
          });

          // Update category options
          this.categoryOptions = [
            { value: 'ALL', label: 'All Categories' },
            ...Array.from(categories).map(cat => ({
              value: cat,
              label: this.formatCategoryName(cat)
            }))
          ];

          // Apply initial filtering
          this.applyFilters();
        } catch (error) {
          console.error("Error processing complaints data:", error);
          this.showNotification('Error processing complaint data', 'error');
        } finally {
          this.isLoading = false;
        }
      },
      error: (err: any) => {
        console.error("Error fetching my complaints", err);
        this.isLoading = false;
        this.showNotification('Failed to load your complaints', 'error');

        // Fallback to listComplaints if getMyComplaints fails
        console.log("Falling back to listComplaints");
        this.fetchComplaintsWithClientSideFiltering();
      }
    });
  }

  // Method to fetch user complaints directly using HTTP client
  fetchUserComplaintsDirectly(): void {
    const apiUrl = 'http://localhost:8089/speedygo/complaint/myComplaints';

    this.http.get<Complaint[]>(apiUrl, {
      headers: {
        'Authorization': `Bearer ${this.authService.getToken()}`
      }
    }).subscribe({
      next: (response) => {
        this.complaints = response;
        console.log("User complaints loaded directly:", this.complaints);

        // Extract unique categories from complaints
        const categories = new Set<string>();
        this.complaints.forEach(complaint => {
          if (complaint.category) {
            categories.add(complaint.category.toString());
          }
        });

        // Update category options
        this.categoryOptions = [
          { value: 'ALL', label: 'All Categories' },
          ...Array.from(categories).map(cat => ({
            value: cat,
            label: this.formatCategoryName(cat)
          }))
        ];

        // Apply initial filtering
        this.applyFilters();
        this.isLoading = false;
      },
      error: (err: any) => {
        console.error("Error fetching user complaints directly", err);
        this.isLoading = false;
        this.showNotification('Failed to load your complaints', 'error');

        // Fallback to listComplaints if direct HTTP request fails
        console.log("Falling back to listComplaints");
        this.fetchComplaintsWithClientSideFiltering();
      }
    });
  }

  // Fallback method using listComplaints with client-side filtering
  fetchComplaintsWithClientSideFiltering(): void {
    const observable = this.complaintService.listComplaints();

    observable.subscribe({
      next: async (response) => {
        try {
          if (response instanceof Blob) {
            const text = await response.text();
            this.complaints = JSON.parse(text);
          } else {
            this.complaints = response;
          }

          // Filter complaints by user ID if exists
          if (!this.isAdmin && this.currentUserId) {
            console.log("Filtering complaints for user ID:", this.currentUserId);

            // Log all user IDs in the complaints for debugging
            const userIds = this.complaints.map(c => c.userId).filter(id => id);
            console.log("All user IDs in complaints:", [...new Set(userIds)]);

            const originalCount = this.complaints.length;
            this.complaints = this.complaints.filter(complaint => {
              const isOwner = complaint.userId === this.currentUserId;
              if (isOwner) {
                console.log("Found complaint owned by current user:", complaint);
              }
              return isOwner;
            });
            console.log(`Filtered complaints: ${this.complaints.length} (from ${originalCount})`);
          }

          // Extract unique categories from complaints
          const categories = new Set<string>();
          this.complaints.forEach(complaint => {
            if (complaint.category) {
              categories.add(complaint.category.toString());
            }
          });

          // Update category options
          this.categoryOptions = [
            { value: 'ALL', label: 'All Categories' },
            ...Array.from(categories).map(cat => ({
              value: cat,
              label: this.formatCategoryName(cat)
            }))
          ];

          // Apply initial filtering
          this.applyFilters();
        } catch (error) {
          console.error("Error processing complaints data:", error);
          this.showNotification('Error processing complaint data', 'error');
        } finally {
          this.isLoading = false;
        }
      },
      error: (err: any) => {
        console.error("Error fetching complaints", err);
        this.isLoading = false;
        this.showNotification('Failed to load complaints', 'error');
      }
    });
  }
}
