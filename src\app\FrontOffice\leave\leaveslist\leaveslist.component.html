<app-header-front></app-header-front>

<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center">
    <h2 class="text-primary">Leave Requests</h2>
    <a class="btn btn-success btn-lg" [routerLink]="['/leaveadd']">
      <i class="fas fa-plus"></i> Add Leave
    </a>
  </div>

  <div class="table-responsive mt-4">
    <table class="table table-bordered table-hover shadow-sm">
      <thead class="table-dark">
        <tr>
          <th>Start Date</th>
          <th>End Date</th>
          <th>Reason</th>
          <th>Status</th>
          <th class="text-center">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let leave of leaves">
          <td>{{ leave.startDate | date:'dd/MM/yyyy' }}</td>
          <td>{{ leave.endDate | date:'dd/MM/yyyy' }}</td>
          <td>{{ leave.reason }}</td>
          <td>
            <span 
              class="badge" 
              [ngClass]="{
                'bg-warning text-dark': leave.status === 'PENDING',
                'bg-success': leave.status === 'APPROVED',
                'bg-danger': leave.status === 'REJECTED'
              }">
              {{ leave.status }}
            </span>
          </td>
          <td class="text-center">
            <!-- Action Buttons: Only visible if status is PENDING -->
            <button *ngIf="leave.status === 'PENDING'" class="btn btn-warning btn-sm me-2" (click)="editLeave(leave.id!)">
              <i class="fas fa-edit"></i> Edit
            </button>
            <button *ngIf="leave.status === 'PENDING'" class="btn btn-danger btn-sm" (click)="deleteLeave(leave.id!)">
              <i class="fas fa-trash"></i> Delete
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<app-footer-front></app-footer-front>
