.ads-section {
    background-color: #f8f9fa; /* <PERSON>uleur claire propre */
    padding: 3rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 16px;
  }
  .ads-carousel {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    scroll-behavior: smooth; /* ✅ smooth scroll natif CSS */
  }

  .ads-section {
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
  }

  .ads-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
  }

  .ads-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
  }

  .ads-carousel-container {
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .ads-carousel-container::-webkit-scrollbar {
    display: none;
  }

  .ads-carousel {
    display: flex;
    flex-direction: row;
    gap: 1.5rem;
    transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    padding: 10px 5px;
  }

  .ad-card {
    position: relative;
    width: 350px;
    height: 480px;
    overflow: hidden;
    border-radius: 8px;
    flex: 0 0 auto;
    scroll-snap-align: start;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    background-color: #fff;
    border: none;
  }


  .ad-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
    z-index: 1;
  }

  .ad-card:hover .ad-image {
    transform: scale(1.05);
  }

  /* Info Bar with Title and Creation Date */
  .ad-info-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0));
    color: white;
    padding: 60px 20px 20px;
    z-index: 2;
    transition: all 0.3s ease;
  }

  .ad-card:hover .ad-info-bar {
    padding-bottom: 25px;
  }

  .ad-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .ad-dates {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    opacity: 0.9;
  }

  .date-item {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 3px 8px;
    border-radius: 12px;
  }

  .date-icon {
    font-size: 14px;
    height: 14px;
    width: 14px;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9);
  }

  .date-value {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
  }

  .ad-price-badge {
    background-color: #0d6efd;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
    z-index: 1;
    opacity: 1;
    transition: all 0.3s ease;
    position: absolute;
    top: 15px;
    right: 15px;
  }

  /* Style for N/A price */
  .price-na {
    background-color: rgba(108, 117, 125, 0.8) !important;
    color: white !important;
    font-style: italic;
  }

  .ad-overlay {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
    padding: 2rem;
    text-align: center;
  }

  .ad-card:hover .ad-image {
    filter: brightness(0.5);
  }

  .ad-card:hover .ad-overlay {
    opacity: 1;
  }

  /* Description Box */
  .description-box {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 280px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease 0.1s;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .description-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .description-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    color: rgba(255, 255, 255, 0.9);
  }

  .description-label {
    font-weight: 600;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.95);
  }

  .description-content {
    font-size: 0.85rem;
    line-height: 1.5;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    max-height: 120px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.2);
    padding-right: 5px;
  }

  .description-content::-webkit-scrollbar {
    width: 6px;
  }

  .description-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  .description-content::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }

  /* Overlay Dates Section */
  .overlay-dates {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 1.5rem;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 12px 15px;
    border-radius: 8px;
    width: 100%;
    max-width: 280px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease 0.15s;
  }

  .overlay-date-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: white;
  }

  .overlay-date-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    color: rgba(255, 255, 255, 0.8);
  }

  .overlay-date-label {
    font-weight: 600;
    width: 60px;
    color: rgba(255, 255, 255, 0.9);
  }

  .overlay-date-value {
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
  }

  .overlay-buttons {
    display: flex;
    gap: 10px;
    flex-direction: column;
    width: 100%;
    max-width: 200px;
  }

  .overlay-buttons button {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease 0.2s;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .overlay-buttons button:nth-child(2) {
    transition-delay: 0.3s;
  }

  .overlay-buttons button mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
  }

  .ad-card:hover .description-box,
  .ad-card:hover .overlay-dates,
  .ad-card:hover .overlay-buttons button {
    opacity: 1;
    transform: translateY(0);
  }

  mat-chip {
    border-radius: 4px;
    font-size: 0.75rem;
    padding: 4px 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
  }

  mat-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }




  .carpool-chip {
    background-color: #198754 !important; /* green */
    color: white;
  }

  .fastpost-chip {
    background-color: #dc3545 !important; /* red */
    color: white;
  }

  .product-chip {
    background-color: #0d6efd !important; /* blue */
    color: white;
  }

  .other-chip {
    background-color: #6c757d !important; /* gray */
    color: white;
  }

  .ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  /* Badge hover effects */
  .ad-card:hover .ad-price-badge {
    opacity: 1;
  }



  .ads-carousel-wrapper {
    position: relative;
  }

  .arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
    background-color: white !important;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    opacity: 0; /* invisible by default */
    transition: all 0.3s ease;
    pointer-events: none; /* prevents click unless visible */
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .arrow mat-icon {
    color: #0d6efd !important;
    transition: all 0.3s ease;
  }

  .ads-carousel-wrapper:hover .arrow {
    opacity: 1;
    pointer-events: auto;
  }

  .arrow:hover {
    background-color: #f8f9fa !important;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }

  .left-arrow {
    left: -20px;
  }

  .right-arrow {
    right: -20px;
  }
/* Masquer sur mobile */
@media (max-width: 768px) {
    .arrow {
      display: none !important;
    }
  }

  .ads-carousel-container {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* IE 10+ */
  }

  .ads-carousel-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .ad-card,
.ad-image {
  border-radius: 16px;
}

.ad-title-bar {
    background: rgba(0, 0, 0, 0.6); /* plus opaque */
    font-size: 1.1rem;
    font-family: 'Poppins', sans-serif; /* une police clean */
  }

  .ad-category-bottom {
    position: absolute;
    top: 60px;
    right: 15px;
    z-index: 1;
    opacity: 1;
    transition: all 0.3s ease;
  }

  .ad-card:hover .ad-category-bottom {
    top: 55px;
  }

  /* This hover effect is already defined above */

  .text-center.mt-4 {
  margin-top: 1.5rem !important;
}

.ad-card {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
  animation-delay: calc(var(--i) * 0.1s);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom Dialog Styling */
:host ::ng-deep .stylish-dialog {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25) !important;
  animation: dialogFadeIn 0.3s ease-out !important;
}

:host ::ng-deep .stylish-backdrop {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px) !important;
  transition: all 0.3s ease !important;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dialog open body style */
:host ::ng-deep body.dialog-open {
  overflow: hidden !important;
}

/* Make sure the dialog content is styled nicely */
:host ::ng-deep .ad-detail-dialog-container .mat-mdc-dialog-surface {
  border-radius: 16px !important;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa) !important;
}

:host ::ng-deep .ad-detail-dialog-container .dialog-header {
  background: linear-gradient(135deg, #4361ee, #3a0ca3) !important;
  color: white !important;
  padding: 16px 24px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

:host ::ng-deep .ad-detail-dialog-container .close-button {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .ad-detail-dialog-container .close-button:hover {
  background-color: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1) !important;
}

.pagination-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 1rem;
  }

  .dot {
    width: 10px;
    height: 10px;
    background-color: #ccc;
    border-radius: 50%;
    transition: background-color 0.3s ease;
  }

  .dot.active {
    background-color: #4caf50;
  }

  .arrow:disabled {
    opacity: 0.3;
    pointer-events: none;
  }

  .pagination-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 1.5rem;
  }

  .dot {
    width: 8px;
    height: 8px;
    background-color: #dee2e6;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: scale(1);
  }

  .dot:hover {
    background-color: #adb5bd;
  }

  .dot.active {
    background-color: #0d6efd;
    transform: scale(1.2);
  }

  /* FAB Container */
  .fab-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
  }

  /* Main FAB Button */
  .fab-add-ad {
    position: relative;
    z-index: 1002;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    background-color: #0d6efd !important;
  }

  .fab-add-ad:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(13, 110, 253, 0.3);
  }

  .fab-add-ad mat-icon {
    font-size: 24px;
  }

  /* FAB Menu */
  .fab-menu {
    position: absolute;
    bottom: 70px;
    right: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 16px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
  }

  .fab-menu-open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  /* FAB Menu Items */
  .fab-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: white !important;
    color: #333 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .fab-menu-item:first-child {
    background-color: #4CAF50 !important;
    color: white !important;
  }

  .fab-menu-item:last-child {
    background-color: #f44336 !important;
    color: white !important;
  }

  .fab-menu-item:hover {
    transform: scale(1.05);
  }

  /* FAB Menu Labels */
  .fab-menu-label {
    position: absolute;
    right: 50px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
  }

  .fab-menu-item:hover .fab-menu-label {
    opacity: 1;
    visibility: visible;
  }

  /* Backdrop */
  .fab-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    z-index: 1001;
  }

/* Flag button */
.flag-button {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 10;
  background-color: rgba(220, 53, 69, 0.8);
  width: 32px;
  height: 32px;
  line-height: 32px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  opacity: 0.9;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 0;
}

.flag-button:hover {
  background-color: rgba(220, 53, 69, 1);
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
  opacity: 1;
}

.flag-button i {
  font-size: 16px;
  line-height: 16px;
}

/* Like indicator */
.like-indicator {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10; /* Increased z-index to ensure it's above other elements */
  background-color: rgba(255, 255, 255, 0.95);
  color: #dc3545;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid rgba(220, 53, 69, 0.2);
  cursor: pointer;
  user-select: none;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  pointer-events: auto; /* Ensure it receives click events */
}

.like-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 1);
}

.like-indicator:active {
  transform: scale(0.95);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Liked state */
.like-indicator.liked {
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
  border: 1px solid rgba(220, 53, 69, 0.8);
  box-shadow: 0 3px 10px rgba(220, 53, 69, 0.4);
}

.like-indicator.liked:hover {
  background-color: rgba(220, 53, 69, 1);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.5);
}

.like-indicator.liked::after {
  content: '';
  position: absolute;
  top: -5px;
  right: -5px;
  width: 12px;
  height: 12px;
  background-color: #4CAF50;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.like-indicator mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  color: #dc3545;
}

.like-indicator mat-icon {
  color: #dc3545;
  transition: all 0.3s ease;
}

.like-indicator:hover mat-icon {
  transform: scale(1.1);
}

.like-indicator.liked mat-icon {
  color: white;
  animation: heartBeat 0.5s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  15% { transform: scale(1.4); }
  30% { transform: scale(0.9); }
  45% { transform: scale(1.2); }
  60% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* Like count number animation */
.like-count-number {
  transition: all 0.3s ease;
}

.like-indicator:active .like-count-number {
  animation: countChange 0.5s ease-in-out;
}

@keyframes countChange {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.5); opacity: 0.5; }
  100% { transform: scale(1); opacity: 1; }
}

/* Snackbar styles */
::ng-deep .success-snackbar {
  background-color: #4CAF50 !important;
  color: white !important;
  font-weight: 500 !important;
}

::ng-deep .info-snackbar {
  background-color: #2196F3 !important;
  color: white !important;
  font-weight: 500 !important;
}

::ng-deep .error-snackbar {
  background-color: #F44336 !important;
  color: white !important;
  font-weight: 500 !important;
}

::ng-deep .mat-simple-snackbar-action {
  color: white !important;
}

/* Direct Action Buttons */
.direct-action-buttons {
  position: fixed;
  bottom: 25px;
  right: 25px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1000;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-weight: 500;
  font-size: 0.85rem;
  text-decoration: none;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  gap: 8px;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
  z-index: -1;
}

.action-button i {
  font-size: 14px;
}

.action-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-button:hover::before {
  left: 100%;
}

.action-button:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.ads-button {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
}

.complaints-button {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
}

/* Button glow effects */
.button-glow {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.5s ease;
  z-index: -1;
  pointer-events: none;
}

.ads-glow {
  background: radial-gradient(circle, rgba(67, 97, 238, 0.6) 0%, rgba(58, 12, 163, 0) 70%);
  right: -10px;
  top: -15px;
}

.complaints-glow {
  background: radial-gradient(circle, rgba(255, 82, 82, 0.6) 0%, rgba(211, 47, 47, 0) 70%);
  right: -10px;
  top: -15px;
}

.action-button:hover .button-glow {
  opacity: 1;
  transform: scale(1.5);
}

/* Add subtle animation to icons */
.action-button:hover i {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Quick Navigation Section */
.py-5.bg-light {
  background-color: #f8f9fa !important;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.py-5.bg-light .card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.py-5.bg-light .card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

.py-5.bg-light .card i {
  transition: all 0.3s ease;
}

.py-5.bg-light .card:hover i {
  transform: scale(1.2);
}

.py-5.bg-light .btn {
  transition: all 0.3s ease;
}

.py-5.bg-light .card:hover .btn {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}