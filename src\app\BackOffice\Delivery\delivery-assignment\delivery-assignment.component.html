<app-navbar-back></app-navbar-back>
<app-sidebar-back></app-sidebar-back>
<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <!-- The offset adds left space -->

  <h2 class="mb-3">Assign Delivery</h2>
    <div class="offset-md-2 col-md-10">
      <div class="container">
      <form [formGroup]="deliveryForm" (ngSubmit)="onSubmit()">
        <!-- Input for driverId -->
        <div class="form-group mb-3">
          <label for="driverId">Driver ID</label>
          <input id="driverId" type="text" formControlName="driverId" class="form-control">
        </div>
        <div class="form-group mb-3">
          <label for="userId">user ID</label>
          <input id="userId" type="text" formControlName="userId" class="form-control">
        </div>

        <div class="form-group mb-3">
          <label for="estimatedDeliveryTime">Estimated Delivery Time</label>
          <input id="estimatedDeliveryTime" type="datetime-local" formControlName="estimatedDeliveryTime" class="form-control">
        </div>
        <div class="form-group mb-3">
          <label for="deliveryStatus">Delivery Status</label>
          <select id="deliveryStatus" formControlName="deliveryStatus" class="form-control">
            <option value="" disabled selected>-- Select Vehicle add status --</option>
            <!-- Loop over the specific statuses you want to display -->
            <option *ngFor="let status of deliveryStatuses" [value]="status">
              {{ status }}
            </option>
          </select>
        </div>

        <div class="form-group mb-3">
          <label for="pamentStatus">Payment Status</label>
          <select id="pamentStatus" formControlName="pamentStatus" class="form-control">
            <option value="" disabled selected>-- Select Vehicle add status --</option>
            <option *ngFor="let status of pamentStatuses" [value]="status">{{ status }}</option>
          </select>
        </div>

        <!-- Vehicle Status Dropdown -->
        <div class="form-group mb-3">
          <label for="status">status</label>
          <select id="status" formControlName="status" class="form-control">
            <option value="" disabled selected>-- Select Vehicle add status --</option>
            <option *ngFor="let add_status of status" [value]="add_status" >
              {{ add_status }}
            </option>
          </select>
        </div>
        <button type="submit" class="btn btn-primary">Assign Delivery</button>
      </form>
    </div>
  </div>
</div>
</div>
