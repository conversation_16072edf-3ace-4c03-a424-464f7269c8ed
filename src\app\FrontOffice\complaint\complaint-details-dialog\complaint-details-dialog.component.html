<div class="complaint-details-dialog">
  <h2 mat-dialog-title>
    <i class="fas fa-exclamation-circle me-2"></i>Complaint Details
  </h2>

  <mat-dialog-content>
    <div *ngIf="loading" class="text-center p-4">
      <mat-spinner diameter="40" class="mx-auto"></mat-spinner>
      <p class="mt-3">Loading complaint details...</p>
    </div>

    <div *ngIf="error" class="alert alert-danger">
      <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
    </div>

    <div *ngIf="!loading && !error" class="complaint-content">
      <!-- Complaint Information -->
      <div class="row">
        <!-- Title -->
        <div class="col-md-12 mb-3">
          <label class="fw-bold text-secondary"><i class="fas fa-file-alt"></i> Title:</label>
          <p class="text-dark border rounded p-2 bg-light">{{ complaint.title }}</p>
        </div>

        <!-- Description -->
        <div class="col-md-12 mb-3">
          <label class="fw-bold text-secondary"><i class="fas fa-align-left"></i> Description:</label>
          <p class="text-muted border rounded p-2 bg-light">{{ complaint.description }}</p>
        </div>

        <!-- Category -->
        <div class="col-md-6 mb-3">
          <label class="fw-bold text-secondary"><i class="fas fa-tags"></i> Category:</label>
          <p class="text-dark border rounded p-2 bg-light">{{ formatCategoryName(complaint.category) }}</p>
        </div>

        <!-- Status -->
        <div class="col-md-6 mb-3">
          <label class="fw-bold text-secondary"><i class="fas fa-info-circle"></i> Status:</label>
          <p class="border rounded p-2 bg-light">
            <span
              class="badge"
              [ngClass]="{
                'bg-warning text-dark': complaint.status === 'PENDING',
                'bg-primary': complaint.status === 'OPENED',
                'bg-success': complaint.status === 'TREATED'
              }">
              {{ complaint.status }}
            </span>
          </p>
        </div>

        <!-- Priority (if available) -->
        <div class="col-md-6 mb-3" *ngIf="complaint.priority">
          <label class="fw-bold text-secondary"><i class="fas fa-thermometer-half"></i> Priority:</label>
          <p class="border rounded p-2 bg-light">
            <span
              class="badge"
              [ngClass]="{
                'bg-success': complaint.priority === 'LOW',
                'bg-warning text-dark': complaint.priority === 'MEDIUM',
                'bg-danger': complaint.priority === 'HIGH'
              }">
              {{ complaint.priority }}
            </span>
          </p>
        </div>

        <!-- Creation Date (if available) -->
        <div class="col-md-6 mb-3" *ngIf="complaint.createdAt">
          <label class="fw-bold text-secondary"><i class="fas fa-calendar-alt"></i> Created:</label>
          <p class="text-dark border rounded p-2 bg-light">{{ formatDate(complaint.createdAt) }}</p>
        </div>

        <!-- Contact Information Section (if available) -->
        <div class="col-12 mt-2 mb-3" *ngIf="hasContactInfo()">
          <h5 class="section-title">
            <i class="fas fa-address-card me-2"></i>Contact Information
          </h5>

          <!-- Contact Preference -->
          <div class="col-md-12 mb-3" *ngIf="complaint.contactPreference">
            <label class="fw-bold text-secondary"><i class="fas fa-comment-dots"></i> Contact Preference:</label>
            <p class="text-dark border rounded p-2 bg-light">{{ complaint.contactPreference }}</p>
          </div>

          <!-- Email (if provided) -->
          <div class="col-md-6 mb-3" *ngIf="complaint.contactEmail">
            <label class="fw-bold text-secondary"><i class="fas fa-envelope"></i> Email:</label>
            <p class="text-dark border rounded p-2 bg-light">{{ complaint.contactEmail }}</p>
          </div>

          <!-- Phone (if provided) -->
          <div class="col-md-6 mb-3" *ngIf="complaint.contactPhone">
            <label class="fw-bold text-secondary"><i class="fas fa-phone"></i> Phone:</label>
            <p class="text-dark border rounded p-2 bg-light">{{ complaint.contactPhone }}</p>
          </div>
        </div>

        <!-- Evidence Files Section (if available) -->
        <div class="col-12 mt-2" *ngIf="complaint.evidence && complaint.evidence.length > 0">
          <h5 class="section-title">
            <i class="fas fa-file-upload me-2"></i>Evidence Files
          </h5>

          <div class="evidence-files">
            <div class="row">
              <div *ngFor="let file of complaint.evidence; let i = index" class="col-md-4 mb-3">
                <div class="evidence-item">
                  <!-- Image preview for image files -->
                  <div *ngIf="isImageFile(file)" class="evidence-image">
                    <img [src]="file" alt="Evidence Image" class="img-fluid rounded">
                    <button class="btn btn-sm btn-primary mt-2" (click)="viewFullImage(file)">
                      <i class="fas fa-search-plus me-1"></i>View Full
                    </button>
                  </div>

                  <!-- Icon for non-image files -->
                  <div *ngIf="!isImageFile(file)" class="evidence-file">
                    <i class="fas fa-file fa-3x text-primary"></i>
                    <p class="mt-2">Evidence File {{ i + 1 }}</p>
                    <button class="btn btn-sm btn-primary mt-1" (click)="downloadFile(file, i)">
                      <i class="fas fa-download me-1"></i>Download
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button class="btn btn-secondary" (click)="close()">
      <i class="fas fa-times me-2"></i>Close
    </button>
  </mat-dialog-actions>
</div>
