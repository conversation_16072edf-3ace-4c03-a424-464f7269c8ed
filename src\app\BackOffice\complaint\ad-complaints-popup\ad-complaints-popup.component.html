<div class="ad-complaints-popup">
  <h2 mat-dialog-title>
    <span *ngIf="ad">
      <i class="fas fa-flag me-2"></i>Reports for Ad: {{ ad.title }}
    </span>
    <span *ngIf="!ad">
      <i class="fas fa-flag me-2"></i>Ad Reports
    </span>
    <span class="badge bg-danger ms-2">{{ data.reportCount }} Reports</span>
  </h2>

  <mat-dialog-content>
    <div *ngIf="loading" class="text-center p-4">
      <mat-spinner diameter="40" class="mx-auto"></mat-spinner>
      <p class="mt-3">Loading reports...</p>
    </div>

    <div *ngIf="error" class="alert alert-danger">
      <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
    </div>

    <div *ngIf="!loading && !error && complaints.length === 0" class="text-center p-4">
      <i class="fas fa-search fa-3x text-muted mb-3"></i>
      <p>No reports found for this ad.</p>
    </div>

    <div *ngIf="!loading && !error && complaints.length > 0" class="complaints-list">
      <div *ngFor="let complaint of complaints; let i = index" class="complaint-item">
        <div class="d-flex justify-content-between align-items-start">
          <h4 class="complaint-title">
            <i class="fas fa-exclamation-circle me-2"></i>{{ complaint.title }}
          </h4>
          <span class="badge" [ngClass]="getStatusClass(complaint.status)">
            <i class="fas" [ngClass]="{
              'fa-hourglass-half': complaint.status === 'PENDING',
              'fa-eye': complaint.status === 'OPENED',
              'fa-check-circle': complaint.status === 'TREATED'
            }"></i>
            {{ complaint.status }}
          </span>
        </div>

        <p class="complaint-description">{{ complaint.description }}</p>

        <div class="complaint-meta d-flex justify-content-between align-items-center">
          <small class="text-muted">
            <i class="fas fa-calendar-alt me-1"></i>
            Report ID: {{ complaint.id?.substring(0, 8) || 'N/A' }}
          </small>
          <small class="text-muted" *ngIf="complaint.category">
            <i class="fas fa-tag me-1"></i> {{ complaint.category }}
          </small>
        </div>

        <mat-divider *ngIf="i < complaints.length - 1"></mat-divider>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="close()">
      <i class="fas fa-times me-2"></i>Close
    </button>
  </mat-dialog-actions>
</div>
