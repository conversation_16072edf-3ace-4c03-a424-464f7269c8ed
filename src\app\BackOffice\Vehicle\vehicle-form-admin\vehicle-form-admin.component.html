<app-navbar-back></app-navbar-back>

<!-- Sidebar Component -->
<app-sidebar-back></app-sidebar-back>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">Vehicule Add</h2>
    <div class="offset-md-2 col-md-10">
      <div class="container">
        <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">
          <!-- Brand -->
          <div class="form-group mb-3">
            <label for="brand" class="form-label fw-bold">Brand</label>
            <input id="brand" formControlName="brand" class="form-control rounded" required>
          </div>

          <!-- Model -->
          <div class="form-group mb-3">
            <label for="model" class="form-label fw-bold">Model</label>
            <input id="model" formControlName="model" class="form-control rounded" required>
          </div>

          <!-- Capacity -->
          <div class="form-group mb-3">
            <label for="capacity" class="form-label fw-bold">Capacity</label>
            <input id="capacity" type="number" formControlName="capacity" class="form-control rounded" required>
          </div>

          <!-- License Plate -->
          <div class="form-group mb-3">
            <label for="licensePlate" class="form-label fw-bold">License Plate</label>
            <input id="licensePlate" formControlName="licensePlate" class="form-control rounded" required>
          </div>

          <!-- VIN -->
          <div class="form-group mb-3">
            <label for="vin" class="form-label fw-bold">VIN</label>
            <input id="vin" formControlName="vin" class="form-control rounded" required>
          </div>

          <!-- Fabrication Date -->
          <div class="form-group mb-3">
            <label for="fabricationDate" class="form-label fw-bold">Fabrication Date</label>
            <input id="fabricationDate" type="date" formControlName="fabricationDate" class="form-control rounded" required>
          </div>

          <!-- Fuel Type -->
          <div class="form-group mb-3">
            <label for="fuelType" class="form-label fw-bold">Fuel Type</label>
            <input id="fuelType" formControlName="fuelType" class="form-control rounded" required>
          </div>

          <!-- Registration Document Upload -->
          <div class="form-group mb-3">
            <label for="image">Registration Document</label>
            <input type="file" id="image" class="form-control" (change)="onFileSelected($event)" accept="image/*">
          </div>

          <!-- Vehicle Status Dropdown -->
          <div class="form-group mb-3">
            <label for="vehicleStatus">Vehicle Status</label>
            <select id="vehicleStatus" formControlName="vehicleStatus" class="form-control">
              <option value="" disabled selected>-- Select Vehicle Status --</option>
              <option *ngFor="let status of vehicleStatuses" [value]="status">
                {{ status }}
              </option>
            </select>
          </div>

          <!-- Vehicle Type Dropdown -->
          <div class="form-group mb-3">
            <label for="vehicleType">Vehicle Type</label>
            <select id="vehicleType" formControlName="vehicleType" class="form-control">
              <option value="" disabled selected>-- Select Vehicle Type --</option>
              <option *ngFor="let type of vehicleTypes" [value]="type">
                {{ type }}
              </option>
            </select>
          </div>

          <!-- Vehicle Status Dropdown -->
          <div class="form-group mb-3">
            <label for="vehicleStatusD">Vehicle Add Request</label>
            <select id="vehicleStatusD" formControlName="vehicleStatusD" class="form-control">
              <option value="" disabled selected>-- Select Vehicle add status --</option>
              <option *ngFor="let add_status of vehicleStatusD" [value]="add_status">
                {{ add_status }}
              </option>
            </select>
          </div>




          <!-- Submit Button -->
          <button type="submit" class="btn btn-primary">
            {{ vehicleForm.value.id ? 'Update' : 'Add' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</div>
