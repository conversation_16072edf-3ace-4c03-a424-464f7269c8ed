/* ===== VARIABLES ===== */
:root {
  --primary-color: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3f37c9;
  --secondary-color: #f72585;
  --secondary-light: #ff4d6d;
  --accent-color: #4cc9f0;
  --success-color: #4cc9a0;
  --warning-color: #ffbe0b;
  --danger-color: #f72585;
  --info-color: #4cc9f0;
  --dark-color: #212121;
  --light-color: #f8f9fa;
  --bg-gradient: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
  --card-gradient: linear-gradient(135deg, rgba(248, 249, 250, 0.5) 0%, rgba(255, 255, 255, 1) 100%);
  --text-color: #333333;
  --text-secondary: #6c757d;
  --border-color: #e3e6f0;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
  --animation-duration: 0.5s;
  --animation-timing: ease-out;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* ===== LAYOUT STYLES ===== */
.sidebar {
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: white;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.main-content {
  margin-left: 250px;
  padding: 30px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: margin-left 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* ===== GENERAL STYLES ===== */
h2 {
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  padding: 0.5rem 1.5rem;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  text-transform: capitalize;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border-radius: 50px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  border-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-image-slice: 1;
}

h2 i {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-right: 10px;
  font-size: 1.2em;
}

h2::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 55px;
  z-index: -1;
  opacity: 0.2;
  filter: blur(8px);
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% {
    opacity: 0.2;
    filter: blur(8px);
  }
  100% {
    opacity: 0.4;
    filter: blur(12px);
  }
}

/* ===== TABLE CONTAINER ===== */
.table-container {
  background: transparent;
  padding: 1.5rem;
  margin-bottom: 2rem;
  width: 100%;
  max-width: 1200px;
  animation: scaleIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.2s;
  animation-fill-mode: both;
  overflow: hidden;
}

/* ===== AD CARDS CONTAINER ===== */
.ad-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
  width: 100%;
}

/* ===== AD CARD STYLES ===== */
.ad-card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease-out;
}

.ad-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Card Header */
.ad-card-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.ad-title-container {
  flex: 1;
  margin-left: 15px;
}

.ad-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: var(--dark-color);
}

.ad-status {
  margin-left: auto;
}

/* Card Body */
.ad-card-body {
  padding: 15px;
  flex: 1;
}

.ad-info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
  gap: 15px;
}

.ad-info-item {
  display: flex;
  align-items: flex-start;
  flex: 1;
  min-width: 200px;
}

.info-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: var(--primary-color);
}

.info-label {
  font-weight: 600;
  color: var(--text-secondary);
  margin-right: 5px;
}

.info-value {
  color: var(--text-color);
}

.description-text {
  display: block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Card Footer */
.ad-card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Table Header */
.table thead th {
  background: var(--bg-gradient);
  color: white;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 15px;
  text-align: center;
  letter-spacing: 0.5px;
  border: none;
  vertical-align: middle;
  position: relative;
  overflow: hidden;
}

/* Colorful header icons */
.table thead th i {
  font-size: 16px;
  margin-right: 6px;
  display: inline-block;
  vertical-align: middle;
  background: rgba(255, 255, 255, 0.2);
  width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: 50%;
  margin-bottom: 2px;
}

/* Individual column header colors */
.table thead th:nth-child(1) {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
}

.table thead th:nth-child(2) {
  background: linear-gradient(135deg, #4895ef 0%, #4361ee 100%);
}

.table thead th:nth-child(3) {
  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
}

.table thead th:nth-child(4) {
  background: linear-gradient(135deg, #7209b7 0%, #560bad 100%);
}

.table thead th:nth-child(5) {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
}

.table thead th:nth-child(6) {
  background: linear-gradient(135deg, #3a0ca3 0%, #480ca8 100%);
}

/* Add subtle animation to headers */
@keyframes headerGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.table thead th {
  background-size: 200% 200%;
  animation: headerGlow 8s ease infinite;
}

/* Table Cell */
.table tbody td {
  padding: 15px;
  font-size: 14px;
  color: var(--text-color);
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  vertical-align: middle;
}

/* Table Row Hover */
.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Status Badge Styles */
.badge {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  display: inline-block;
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.bg-warning {
  background: linear-gradient(135deg, #ffbe0b 0%, #fb8500 100%) !important;
  color: #000 !important;
}

.bg-success {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%) !important;
  color: white !important;
}

.bg-danger {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%) !important;
  color: white !important;
}

.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
  color: white !important;
}

.bg-info {
  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%) !important;
  color: white !important;
  transition: all 0.3s ease;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.bg-info:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 8px rgba(76, 201, 240, 0.3);
}

/* Different category colors based on text content */
.badge-category {
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: inline-block;
  text-transform: uppercase;
  color: white;
}

.badge-category[data-category*="Electronics"] {
  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
}

.badge-category[data-category*="Clothing"] {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
}

.badge-category[data-category*="Home"] {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%);
}

.badge-category[data-category*="Sports"] {
  background: linear-gradient(135deg, #ffbe0b 0%, #fb8500 100%);
  color: #333;
}

.badge-category[data-category*="Books"] {
  background: linear-gradient(135deg, #7209b7 0%, #560bad 100%);
}

.badge-category[data-category*="Toys"] {
  background: linear-gradient(135deg, #ff4d6d 0%, #c9184a 100%);
}

.badge-category[data-category*="Furniture"] {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
}

.badge-category[data-category*="Beauty"] {
  background: linear-gradient(135deg, #ff758f 0%, #ff7eb3 100%);
}

.badge-category[data-category*="Jewelry"] {
  background: linear-gradient(135deg, #ffd166 0%, #ef476f 100%);
}

.badge-category {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
}

/* Action Buttons */
.btn-action {
  border-radius: 50px;
  font-weight: 600;
  padding: 8px 16px;
  font-size: 13px;
  letter-spacing: 0.5px;
  margin: 0 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  border: none;
}

.btn-action:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.btn-success {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%) !important;
  color: white !important;
  border: none;
}

.btn-danger {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%) !important;
  color: white !important;
  border: none;
}

/* Description truncation */
.description-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Image styling */
.ad-image-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.ad-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ad-thumbnail:hover {
  transform: scale(1.1);
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #aaa;
  font-size: 24px;
}

.image-count {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 4px 0 0 0;
}

/* Price cell styling */
.price-cell {
  width: 100px;
}

.price-tag {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
}

.price-na {
  color: #999;
  font-style: italic;
  font-size: 12px;
}

/* Location cell styling */
.location-cell {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-icon {
  color: #f72585;
  margin-right: 3px;
}

/* Tags cell styling */
.tags-cell {
  max-width: 150px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-badge {
  background: linear-gradient(135deg, #4895ef 0%, #4361ee 100%);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Views cell styling */
.views-cell {
  width: 80px;
  text-align: center;
}

.view-count {
  font-size: 14px;
  color: #666;
}

.view-count i {
  color: #4361ee;
  margin-right: 3px;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  gap: 10px;
}

/* Info button styling */
.btn-info {
  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%) !important;
  color: white !important;
  border: none;
}

/* Empty state styling */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
  animation: fadeIn var(--animation-duration) var(--animation-timing);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-secondary);
  opacity: 0.5;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .table-container {
    padding: 1rem;
  }

  .ad-cards-container {
    grid-template-columns: 1fr;
  }

  .btn-action {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(var(--primary-light), var(--primary-dark));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* ===== DIALOG STYLING ===== */
::ng-deep .ad-detail-dialog-container {
  padding: 0 !important;
}

::ng-deep .ad-detail-dialog-container .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

::ng-deep .ad-detail-dialog-container .mat-mdc-dialog-surface {
  border-radius: 12px !important;
  overflow: hidden !important;
}

::ng-deep .cdk-overlay-backdrop.cdk-overlay-backdrop-showing {
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 20px 15px;
  }

  .sidebar {
    position: relative;
    width: 100%;
    height: auto;
  }

  h2 {
    font-size: 1.5rem;
  }

  .ad-info-row {
    flex-direction: column;
    gap: 10px;
  }

  .ad-info-item {
    min-width: 100%;
  }

  .ad-card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .ad-title-container {
    margin: 10px 0;
  }

  .ad-status {
    margin-left: 0;
    align-self: flex-start;
  }

  .ad-card-footer {
    flex-direction: column;
  }

  .btn-action {
    width: 100%;
    padding: 8px;
    font-size: 14px;
    margin: 0;
  }
}
