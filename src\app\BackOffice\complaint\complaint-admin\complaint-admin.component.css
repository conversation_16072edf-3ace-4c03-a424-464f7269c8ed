/* ===== VARIABLES ===== */
:root {
  --primary-color: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3f37c9;
  --secondary-color: #f72585;
  --secondary-light: #ff4d6d;
  --accent-color: #4cc9f0;
  --success-color: #4cc9a0;
  --warning-color: #ffbe0b;
  --danger-color: #f72585;
  --info-color: #4cc9f0;
  --dark-color: #212121;
  --light-color: #f8f9fa;
  --bg-gradient: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
  --card-gradient: linear-gradient(135deg, rgba(248, 249, 250, 0.5) 0%, rgba(255, 255, 255, 1) 100%);
  --text-color: #333333;
  --text-secondary: #6c757d;
  --border-color: #e3e6f0;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
  --animation-duration: 0.5s;
  --animation-timing: ease-out;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ===== FILTER SECTION STYLES ===== */
.filter-section {
  background: white;
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  animation: scaleIn var(--animation-duration) var(--animation-timing);
  animation-fill-mode: both;
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 1200px;
}

.filter-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #4361ee, #f72585, #4cc9f0);
}

.filter-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  position: relative;
  display: inline-block;
  margin: 0 auto;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
  width: 100%;
}

.filter-title i {
  color: var(--primary-color);
}

.filter-label {
  font-size: 1rem;
  font-weight: 600;
  color: #555;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-label i {
  color: var(--primary-color);
}

/* Priority Filter Buttons */
.priority-filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 30px;
  background-color: #f5f5f5;
  color: #555;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.priority-filter-btn i {
  font-size: 14px;
}

.priority-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.priority-filter-btn.selected {
  background-color: #4361ee;
  color: white;
  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
  border-color: #4361ee;
}

.priority-filter-btn.priority-high {
  background-color: rgba(247, 37, 133, 0.1);
  color: #f72585;
  border-color: rgba(247, 37, 133, 0.2);
}

.priority-filter-btn.priority-high:hover,
.priority-filter-btn.priority-high.selected {
  background-color: #f72585;
  color: white;
  border-color: #f72585;
  box-shadow: 0 4px 8px rgba(247, 37, 133, 0.3);
}

.priority-filter-btn.priority-medium {
  background-color: rgba(255, 190, 11, 0.1);
  color: #ffbe0b;
  border-color: rgba(255, 190, 11, 0.2);
}

.priority-filter-btn.priority-medium:hover,
.priority-filter-btn.priority-medium.selected {
  background-color: #ffbe0b;
  color: #333;
  border-color: #ffbe0b;
  box-shadow: 0 4px 8px rgba(255, 190, 11, 0.3);
}

.priority-filter-btn.priority-low {
  background-color: rgba(76, 201, 240, 0.1);
  color: #4cc9f0;
  border-color: rgba(76, 201, 240, 0.2);
}

.priority-filter-btn.priority-low:hover,
.priority-filter-btn.priority-low.selected {
  background-color: #4cc9f0;
  color: white;
  border-color: #4cc9f0;
  box-shadow: 0 4px 8px rgba(76, 201, 240, 0.3);
}

/* ===== GENERAL STYLES ===== */
h2 {
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  padding-bottom: 0.5rem;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
}

h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

h4 {
  font-weight: 600;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.1s;
}

/* Chart Container Styles */
.chart-container {
  background: white;
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  animation: scaleIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.15s;
  animation-fill-mode: both;
  border: 1px solid rgba(0, 0, 0, 0.05);
  height: 450px;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(to right, #4361ee, #f72585, #4cc9f0);
}

.chart-container h4 {
  font-weight: 700;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
  display: inline-block;
}

.chart-container h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #4361ee, #f72585);
  border-radius: 2px;
}

.chart-container canvas {
  margin: 0 auto;
  max-width: 100%;
  height: 350px !important;
}

/* ===== CONTACT INFO & EVIDENCE STYLES ===== */
.contact-info {
  display: flex;
  align-items: center;
  font-size: 13px;
  padding: 4px 0;
}

.contact-info i {
  color: var(--primary-color);
  margin-right: 6px;
  font-size: 14px;
}

.evidence-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.evidence-button {
  min-width: 120px !important;
  padding: 4px 12px !important;
  font-size: 13px !important;
  line-height: 1.2 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}

.evidence-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

.evidence-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  line-height: 18px;
}

/* ===== TABLE STYLES ===== */
.table-container {
  background: white;
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  padding: 1.5rem !important;
  margin-bottom: 2rem;
  width: 100%;
  max-width: 1200px;
  animation: scaleIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.2s;
  animation-fill-mode: both;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
}

/* Table Header */
.mat-header-cell {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
  font-size: 14px;
  font-weight: 600;
  padding: 16px;
  text-align: center;
  letter-spacing: 0.5px;
  border: none;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.header-content i {
  font-size: 16px;
}

.header-content span {
  font-weight: 600;
}

/* Table Cell */
.mat-cell {
  padding: 14px 16px;
  font-size: 15px;
  color: #333333;
  font-weight: 500;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  transition: var(--transition);
  vertical-align: middle;
  letter-spacing: 0.2px;
}

/* Hover effect */
.mat-row {
  transition: var(--transition);
  height: auto;
}

.mat-row:hover {
  background-color: rgba(67, 97, 238, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Advertisement row highlighting */
.advertisement-row {
  background-color: rgba(247, 37, 133, 0.05);
}

.advertisement-row:hover {
  background-color: rgba(247, 37, 133, 0.1);
}

/* Category Badge */
.category-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  border-radius: 20px;
  background-color: #f5f5f5;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.3px;
}

.category-badge i {
  font-size: 16px;
  color: #4361ee;
}

.category-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.12);
}

/* Category-specific colors */
.category-retard-livraison, .category-livraison-mauvaise-adresse {
  background-color: #e6f7ff;
  color: #0066cc;
}

.category-produit-endommage, .category-article-manquant {
  background-color: #fff2e6;
  color: #cc5500;
}

.category-mauvais-service-client {
  background-color: #e6f2ff;
  color: #0044cc;
}

.category-erreur-facturation, .category-frais-inattendus {
  background-color: #e6ffe6;
  color: #006600;
}

.category-commande-incomplete {
  background-color: #fff9e6;
  color: #cc8800;
}

.category-mauvaise-qualite {
  background-color: #ffe6e6;
  color: #cc0000;
}

.category-comportement-personnel {
  background-color: #f2e6ff;
  color: #6600cc;
}

.category-probleme-technique {
  background-color: #e6e6ff;
  color: #0000cc;
}

.category-probleme-remboursement {
  background-color: #ffe6f2;
  color: #cc0066;
}

.category-publicite-mensongere, .category-advertisement {
  background-color: #ffebee;
  color: #d32f2f;
}

.category-securite-harcelement {
  background-color: #fce4ec;
  color: #c2185b;
}

.category-autre, .category-other {
  background-color: #f5f5f5;
  color: #757575;
}

/* Description Container */
.description-container {
  position: relative;
  max-width: 280px;
  max-height: 90px;
  overflow: hidden;
  text-align: left;
  margin: 0 auto;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px 12px;
  border-left: 3px solid #4361ee;
}

.description-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  text-align: left;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.description-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(to bottom, rgba(249, 249, 249, 0), rgba(249, 249, 249, 1));
}

.view-more-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(67, 97, 238, 0.1);
  color: #4361ee;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.view-more-btn:hover {
  background: rgba(67, 97, 238, 0.2);
}

.view-more-btn i {
  font-size: 10px;
  margin-right: 4px;
}

/* Priority Badge */
.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
  transition: var(--transition);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.priority-badge i {
  font-size: 16px;
}

.priority-low {
  background: linear-gradient(135deg, #4cc9f0 0%, #3ab7db 100%);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.priority-medium {
  background: linear-gradient(135deg, #ffbe0b 0%, #fb8500 100%);
  color: #333;
  font-weight: 800;
}

.priority-high {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.priority-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge i {
  font-size: 16px;
}

.status-pending {
  background: linear-gradient(135deg, #ffbe0b 0%, #fb8500 100%);
  color: #333;
  font-weight: 800;
}

.status-opened {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.status-treated {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.status-rejected {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.status-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Contact Info Container */
.contact-info-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.contact-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  background-color: #f0f4ff;
  padding: 8px 12px;
  border-radius: 12px;
  max-width: 200px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #4361ee;
  font-weight: 500;
}

.contact-info-item i {
  color: #4361ee;
  font-size: 14px;
}

.contact-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
  letter-spacing: 0.2px;
}

.no-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #777;
  font-style: italic;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.no-info i {
  font-size: 14px;
  color: #999;
}

/* Evidence Container */
.evidence-container {
  display: flex;
  justify-content: center;
}

.evidence-button {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%) !important;
  color: white !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
  transition: var(--transition) !important;
  min-width: auto !important;
  line-height: 1.2 !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important;
}

.evidence-button i {
  font-size: 16px;
}

.evidence-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25) !important;
}

/* Ad Title Container */
.ad-title-container {
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 280px;
  margin: 0 auto;
  background-color: #fff0f5;
  padding: 10px 14px;
  border-radius: 10px;
  border-left: 3px solid #f72585;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.ad-title-container i {
  color: #f72585;
  font-size: 16px;
}

.ad-title-text {
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  font-size: 15px;
  letter-spacing: 0.2px;
}

/* Report Count Button */
.report-count-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
  color: white;
  font-size: 14px;
  font-weight: 700;
  border: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: var(--transition);
  cursor: pointer;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.report-count {
  font-weight: 800;
  font-size: 16px;
}

.report-count-button i {
  font-size: 14px;
}

.report-count-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
}

/* Actions Container */
.actions-container {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.action-button i {
  font-size: 16px;
  transition: all 0.2s ease;
}

.action-button.view {
  background-color: rgba(67, 97, 238, 0.15);
  color: #4361ee;
  border: 2px solid rgba(67, 97, 238, 0.3);
}

.action-button.edit {
  background-color: rgba(76, 201, 240, 0.15);
  color: #4cc9f0;
  border: 2px solid rgba(76, 201, 240, 0.3);
}

.action-button.treat {
  background-color: rgba(76, 201, 160, 0.15);
  color: #4cc9a0;
  border: 2px solid rgba(76, 201, 160, 0.3);
}

.action-button.delete {
  background-color: rgba(247, 37, 133, 0.15);
  color: #f72585;
  border: 2px solid rgba(247, 37, 133, 0.3);
}

.action-button:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.action-button.view:hover {
  background-color: #4361ee;
  color: white;
  border-color: #4361ee;
}

.action-button.edit:hover {
  background-color: #4cc9f0;
  color: white;
  border-color: #4cc9f0;
}

.action-button.treat:hover {
  background-color: #4cc9a0;
  color: white;
  border-color: #4cc9a0;
}

.action-button.delete:hover {
  background-color: #f72585;
  color: white;
  border-color: #f72585;
}

/* Reset Button */
button[mat-button] {
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 30px;
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
  color: white;
  font-weight: 600;
  transition: var(--transition);
  margin-bottom: 2rem;
  border: none;
  box-shadow: 0 4px 10px rgba(247, 37, 133, 0.3);
  letter-spacing: 0.5px;
}

button[mat-button]:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(247, 37, 133, 0.4);
}

button[mat-button]:active {
  transform: translateY(-1px);
}

/* ===== CHART SECTION ===== */
#complaintChart {
  max-width: 100%;
  display: block;
  margin: 0 auto;
  animation: scaleIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

/* ===== CATEGORY CARDS ===== */
.category-card {
  width: 140px;
  padding: 1.2rem 1rem;
  background: white;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.08);
  text-align: center;
  border-radius: 16px;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  animation-fill-mode: both;
  position: relative;
  overflow: hidden;
}

/* Staggered animation delays for category cards */
.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.15s; }
.category-card:nth-child(3) { animation-delay: 0.2s; }
.category-card:nth-child(4) { animation-delay: 0.25s; }
.category-card:nth-child(5) { animation-delay: 0.3s; }
.category-card:nth-child(6) { animation-delay: 0.35s; }
.category-card:nth-child(7) { animation-delay: 0.4s; }
.category-card:nth-child(8) { animation-delay: 0.45s; }
.category-card:nth-child(9) { animation-delay: 0.5s; }
.category-card:nth-child(10) { animation-delay: 0.55s; }
.category-card:nth-child(11) { animation-delay: 0.6s; }
.category-card:nth-child(12) { animation-delay: 0.65s; }
.category-card:nth-child(13) { animation-delay: 0.7s; }
.category-card:nth-child(14) { animation-delay: 0.75s; }
.category-card:nth-child(15) { animation-delay: 0.8s; }
.category-card:nth-child(16) { animation-delay: 0.85s; }

/* Colorful top border for category cards */
.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #4361ee, #f72585);
  opacity: 0.7;
  transition: var(--transition);
}

.category-card:hover::before {
  height: 6px;
  opacity: 1;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
  border-color: rgba(67, 97, 238, 0.3);
}

.category-card.selected {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.08) 0%, rgba(76, 201, 240, 0.08) 100%);
  border-color: var(--primary-color);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.15);
  transform: translateY(-5px);
}

.category-card.selected::before {
  height: 6px;
  opacity: 1;
  background: linear-gradient(to right, #4361ee, #f72585, #4cc9f0);
}

.category-card .fw-bold {
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 0.8rem;
  transition: var(--transition);
  font-weight: 600;
  letter-spacing: 0.3px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-card:hover .fw-bold {
  color: var(--primary-color);
}

.category-card .fs-5 {
  font-size: 1.6rem !important;
  font-weight: 700;
  color: var(--primary-color);
  transition: var(--transition);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.category-card:hover .fs-5 {
  transform: scale(1.1);
}

.category-card.selected .fs-5 {
  color: var(--primary-dark);
}

/* ===== LAYOUT STYLES ===== */
.sidebar {
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: white;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.flex-grow-1 {
  margin-left: 250px;
  padding: 30px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: margin-left 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* ===== PAGINATION ===== */
.mat-paginator {
  background: transparent;
  font-size: 14px;
  border-radius: 8px;
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(var(--primary-light), var(--primary-dark));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
  .category-card {
    width: 110px;
    padding: 1rem 0.5rem;
  }

  .category-card .fs-5 {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: relative;
    width: 100%;
    height: auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .flex-grow-1 {
    margin-left: 0;
    width: 100%;
    padding: 20px 15px;
  }

  h2 {
    font-size: 1.5rem;
  }

  h4 {
    font-size: 1.2rem;
  }

  /* Chart Adjustments for Mobile */
  #complaintChart {
    width: 250px !important;
    height: 250px !important;
  }

  /* Table Adjustments for Mobile */
  .table-container {
    width: 100%;
    padding: 1rem;
    overflow-x: auto;
  }

  .mat-header-cell, .mat-cell {
    padding: 10px 12px;
    font-size: 12px;
  }

  .category-card {
    width: 100px;
    padding: 0.8rem 0.4rem;
  }

  .category-card .fw-bold {
    font-size: 0.75rem;
  }

  .category-card .fs-5 {
    font-size: 1.1rem !important;
  }
}

