<app-header-front></app-header-front>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    
    <!-- Complaint Title -->
    <h2 class="text-primary text-center mb-4">
      <i class="fas fa-exclamation-circle"></i> Complaint Details
    </h2>

    <!-- Complaint Information -->
    <div class="row">
      <!-- Title -->
      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-file-alt"></i> Title:</label>
        <p class="text-dark border rounded p-2 bg-light">{{ complaint.title }}</p>
      </div>

      <!-- Description -->
      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-align-left"></i> Description:</label>
        <p class="text-muted border rounded p-2 bg-light">{{ complaint.description }}</p>
      </div>

      <!-- Category -->
      <div class="col-md-6 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-tags"></i> Category:</label>
        <p class="text-dark border rounded p-2 bg-light">{{ complaint.category }}</p>
      </div>

      <!-- Status -->
      <div class="col-md-6 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-info-circle"></i> Status:</label>
        <p class="border rounded p-2 bg-light">
          <span 
            class="badge" 
            [ngClass]="{
              'bg-warning text-dark': complaint.status === 'PENDING',
              'bg-primary': complaint.status === 'OPENED',
              'bg-success': complaint.status === 'TREATED'
            }">
            {{ complaint.status }}
          </span>
        </p>
      </div>
    </div>

    <!-- Back Button -->
    <div class="d-flex justify-content-center mt-4">
      <button class="btn btn-secondary btn-lg px-4" (click)="goBack()">
        <i class="fas fa-arrow-left"></i> Back to Complaints
      </button>
    </div>

  </div>
</div>

<app-footer-front></app-footer-front>
