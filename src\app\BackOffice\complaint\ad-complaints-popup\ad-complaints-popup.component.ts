import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { Complaint, ComplaintControllerService, Ad, AdControllerService } from 'src/app/openapi';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

@Component({
  selector: 'app-ad-complaints-popup',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule
  ],
  templateUrl: './ad-complaints-popup.component.html',
  styleUrls: ['./ad-complaints-popup.component.css']
})
export class AdComplaintsPopupComponent implements OnInit {
  complaints: Complaint[] = [];
  ad: Ad | null = null;
  loading = true;
  error = '';

  constructor(
    private dialogRef: MatDialogRef<AdComplaintsPopupComponent>,
    private complaintService: ComplaintControllerService,
    private adService: AdControllerService,
    @Inject(MAT_DIALOG_DATA) public data: { adId: string, reportCount: number }
  ) {}

  ngOnInit(): void {
    this.loadAdDetails();
    this.loadComplaints();
  }

  loadAdDetails(): void {
    if (!this.data.adId) {
      this.error = 'No ad ID provided';
      this.loading = false;
      return;
    }

    this.adService.getAdById(this.data.adId).subscribe({
      next: async (response) => {
        if (response instanceof Blob) {
          const text = await response.text();
          this.ad = JSON.parse(text);
        } else {
          this.ad = response;
        }
      },
      error: (err) => {
        console.error('Error loading ad details', err);
        this.error = 'Failed to load ad details';
        this.loading = false;
      }
    });
  }

  loadComplaints(): void {
    if (!this.data.adId) {
      this.error = 'No ad ID provided';
      this.loading = false;
      return;
    }

    this.complaintService.getComplaintsByAdId(this.data.adId).subscribe({
      next: async (response) => {
        if (response instanceof Blob) {
          const text = await response.text();
          this.complaints = JSON.parse(text);
        } else {
          this.complaints = response;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading complaints', err);
        this.error = 'Failed to load complaints';
        this.loading = false;
      }
    });
  }

  getStatusClass(status: Complaint.StatusEnum | undefined): string {
    if (status === Complaint.StatusEnum.Pending) {
      return 'bg-warning text-dark';
    } else if (status === Complaint.StatusEnum.Opened) {
      return 'bg-info text-white';
    } else if (status === Complaint.StatusEnum.Treated) {
      return 'bg-success text-white';
    } else if (status === 'REJECTED' as any) {
      return 'bg-danger text-white';
    } else {
      return 'bg-secondary text-white';
    }
  }

  close(): void {
    this.dialogRef.close();
  }
}
