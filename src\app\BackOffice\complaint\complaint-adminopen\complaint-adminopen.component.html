<app-navbar-back></app-navbar-back>

<div class="d-flex">
  <!-- Sidebar -->
  <app-sidebar-back class="sidebar"></app-sidebar-back>

  <!-- Main Content -->
  <div class="content-container flex-grow-1 p-4">
    <div *ngIf="complaint" class="card shadow-lg p-4">
      <h2 class="text-primary text-center mb-4">
        <i class="fas fa-exclamation-circle"></i> Complaint Details
      </h2>

      <div class="mb-3">
        <strong>Title:</strong>
        <p class="border rounded p-3 bg-light">{{ complaint.title }}</p>
      </div>

      <div class="mb-3">
        <strong>Description:</strong>
        <p class="border rounded p-3 bg-light">{{ complaint.description }}</p>
      </div>

      <div class="mb-3">
        <strong>Category:</strong>
        <p class="border rounded p-3 bg-light">{{ complaint.category }}</p>
      </div>

      <div class="mb-3">
        <strong>Priority:</strong>
        <span class="badge ms-2"
          [ngClass]="{
            'bg-info': complaint.priority === 'LOW',
            'bg-warning text-dark': complaint.priority === 'MEDIUM' || !complaint.priority,
            'bg-danger': complaint.priority === 'HIGH'
          }">
          <i class="fas" [ngClass]="{
            'fa-thermometer-quarter': complaint.priority === 'LOW',
            'fa-thermometer-half': complaint.priority === 'MEDIUM' || !complaint.priority,
            'fa-thermometer-full': complaint.priority === 'HIGH'
          }"></i>
          {{ complaint.priority || 'MEDIUM' }}
        </span>
      </div>

      <!-- Contact Information -->
      <div class="mb-3">
        <strong>Contact Information:</strong>
        <div class="border rounded p-3 bg-light">
          <div *ngIf="complaint.contactEmail" class="mb-2">
            <i class="fas fa-envelope text-primary me-2"></i> <strong>Email:</strong> {{ complaint.contactEmail }}
          </div>
          <div *ngIf="complaint.contactPhone" class="mb-2">
            <i class="fas fa-phone text-primary me-2"></i> <strong>Phone:</strong> {{ complaint.contactPhone }}
          </div>
          <div *ngIf="!complaint.contactEmail && !complaint.contactPhone" class="text-muted">
            <i class="fas fa-info-circle me-2"></i> No contact information provided
          </div>
        </div>
      </div>

      <!-- Evidence Files -->
      <div class="mb-3">
        <strong>Evidence Files:</strong>
        <div class="border rounded p-3 bg-light">
          <div *ngIf="complaint.evidence && complaint.evidence.length > 0">
            <div class="evidence-count mb-3 text-center">
              <span class="badge bg-primary">{{ complaint.evidence.length }} file(s) attached</span>
            </div>

            <div class="row">
              <div *ngFor="let file of complaint.evidence; let i = index" class="col-md-6 mb-4">
                <div class="evidence-card">
                  <!-- For images, display them directly -->
                  <div *ngIf="isImage(file)" class="text-center">
                    <div class="image-container mb-3">
                      <img [src]="file" class="img-fluid" alt="Evidence image">
                    </div>
                    <div class="action-buttons">
                      <a [href]="file" target="_blank" class="btn btn-sm btn-primary me-2">
                        <i class="fas fa-external-link-alt me-1"></i>Open
                      </a>
                      <a [href]="file" download="evidence-image-{{i+1}}.jpg" class="btn btn-sm btn-info">
                        <i class="fas fa-download me-1"></i>Download
                      </a>
                    </div>
                  </div>

                  <!-- For other file types, show a download link -->
                  <div *ngIf="!isImage(file)" class="text-center">
                    <div class="file-icon mb-2">
                      <i class="fas fa-file-alt fa-3x text-primary"></i>
                    </div>
                    <p class="file-label mb-2">File #{{ i+1 }}</p>
                    <div>
                      <a [href]="file" download="evidence-file-{{i+1}}" class="btn btn-sm btn-primary">
                        <i class="fas fa-download me-1"></i>Download File
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="!complaint.evidence || complaint.evidence.length === 0" class="text-center py-3">
            <i class="fas fa-exclamation-circle text-muted me-2"></i>
            <span class="text-muted">No evidence files attached</span>
          </div>
        </div>
      </div>

      <div class="mb-3">
        <strong>Status:</strong>
        <span class="badge ms-2"
          [ngClass]="{
            'bg-warning text-dark': complaint.status === 'PENDING',
            'bg-primary': complaint.status === 'OPENED',
            'bg-success': complaint.status === 'TREATED'
          }">
          {{ complaint.status }}
        </span>
      </div>

      <div class="text-center mt-4 d-flex justify-content-center gap-3">
        <!-- ✅ Open Complaint (Only if Pending) -->
        <button *ngIf="complaint.status === 'PENDING'" class="btn btn-primary btn-lg px-4" (click)="openComplaint(complaint.id!)">
          <i class="fas fa-folder-open"></i> Open Complaint
        </button>

        <!-- ✅ Mark as Treated (Only if Opened) -->
        <button *ngIf="complaint.status === 'OPENED'" class="btn btn-success btn-lg px-4" (click)="treatComplaint(complaint.id!)">
          <i class="fas fa-check-circle"></i> Mark as Treated
        </button>

        <!-- ❌ Delete Complaint -->
        <button class="btn btn-danger btn-lg px-4" (click)="deleteComplaint(complaint.id!)">
          <i class="fas fa-trash"></i> Delete
        </button>

        <!-- 🔙 Return Button -->
        <a class="btn btn-secondary btn-lg px-4" [routerLink]="['/admin/complaints']">
          <i class="fas fa-arrow-left"></i> Return
        </a>
      </div>
    </div>
  </div>
</div>

<app-footer-back></app-footer-back>
