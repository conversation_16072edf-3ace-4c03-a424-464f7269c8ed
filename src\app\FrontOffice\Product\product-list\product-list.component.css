/* ✅ General Container */
.container {
  margin-top: 2rem;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* ✅ Animation: Fade-in for product cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ✅ Header & Add Product Button */
.d-flex {
  margin-bottom: 1.5rem;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
}

/* ✅ "Add Product" Button */
.btn.btn-primary {
  background-color: #007bff;
  border: none;
  border-radius: 25px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.btn.btn-primary:hover {
  background-color: #0056b3;
  transform: scale(1.05);
}

/* ✅ Sidebar Styling */
.sidebar {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  min-height: auto; /* ✅ Permet à la sidebar de grandir avec le contenu */
  position: relative; /* ✅ Assurez-vous qu'elle n'est pas en position fixe */
}


.sidebar h5 {
  text-align: center;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.list-group-item {
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.list-group-item:hover {
  background-color: #e9ecef;
}

.list-group-item.active {
  background-color: #ff6b6b !important;
  color: white !important;
  transform: scale(1.05);
}

/* ✅ Product Cards */
.card {
  border: none;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  animation: fadeInUp 0.5s ease forwards;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hover Effect */
.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* ✅ Product Image */
.card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card:hover .card-img-top {
  transform: scale(1.1);
}

/* ✅ Card Body */
.card-body {
  padding: 1rem;
}

.card-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.card-text {
  font-size: 1rem;
  color: #666;
  margin-bottom: 0.5rem;
}

/* ✅ Card Footer (Buttons) */
.card-footer {
  background-color: transparent;
  border-top: none;
  padding: 0.5rem 1rem 1rem;
  display: flex;
  justify-content: space-between;
}

/* ✅ Buttons inside Cards */
.btn {
  border-radius: 20px;
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.btn-secondary {
  background-color: #6c757d;
  border: none;
  color: #fff;
}

.btn-secondary:hover {
  background-color: #565e64;
  transform: scale(1.05);
}

.btn-danger {
  background-color: #dc3545;
  border: none;
  color: #fff;
}

.btn-danger:hover {
  background-color: #c82333;
  transform: scale(1.05);
}

/* ✅ Responsive Layout */
@media (max-width: 992px) {
  .sidebar {
    width: 100%;
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: relative;
    width: 100%;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .card-text {
    font-size: 0.9rem;
  }

  .btn {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}