import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { Complaint, ComplaintControllerService, Ad, AdControllerService } from 'src/app/openapi';
import { Chart, registerables } from 'chart.js';
import chartjsPluginDatalabels from 'chartjs-plugin-datalabels';
import { NavbarBackComponent } from "../../navbar-back/navbar-back.component";
import { SidebarBackComponent } from "../../sidebar-back/sidebar-back.component";
import { FooterBackComponent } from "../../footer-back/footer-back.component";
import { FormsModule } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AdComplaintsPopupComponent } from '../ad-complaints-popup/ad-complaints-popup.component';

Chart.register(...registerables, chartjsPluginDatalabels);

@Component({
  selector: 'app-complaint-admin',
  standalone: true,
  imports: [
    CommonModule,
    NavbarBackComponent,
    SidebarBackComponent,
    FooterBackComponent,
    FormsModule,
    MatPaginatorModule,
    MatSortModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatDialogModule,
    RouterModule
  ],
  templateUrl: './complaint-admin.component.html',
  styleUrl: './complaint-admin.component.css'
})
export class ComplaintAdminComponent implements OnInit {
  complaints: Complaint[] = [];
  dataSource!: MatTableDataSource<any>; // Use 'any' type to support both Complaint and Ad
  displayedColumns: string[] = ['category', 'description', 'priority', 'contactInfo', 'evidence', 'status', 'actions'];
  categories: string[] = [
    'RETARD_LIVRAISON', 'PRODUIT_ENDOMMAGE', 'MAUVAIS_SERVICE_CLIENT',
    'ERREUR_FACTURATION', 'ARTICLE_MANQUANT', 'LIVRAISON_MAUVAISE_ADRESSE',
    'MAUVAISE_QUALITE', 'COMPORTEMENT_PERSONNEL', 'PROBLEME_TECHNIQUE',
    'PROBLEME_REMBOURSEMENT', 'FRAIS_INATTENDUS', 'PUBLICITE_MENSONGERE',
    'SECURITE_HARCELEMENT', 'ADVERTISEMENT', 'AUTRE'
  ];
  priorities: string[] = ['HIGH', 'MEDIUM', 'LOW'];
  selectedCategory: string = 'ALL';
  selectedPriority: string = 'ALL';
  categoryCounts: { [key: string]: number } = {};
  priorityCounts: { [key: string]: number } = {};

  // For reported ads
  adComplaintCounts: { [adId: string]: number } = {};
  reportedAds: Ad[] = [];
  isReportedAdsView = false;

  chart: any;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private complaintService: ComplaintControllerService,
    private adService: AdControllerService,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getAllComplaints();
    this.getAdComplaintCounts();
  }

  getAllComplaints(): void {
    this.complaintService.listComplaints().subscribe({
      next: (response) => {
        if (response instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            this.complaints = JSON.parse(reader.result as string);
            console.log('Complaints loaded:', this.complaints);

            // Debug evidence data
            this.complaints.forEach(complaint => {
              console.log(`Complaint ID: ${complaint.id}, Evidence:`, complaint.evidence);
            });

            this.updateCounts();
            this.setupTable();
            this.createChart();
          };
          reader.readAsText(response);
        } else {
          this.complaints = response;
          console.log('Complaints loaded:', this.complaints);
          this.updateCounts();
          this.setupTable();
          this.createChart();
        }
      },
      error: (err) => console.error("Error fetching complaints", err)
    });
  }

  // Update both category and priority counts
  updateCounts(): void {
    // Reset counts
    this.categoryCounts = {};
    this.priorityCounts = {};

    // Initialize priority counts
    this.priorities.forEach(priority => {
      this.priorityCounts[priority] = 0;
    });

    // Count complaints by category and priority
    this.complaints.forEach(c => {
      // Count by category
      if (c.category) {
        if (!this.categoryCounts[c.category]) {
          this.categoryCounts[c.category] = 0;
        }
        this.categoryCounts[c.category]++;
      }

      // Count by priority
      if (c.priority) {
        if (!this.priorityCounts[c.priority]) {
          this.priorityCounts[c.priority] = 0;
        }
        this.priorityCounts[c.priority]++;
      }
    });
  }

  setupTable(): void {
    if (this.selectedCategory === 'ADVERTISEMENT' && this.isReportedAdsView) {
      // We're in the reported ads view, so we need to change the columns
      this.displayedColumns = ['title', 'description', 'reportCount', 'actions'];
      this.dataSource = new MatTableDataSource(this.reportedAds);
    } else {
      // Normal complaints view
      this.displayedColumns = ['category', 'description', 'priority', 'contactInfo', 'evidence', 'status', 'actions'];
      this.dataSource = new MatTableDataSource(this.filteredComplaints);
    }

    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  // Select category filter
  selectCategory(category: string): void {
    this.selectedCategory = category;

    if (category === 'ADVERTISEMENT') {
      // Toggle between normal complaints view and reported ads view
      this.isReportedAdsView = !this.isReportedAdsView;

      if (this.isReportedAdsView) {
        this.loadReportedAds();
      }
    } else {
      // For other categories, always show normal complaints view
      this.isReportedAdsView = false;
    }

    this.updateCounts();
    this.setupTable();
    this.createChart();
  }

  // Select priority filter
  selectPriority(priority: string): void {
    this.selectedPriority = priority;
    this.updateCounts();
    this.setupTable();
    this.createChart();
  }

  // Reset all filters
  resetFilters(): void {
    this.selectedCategory = 'ALL';  // Reset category filter
    this.selectedPriority = 'ALL';  // Reset priority filter
    this.isReportedAdsView = false; // Reset to normal view
    this.updateCounts();            // Recalculate counts
    this.setupTable();              // Refresh the table
    this.createChart();             // Refresh the chart
  }

  openComplaint(id: string): void {
    if (confirm("Do you want to mark this complaint as OPENED?")) {
      this.complaintService.opencomplaint(id).subscribe({
        next: () => {
          alert("Complaint status updated to OPENED.");
          this.getAllComplaints();
        },
        error: (err) => console.error("Error opening complaint", err)
      });
    }
  }

  treatComplaint(id: string): void {
    if (confirm("Do you want to mark this complaint as TREATED?")) {
      this.complaintService.treatecomplaint(id).subscribe({
        next: () => {
          alert("Complaint status updated to TREATED.");
          this.getAllComplaints();
        },
        error: (err) => console.error("Error treating complaint", err)
      });
    }
  }

  deleteComplaint(id: string): void {
    if (confirm("Are you sure you want to delete this complaint?")) {
      this.complaintService.deleteComplaint(id).subscribe({
        next: () => {
          alert("Complaint deleted successfully!");
          this.getAllComplaints();
        },
        error: (err) => console.error("Error during deletion", err)
      });
    }
  }

  createChart(): void {
    const filtered = this.filteredComplaints;
    const categoryCounts: { [key: string]: number } = {};

    filtered.forEach(complaint => {
      if (complaint.category) {
        categoryCounts[complaint.category] = (categoryCounts[complaint.category] || 0) + 1;
      }
    });

    // Get the category keys and translate them to English
    const categoryKeys = Object.keys(categoryCounts);
    const translatedLabels = categoryKeys.map(category => this.formatCategoryLabel(category));
    const data = Object.values(categoryCounts);

    // Professional color palette for categories
    const categoryColorMap: { [key: string]: string } = {
      'RETARD_LIVRAISON': '#4361ee',         // Delivery Delay - Blue
      'PRODUIT_ENDOMMAGE': '#ef476f',        // Damaged Product - Red
      'MAUVAIS_SERVICE_CLIENT': '#ffd166',   // Poor Customer Service - Yellow
      'ERREUR_FACTURATION': '#06d6a0',       // Billing Error - Green
      'ARTICLE_MANQUANT': '#118ab2',         // Missing Item - Teal
      'LIVRAISON_MAUVAISE_ADRESSE': '#073b4c', // Wrong Delivery Address - Dark Blue
      'MAUVAISE_QUALITE': '#ff9e00',         // Poor Quality - Orange
      'COMPORTEMENT_PERSONNEL': '#9b5de5',   // Staff Behavior - Purple
      'PROBLEME_TECHNIQUE': '#f15bb5',       // Technical Issue - Pink
      'PROBLEME_REMBOURSEMENT': '#00bbf9',   // Refund Problem - Light Blue
      'FRAIS_INATTENDUS': '#00f5d4',         // Unexpected Fees - Turquoise
      'PUBLICITE_MENSONGERE': '#fb5607',     // False Advertising - Orange Red
      'SECURITE_HARCELEMENT': '#ff006e',     // Security/Harassment - Magenta
      'ADVERTISEMENT': '#8338ec',            // Advertisement - Violet
      'AUTRE': '#adb5bd'                     // Other - Gray
    };

    // Get colors for each category
    const backgroundColors = categoryKeys.map(category =>
      categoryColorMap[category] || `hsl(${Math.floor(Math.random() * 360)}, 70%, 60%)`
    );

    if (this.chart) {
      this.chart.destroy();
    }

    const ctx = document.getElementById('complaintChart') as HTMLCanvasElement;
    if (ctx) {
      this.chart = new Chart(ctx, {
        type: 'doughnut', // Changed to doughnut for more professional look
        data: {
          labels: translatedLabels,
          datasets: [{
            label: 'Complaints by Category',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: 'white',
            borderWidth: 2,
            hoverOffset: 15, // Increase offset on hover for better emphasis
            borderRadius: 4  // Slightly rounded segments
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: '60%', // Larger hole in the middle
          layout: {
            padding: 20
          },
          plugins: {
            datalabels: {
              formatter: (value: any, context: any) => {
                const sum = data.reduce((a: number, b: number) => a + b, 0);
                const percentage = (value / sum) * 100;
                return percentage > 5 ? `${percentage.toFixed(1)}%` : ''; // Only show labels for segments > 5%
              },
              color: 'white',
              font: {
                weight: 'bold',
                size: 14
              },
              textAlign: 'center',
              textStrokeColor: 'rgba(0, 0, 0, 0.3)',
              textStrokeWidth: 2,
              offset: 8
            },
            legend: {
              position: 'right',
              align: 'center',
              labels: {
                padding: 15,
                usePointStyle: true, // Use circle instead of rectangle
                pointStyle: 'circle',
                font: {
                  size: 13,
                  weight: 'bold',
                  family: "'Poppins', sans-serif"
                },
                generateLabels: function(chart) {
                  const originalLabels = Chart.defaults.plugins.legend.labels.generateLabels(chart);

                  // Add count to each label
                  originalLabels.forEach((label, i) => {
                    if (i < data.length) {
                      label.text = `${label.text} (${data[i]})`;
                    }
                  });

                  return originalLabels;
                }
              },
              title: {
                display: true,
                text: 'Categories',
                font: {
                  size: 16,
                  weight: 'bold'
                },
                padding: {
                  bottom: 15
                }
              }
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              titleColor: '#333',
              bodyColor: '#333',
              bodyFont: {
                size: 14,
                weight: 'bold'
              },
              borderColor: '#ddd',
              borderWidth: 1,
              cornerRadius: 8,
              padding: 12,
              boxPadding: 6,
              usePointStyle: true,
              callbacks: {
                // Show both the translated label and the count in the tooltip
                label: function(context) {
                  const label = context.label || '';
                  const value = context.raw || 0;
                  const sum = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                  const percentage = ((value as number) / sum) * 100;
                  return `${label}: ${value} complaints (${percentage.toFixed(1)}%)`;
                }
              }
            }
          },
          animation: {
            animateScale: true,
            animateRotate: true,
            duration: 1000,
            easing: 'easeOutQuart'
          }
        }
      });
    }
  }

  formatCategoryLabel(category: string): string {
    // Map of French category names to English translations
    const categoryTranslations: { [key: string]: string } = {
      'RETARD_LIVRAISON': 'Delivery Delay',
      'PRODUIT_ENDOMMAGE': 'Damaged Product',
      'MAUVAIS_SERVICE_CLIENT': 'Poor Customer Service',
      'ERREUR_FACTURATION': 'Billing Error',
      'ARTICLE_MANQUANT': 'Missing Item',
      'LIVRAISON_MAUVAISE_ADRESSE': 'Wrong Delivery Address',
      'MAUVAISE_QUALITE': 'Poor Quality',
      'COMPORTEMENT_PERSONNEL': 'Staff Behavior',
      'PROBLEME_TECHNIQUE': 'Technical Issue',
      'PROBLEME_REMBOURSEMENT': 'Refund Problem',
      'FRAIS_INATTENDUS': 'Unexpected Fees',
      'PUBLICITE_MENSONGERE': 'False Advertising',
      'SECURITE_HARCELEMENT': 'Security/Harassment',
      'ADVERTISEMENT': 'Advertisement',
      'AUTRE': 'Other'
    };

    // Return the English translation if available, otherwise format the original
    if (categoryTranslations[category]) {
      return categoryTranslations[category];
    }

    // Fallback formatting for any categories not in the map
    return category
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  }




  get filteredComplaints(): Complaint[] {
    let filtered = this.complaints;

    // Apply category filter if not ALL
    if (this.selectedCategory !== 'ALL') {
      filtered = filtered.filter(c => c.category === this.selectedCategory);
    }

    // Apply priority filter if not ALL
    if (this.selectedPriority !== 'ALL') {
      filtered = filtered.filter(c => c.priority === this.selectedPriority);
    }

    return filtered;
  }

  // New methods for reported ads

  getAdComplaintCounts(): void {
    this.complaintService.getAdComplaintCounts().subscribe({
      next: async (response) => {
        if (response instanceof Blob) {
          const text = await response.text();
          this.adComplaintCounts = JSON.parse(text);
        } else {
          this.adComplaintCounts = response;
        }
      },
      error: (err) => console.error("Error fetching ad complaint counts", err)
    });
  }

  loadReportedAds(): void {
    // Get all ads that have been reported
    const adIds = Object.keys(this.adComplaintCounts);

    if (adIds.length === 0) {
      this.reportedAds = [];
      this.setupTable();
      return;
    }

    // Load details for each reported ad
    this.reportedAds = [];

    // We'll use Promise.all to wait for all ad details to be loaded
    const adPromises = adIds.map(adId => {
      return new Promise<Ad>((resolve, reject) => {
        this.adService.getAdById(adId).subscribe({
          next: async (response) => {
            let ad: Ad;
            if (response instanceof Blob) {
              const text = await response.text();
              ad = JSON.parse(text);
            } else {
              ad = response;
            }

            // Add the report count to the ad object
            ad.reportCount = this.adComplaintCounts[adId];

            resolve(ad);
          },
          error: (err) => {
            console.error(`Error loading ad ${adId}`, err);
            reject(err);
          }
        });
      });
    });

    Promise.all(adPromises)
      .then(ads => {
        this.reportedAds = ads;
        this.setupTable();
      })
      .catch(err => {
        console.error("Error loading reported ads", err);
      });
  }

  openAdComplaintsPopup(adId: string, reportCount: number): void {
    this.dialog.open(AdComplaintsPopupComponent, {
      width: '600px',
      maxWidth: '95vw',
      data: { adId, reportCount }
    });
  }

  /**
   * Opens a dialog to view evidence files
   */
  viewEvidence(complaint: Complaint): void {
    console.log('Viewing evidence for complaint:', complaint);
    console.log('Evidence files:', complaint.evidence);

    // Create a simple dialog to display the evidence
    const dialogRef = this.dialog.open(EvidenceViewerDialogComponent, {
      width: '800px',
      maxWidth: '95vw',
      data: {
        evidence: complaint.evidence || [],
        complaintTitle: complaint.title || 'Complaint'
      }
    });
  }
}

/**
 * Simple dialog component to view evidence files
 */
@Component({
  selector: 'app-evidence-viewer-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <h2 mat-dialog-title class="dialog-title">
      <i class="fas fa-file-alt me-2"></i>Evidence for "{{ data.complaintTitle }}"
    </h2>
    <mat-dialog-content class="dialog-content">
      <div *ngIf="data.evidence && data.evidence.length > 0">
        <div class="evidence-count mb-3">
          <span class="badge bg-primary">{{ data.evidence.length }} file(s) attached</span>
        </div>

        <div *ngFor="let file of data.evidence; let i = index" class="evidence-item mb-4">
          <!-- For images, display them directly -->
          <div *ngIf="isImage(file)" class="text-center">
            <div class="image-container mb-3">
              <img [src]="file" class="img-fluid" alt="Evidence image">
            </div>
            <div class="action-buttons">
              <a [href]="file" target="_blank" mat-raised-button color="primary">
                <i class="fas fa-external-link-alt me-1"></i>Open in New Tab
              </a>
              <a [href]="file" download="evidence-image-{{i+1}}.jpg" mat-raised-button color="accent" class="ms-2">
                <i class="fas fa-download me-1"></i>Download
              </a>
            </div>
          </div>

          <!-- For other file types, show a download link -->
          <div *ngIf="!isImage(file)" class="text-center">
            <div class="file-icon mb-3">
              <i class="fas fa-file-alt fa-4x"></i>
            </div>
            <p class="file-label mb-3">File #{{ i+1 }}</p>
            <div class="action-buttons">
              <a [href]="file" download="evidence-file-{{i+1}}" mat-raised-button color="primary">
                <i class="fas fa-download me-1"></i>Download File
              </a>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="!data.evidence || data.evidence.length === 0" class="text-center py-5">
        <i class="fas fa-exclamation-circle fa-3x text-warning mb-3"></i>
        <h5>No Evidence Files</h5>
        <p class="text-muted">This complaint doesn't have any evidence files attached.</p>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-raised-button color="warn" mat-dialog-close>Close</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .dialog-title {
      background-color: #f5f5f5;
      padding: 16px;
      margin: -24px -24px 16px -24px;
      border-bottom: 1px solid #e0e0e0;
    }

    .dialog-content {
      max-height: 70vh;
      overflow-y: auto;
      padding: 0 16px;
    }

    .evidence-count {
      text-align: center;
    }

    .evidence-count .badge {
      font-size: 14px;
      padding: 8px 16px;
    }

    .evidence-item {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      background-color: #f9f9f9;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .image-container {
      max-height: 300px;
      overflow: hidden;
      border-radius: 4px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .image-container img {
      max-height: 300px;
      object-fit: contain;
    }

    .file-icon {
      color: #3f51b5;
      margin-bottom: 10px;
    }

    .file-label {
      font-weight: 500;
      color: #555;
    }

    .action-buttons {
      margin-top: 10px;
    }
  `]
})
export class EvidenceViewerDialogComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { evidence: string[], complaintTitle: string }
  ) {}

  isImage(src: string): boolean {
    return src.startsWith('data:image') ||
           src.endsWith('.jpg') ||
           src.endsWith('.jpeg') ||
           src.endsWith('.png') ||
           src.endsWith('.gif');
  }
}
