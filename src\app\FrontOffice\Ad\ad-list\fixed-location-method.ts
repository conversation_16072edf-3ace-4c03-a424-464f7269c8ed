/**
 * Use the current location for filtering
 */
useCurrentLocationForFilter(): void {
  console.log('Using current location for filtering');
  this.isLocationLoading = true;

  if (!navigator.geolocation) {
    alert('Geolocation is not supported by your browser.');
    this.isLocationLoading = false;
    return;
  }

  // Get the current position with improved options
  navigator.geolocation.getCurrentPosition(
    // Success callback
    (position) => {
      try {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;
        const accuracy = position.coords.accuracy;

        // Validate coordinates
        if (isNaN(lat) || isNaN(lng)) {
          throw new Error('Invalid coordinates received from geolocation API');
        }

        console.log('Current location:', { lat, lng, accuracy });
        console.log('Position accuracy:', accuracy, 'meters');

        // Store the location for future use
        this.currentLocation = {
          lat,
          lng,
          accuracy,
          source: 'browser-geolocation'
        };

        // Format coordinates with proper precision
        const coords = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

        // Update the location input field
        this.filterForm.patchValue({
          location: coords
        });

        // Apply filters
        this.applyFilters();
      } catch (innerError) {
        console.error('Error processing geolocation result:', innerError);
        this.handleLocationError('Error processing your location. Please try again.');
      }
    },
    // Error callback
    (error) => {
      console.error('Geolocation error:', error);

      let errorMessage = 'Could not get your current location.';

      // Provide specific error messages based on error code
      switch (error.code) {
        case 1: // PERMISSION_DENIED
          errorMessage = 'Location access was denied. Please enable location services in your browser settings.';
          break;
        case 2: // POSITION_UNAVAILABLE
          errorMessage = 'Location information is unavailable. Please try again later.';
          break;
        case 3: // TIMEOUT
          errorMessage = 'The request to get your location timed out. Please try again.';
          break;
        default:
          errorMessage = 'An unknown error occurred while trying to get your location.';
          break;
      }

      alert(errorMessage);
      this.isLocationLoading = false;
    },
    // Options
    {
      enableHighAccuracy: true,
      timeout: 15000, // Increased timeout for better reliability
      maximumAge: 0
    }
  );
}
