<app-header-front></app-header-front>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">Modify Ad</h2>

    <form [formGroup]="adForm" (ngSubmit)="updateAd()">

      <!-- Ad Title -->
      <div class="form-group mb-3">
        <label for="title" class="form-label fw-bold">Title</label>
        <input id="title" type="text" formControlName="title" class="form-control rounded" required>
      </div>

      <!-- Ad Description -->
      <div class="form-group mb-3">
        <label for="description" class="form-label fw-bold">Description</label>
        <textarea id="description" formControlName="description" class="form-control rounded" rows="4" required></textarea>
      </div>

      <!-- Ad Image Preview -->
      <div class="form-group mb-3">
        <label for="image" class="form-label fw-bold">Current Image</label>
        <div class="image-preview mb-2" *ngIf="adForm.get('image')?.value">
          <img [src]="'data:image/jpeg;base64,' + adForm.get('image')?.value"
               class="img-fluid rounded" style="max-height: 200px;" alt="Ad image">
        </div>
        <div *ngIf="!adForm.get('image')?.value" class="alert alert-info">
          No image available
        </div>
        <input type="hidden" id="image" formControlName="image">
      </div>

      <!-- New Image Upload -->
      <div class="form-group mb-3">
        <label for="newImage" class="form-label fw-bold">Upload New Image</label>
        <input type="file" id="newImage" class="form-control" (change)="onFileSelected($event)" accept="image/*">
        <small class="text-muted">Leave empty to keep the current image</small>
      </div>

      <!-- Ad Category -->
      <div class="form-group mb-3">
        <label for="category" class="form-label fw-bold">Category</label>
        <input id="category" type="text" formControlName="category" class="form-control rounded">
      </div>

      <!-- Price -->
      <div class="form-group mb-3">
        <label for="price" class="form-label fw-bold">Price</label>
        <div class="input-group">
          <input id="price" type="number" step="0.01" min="0" formControlName="price" class="form-control rounded" placeholder="Enter price">
          <span class="input-group-text">TND</span>
        </div>
        <small class="text-muted">Leave empty if not applicable</small>
      </div>

      <!-- Date and Time Selection -->
      <div class="row">
        <!-- Start Date -->
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="startDate" class="form-label fw-bold">Start Date</label>
            <input id="startDate" type="date" formControlName="startDate" class="form-control rounded" required>
          </div>
        </div>

        <!-- Start Time -->
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="startTime" class="form-label fw-bold">Start Time</label>
            <input id="startTime" type="time" formControlName="startTime" class="form-control rounded" required>
          </div>
        </div>
      </div>

      <div class="row">
        <!-- End Date -->
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="endDate" class="form-label fw-bold">End Date</label>
            <input id="endDate" type="date" formControlName="endDate" class="form-control rounded" required>
          </div>
        </div>

        <!-- End Time -->
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="endTime" class="form-label fw-bold">End Time</label>
            <input id="endTime" type="time" formControlName="endTime" class="form-control rounded" required>
          </div>
        </div>
      </div>

      <!-- Toggle for using End Date as Expiry Date -->
      <div class="form-group mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="useEndDateAsExpiry" formControlName="useEndDateAsExpiry">
          <label class="form-check-label fw-bold" for="useEndDateAsExpiry">
            Use End Date as Expiration Date
            <small class="text-muted ms-2">(When the ad will no longer be visible)</small>
          </label>
        </div>
        <small class="form-text text-muted">When enabled, the ad will automatically expire on the end date.</small>
      </div>

      <!-- Ad Status
      <div class="form-group mb-3">
        <label for="status" class="form-label fw-bold">Status</label>
        <select id="status" formControlName="status" class="form-control rounded">
          <option value="PENDING">Pending</option>
          <option value="APPROVED">Approved</option>
          <option value="REJECTED">Rejected</option>
        </select>
      </div>-->

      <!-- Submit & Cancel Buttons -->
      <div class="d-flex justify-content-center gap-3">
        <button type="submit" class="btn btn-primary btn-lg px-4" [disabled]="adForm.invalid || isSubmitting">
          <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
          <i *ngIf="!isSubmitting" class="fas fa-save"></i>
          {{ isSubmitting ? 'Updating...' : 'Update' }}
        </button>
        <button type="button" class="btn btn-secondary btn-lg px-4" (click)="cancelEdit()" [disabled]="isSubmitting">
          <i class="fas fa-times"></i> Cancel
        </button>
      </div>

    </form>

  </div>
</div>

<app-footer-front></app-footer-front>
