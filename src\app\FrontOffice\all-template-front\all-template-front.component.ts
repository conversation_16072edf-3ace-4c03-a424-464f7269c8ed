import { Compo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription, filter } from 'rxjs';
import { HttpClient, HttpClientModule } from '@angular/common/http';

import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { HeaderFrontComponent } from '../header-front/header-front.component';
import { FooterFrontComponent } from '../footer-front/footer-front.component';
import { AdComplaintDialogComponent } from '../complaint/ad-complaint-dialog/ad-complaint-dialog.component';
import { AdDetailDialogComponent } from '../Ad/ad-detail-dialog/ad-detail-dialog.component';

import { AdControllerService, Ad } from 'src/app/openapi';
import { ExtendedAd } from 'src/app/models/ad.model';
import { LikeService } from 'src/app/services/like.service';

@Component({
  selector: 'app-all-template-front',
  templateUrl: './all-template-front.component.html',
  styleUrls: ['./all-template-front.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    HeaderFrontComponent,
    FooterFrontComponent,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDialogModule,
    MatTooltipModule,
    MatSnackBarModule,
    HttpClientModule,
    AdDetailDialogComponent
  ],
  providers: [DatePipe, LikeService]
})
export class AllTemplateFrontComponent implements OnInit, OnDestroy {
  ads: ExtendedAd[] = [];
  private routerSubscription: Subscription | null = null;
  likedAds: Set<string> = new Set<string>();

  @ViewChild('carousel', { static: true }) carousel!: ElementRef<HTMLDivElement>;

  // Format and return the price with proper handling
  getPrice(ad: ExtendedAd): string {
    // Check if price exists and is a valid number
    if (ad.price !== undefined && ad.price !== null && !isNaN(Number(ad.price))) {
      // Format the price with 2 decimal places
      return `${Number(ad.price).toFixed(2)} TND`;
    } else {
      return '';
    }
  }

  constructor(
    private adService: AdControllerService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private datePipe: DatePipe,
    private likeService: LikeService,
    private http: HttpClient
  ) {}
  isLoggedIn = false;
  isFabMenuOpen = false;

  ngOnInit(): void {
    this.isLoggedIn = !!localStorage.getItem('token'); // ou adapte selon ton auth flow

    // Load ads initially
    this.loadAds();

    // Subscribe to router events to reload ads when navigating back to this component
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        // Check if we're on the home page
        if (this.router.url === '/' || this.router.url === '/home') {
          console.log('Reloading ads after navigation to home');
          this.loadAds();
        }
      });
  }

  ngOnDestroy(): void {
    // Clean up subscription when component is destroyed
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
      this.routerSubscription = null;
    }
  }

  /**
   * Load ads from the service
   */
  loadAds(): void {
    console.log('Home: Loading ads...');

    // Check if we have a recently updated ad
    const lastUpdatedAdId = localStorage.getItem('lastUpdatedAdId');
    if (lastUpdatedAdId) {
      console.log(`Home: Looking for recently updated ad: ${lastUpdatedAdId}`);
    }

    this.adService.getAllAds().subscribe({
      next: async (response: any) => {
        let allAds: Ad[] = [];

        try {
          if (response instanceof Blob) {
            const text = await response.text();
            allAds = JSON.parse(text);
          } else {
            allAds = response;
          }

          console.log('Home: All received ads:', allAds.length);

          // Filter approved ads and limit to 7
          const filteredAds = allAds
            .filter(ad => {
              const isApproved = ad.status === 'APPROVED';
              const isNotExpired = !ad.expiresAt || new Date(ad.expiresAt) > new Date();

              // Check if this is our recently updated ad
              if (ad.id === lastUpdatedAdId) {
                console.log('Home: Found recently updated ad:', ad);
              }

              return isApproved && isNotExpired;
            })
            .slice(0, 7);

          // Ensure price is properly parsed as a number and load like data
          this.ads = filteredAds.map(ad => {
            // Convert to ExtendedAd type
            const extendedAd = ad as ExtendedAd;

            // Convert price to number if it exists
            if (extendedAd.price !== undefined && extendedAd.price !== null) {
              extendedAd.price = Number(extendedAd.price);
            }

            // Load like data from localStorage and check if current user has liked this ad
            if (extendedAd.id) {
              const likeKey = `ad_like_${extendedAd.id}`;
              const storedLikeData = localStorage.getItem(likeKey);

              // Get current user ID
              const userId = localStorage.getItem('userId');

              // Create a unique key for this user's like on this ad
              const userLikeKey = userId ? `user_like_${userId}_ad_${extendedAd.id}` : null;

              // Check if this user has already liked this ad
              const hasUserLiked = userLikeKey ? localStorage.getItem(userLikeKey) === 'true' : false;

              if (storedLikeData) {
                try {
                  const likeData = JSON.parse(storedLikeData);
                  extendedAd._likeCount = likeData.count;

                  // Set the like status based on whether this specific user has liked the ad
                  extendedAd._likeStatus = hasUserLiked;

                  console.log(`Loaded like data for ad ${extendedAd.id}: count=${extendedAd._likeCount}, liked=${extendedAd._likeStatus}`);
                } catch (error) {
                  console.error(`Error parsing like data for ad ${extendedAd.id}:`, error);
                  extendedAd._likeCount = 0;
                  extendedAd._likeStatus = hasUserLiked;
                }
              } else {
                // Default values if no stored data
                extendedAd._likeCount = 0;
                extendedAd._likeStatus = hasUserLiked;
              }
            }

            return extendedAd;
          });

          // Debug logging for loaded ads
          console.log("Home page ads loaded:", this.ads.length);

          setTimeout(() => this.autoScroll(), 1000);
        } catch (error) {
          console.error('Home: Error processing response:', error);
        }
      },
      error: (err) => console.error('Home: Error loading ads', err)
    });
  }

  // Toggle FAB menu open/closed
  toggleFabMenu(): void {
    this.isFabMenuOpen = !this.isFabMenuOpen;
  }

  // Close FAB menu
  closeFabMenu(): void {
    this.isFabMenuOpen = false;
  }

  // Navigate to ads list page
  onAddAdClick(): void {
    this.closeFabMenu();
    const token = localStorage.getItem('token');
    if (token) {
      this.router.navigate(['/adlist']);
    } else {
      this.router.navigate(['/login']);
    }
  }

  // Navigate to complaints list page
  onAddComplaintClick(): void {
    this.closeFabMenu();
    const token = localStorage.getItem('token');
    if (token) {
      this.router.navigate(['/complaint']);
    } else {
      this.router.navigate(['/login']);
    }
  }


  scrollLeft(): void {
    this.scrollToOffset(-366); // Updated card width + gap
  }

  scrollRight(): void {
    this.scrollToOffset(366); // Updated card width + gap
  }


  // Store the interval ID so we can clear it if needed
  private autoScrollInterval: any = null;

  autoScroll(): void {
    // Clear any existing interval
    if (this.autoScrollInterval) {
      clearInterval(this.autoScrollInterval);
    }

    // Create a new interval with smoother scrolling
    this.autoScrollInterval = setInterval(() => {
      // Get the carousel element
      const el = this.carousel.nativeElement;

      // Check if we need to reset to the beginning
      if (el.scrollLeft >= el.scrollWidth - el.clientWidth - 50) {
        // Scroll to the beginning with a smooth animation
        this.scrollToIndex(0);
      } else {
        // Scroll right with a smooth animation
        this.scrollRight();
      }
    }, 10000); // Scroll every 10 seconds for a more relaxed pace
  }
  currentIndex = 0;

scrollToOffset(offset: number): void {
  const el = this.carousel.nativeElement;
  const start = el.scrollLeft;
  const duration = 3000; // Longer duration for smoother animation
  const startTime = performance.now();

  // Use easeInOutCubic for smoother animation
  const easeInOutCubic = (t: number): number => {
    return t < 0.5
      ? 4 * t * t * t
      : 1 - Math.pow(-2 * t + 2, 3) / 2;
  };

  const step = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    el.scrollLeft = start + offset * easeInOutCubic(progress);
    if (progress < 1) requestAnimationFrame(step);
  };

  requestAnimationFrame(step);
  this.syncIndexFromScroll();
}

updateIndex(): void {
  const el = this.carousel.nativeElement;
  const cardWidth = 350 + 16; // Updated card width + gap
  const newIndex = Math.round(el.scrollLeft / cardWidth);

  this.currentIndex = newIndex;
  this.updateArrowVisibility();
}

isDotActive(index: number): boolean {
  return index === this.currentIndex;
}

canScrollLeft = false;
canScrollRight = true;

updateArrowVisibility(): void {
  const el = this.carousel.nativeElement;
  const maxScroll = el.scrollWidth - el.clientWidth;
  this.canScrollLeft = el.scrollLeft > 0;
  this.canScrollRight = el.scrollLeft < maxScroll;
}

scrollToIndex(index: number): void {
  const el = this.carousel.nativeElement;
  const cardWidth = 350 + 16; // Updated card width + gap
  const target = index * cardWidth;
  const start = el.scrollLeft;
  const duration = 1000; // Longer duration for smoother animation
  const startTime = performance.now();

  // Use easeInOutCubic for smoother animation
  const easeInOutCubic = (t: number): number => {
    return t < 0.5
      ? 4 * t * t * t
      : 1 - Math.pow(-2 * t + 2, 3) / 2;
  };

  const step = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const ease = easeInOutCubic(progress);

    el.scrollLeft = start + (target - start) * ease;

    if (progress < 1) {
      requestAnimationFrame(step);
    }
  };

  requestAnimationFrame(step);
  this.currentIndex = index;
  this.updateArrowVisibility();
}

ngAfterViewInit(): void {
  this.carousel.nativeElement.addEventListener('scroll', () => {
    this.syncIndexFromScroll();
  });
}

syncIndexFromScroll(): void {
  const el = this.carousel.nativeElement;
  const cardWidth = 350 + 16; // Updated card width + gap
  this.currentIndex = Math.round(el.scrollLeft / cardWidth);
  this.updateArrowVisibility(); // Update arrow visibility when scrolling
}


  /**
   * Get the appropriate image from an ad
   * Handles both single image and multiple images
   */
  getAdImage(ad: ExtendedAd): string | null {
    // If ad has images array, return the first one
    if (ad.images && ad.images.length > 0) {
      return ad.images[0];
    }

    // If ad only has a single image, return it
    if (ad.image) {
      return ad.image;
    }

    // If no images, return null
    return null;
  }

  getCategoryClass(category: string): string {
    switch (category) {
      case 'CARPOOLING':
        return 'carpool-chip';
      case 'FASTPOST':
        return 'fastpost-chip';
      case 'PRODUCT':
        return 'product-chip';
      case 'OTHER':
        return 'other-chip';
      default:
        return '';
    }
  }

  openComplaintDialog(ad: ExtendedAd, event?: Event): void {
    // Stop event propagation if provided
    if (event) {
      event.stopPropagation();
    }

    if (!ad.id || !ad.title) {
      console.error('Cannot report ad: missing id or title');
      return;
    }

    const dialogRef = this.dialog.open(AdComplaintDialogComponent, {
      width: '450px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      panelClass: ['complaint-dialog-panel', 'stylish-dialog'],
      backdropClass: 'stylish-backdrop',
      autoFocus: false,
      position: { top: '50px' },
      data: {
        adId: ad.id,
        adTitle: ad.title
      }
    });

    // Add a class to the body to indicate a dialog is open (for additional styling)
    document.body.classList.add('dialog-open');

    dialogRef.afterClosed().subscribe(result => {
      // Remove the body class when dialog is closed
      document.body.classList.remove('dialog-open');

      if (result === true) {
        // Show success message using snackbar
        this.snackBar.open('Thank you for your report. We will review it shortly.', 'Close', {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: 'success-snackbar'
        });
      }
    });
  }

  /**
   * Format a date for display
   * @param dateString The date string to format
   * @returns Formatted date string
   */
  formatDate(dateString: string | undefined): string {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return this.datePipe.transform(date, 'MMM d, y, h:mm a') || 'N/A';
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  }



  /**
   * Update the like counts on ads
   */
  updateLikeCounts(): void {
    console.log('Updating like counts for ads...');
    console.log('Number of ads to update:', this.ads.length);

    // Load like data for each ad
    this.ads.forEach(ad => {
      if (ad.id) {
        console.log(`Fetching like data for ad ${ad.id} (${ad.title})`);

        this.likeService.getLikeData(ad.id).subscribe({
          next: (likeData) => {
            console.log(`Received like data for ad ${ad.id}:`, likeData);

            // Store previous values for comparison
            const prevLikeStatus = ad._likeStatus;
            const prevLikeCount = ad._likeCount;

            // Get current user ID
            const userId = localStorage.getItem('userId');

            // Create a unique key for this user's like on this ad
            const userLikeKey = userId ? `user_like_${userId}_ad_${ad.id}` : null;

            // Check if this user has already liked this ad
            const hasUserLiked = userLikeKey ? localStorage.getItem(userLikeKey) === 'true' : false;

            // Update with new values
            ad._likeCount = likeData.count;
            ad._likeStatus = hasUserLiked;

            // Force change detection
            this.ads = [...this.ads];

            console.log(`Updated ad ${ad.id} like status: ${prevLikeStatus} -> ${ad._likeStatus}`);
            console.log(`Updated ad ${ad.id} like count: ${prevLikeCount} -> ${ad._likeCount}`);
          },
          error: (error) => {
            console.error(`Error getting like data for ad ${ad.id}:`, error);
            // Set default values in case of error
            ad._likeStatus = false;
            ad._likeCount = 0;

            // Force change detection
            this.ads = [...this.ads];
          }
        });
      }
    });
  }

  /**
   * Get the like count for an ad
   */
  getLikeCount(ad: ExtendedAd): number {
    if (!ad.id) return 0;

    // Use nullish coalescing operator to provide a default value of 0
    return ad._likeCount ?? 0;
  }

  /**
   * Check if an ad is liked by the current user
   */
  isLiked(ad: ExtendedAd): boolean {
    if (!ad.id) return false;

    // Get current user ID
    const userId = localStorage.getItem('userId');
    if (!userId) return false;

    // Create a unique key for this user's like on this ad
    const userLikeKey = `user_like_${userId}_ad_${ad.id}`;

    // Check if this user has already liked this ad
    const hasUserLiked = localStorage.getItem(userLikeKey) === 'true';

    return hasUserLiked;
  }

  /**
   * Toggle like status for an ad
   */
  toggleLike(ad: ExtendedAd): void {
    console.log('Toggling like for ad:', ad.id);

    if (!this.isLoggedIn) {
      console.log('User not logged in, redirecting to login page');
      this.snackBar.open(
        'Please log in to like ads',
        'Login',
        {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: 'info-snackbar'
        }
      ).onAction().subscribe(() => {
        this.router.navigate(['/login']);
      });
      return;
    }

    if (!ad.id) {
      console.log('Ad ID is missing, cannot toggle like');
      return;
    }

    const userId = localStorage.getItem('userId');
    if (!userId) {
      console.log('User ID is missing, cannot toggle like');
      return;
    }

    // Initialize like count if undefined
    if (ad._likeCount === undefined) {
      ad._likeCount = 0;
    }

    // Create a unique key for this user's like on this ad
    const userLikeKey = `user_like_${userId}_ad_${ad.id}`;

    // Check if this user has already liked this ad
    const hasUserLiked = localStorage.getItem(userLikeKey) === 'true';

    console.log(`User ${userId} has ${hasUserLiked ? 'already liked' : 'not liked'} ad ${ad.id}`);

    // Get current like status for this user
    const isCurrentlyLiked = hasUserLiked;

    // DIRECT UPDATE: Immediately update the UI
    if (isCurrentlyLiked) {
      // If currently liked, decrement count and set to not liked
      ad._likeCount = Math.max(0, ad._likeCount - 1);
      ad._likeStatus = false;
      // Remove the record that this user liked this ad
      localStorage.removeItem(userLikeKey);
    } else {
      // If not currently liked, increment count and set to liked
      ad._likeCount = ad._likeCount + 1;
      ad._likeStatus = true;
      // Store that this user has liked this ad
      localStorage.setItem(userLikeKey, 'true');
    }

    console.log(`DIRECT UPDATE: Changed like status to ${ad._likeStatus}, count to ${ad._likeCount}`);

    // Force change detection to update the UI immediately
    this.ads = [...this.ads];

    // Save the updated like data to localStorage
    const likeKey = `ad_like_${ad.id}`;
    localStorage.setItem(likeKey, JSON.stringify({
      adId: ad.id,
      count: ad._likeCount,
      userLiked: ad._likeStatus
    }));

    // Show feedback to the user
    this.snackBar.open(
      ad._likeStatus ? 'You liked this ad! 💖' : 'You removed your like from this ad',
      'Close',
      {
        duration: 3000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom',
        panelClass: ad._likeStatus ? 'success-snackbar' : 'info-snackbar'
      }
    );

    // Also update the like service in the background
    this.likeService.toggleLike(ad.id).subscribe({
      next: (likeData) => {
        console.log('Like service updated:', likeData);

        // Send notification to ad owner when liked
        if (likeData.userLiked && ad.userId) {
          this.sendLikeNotification(ad);
        }
      },
      error: (error) => {
        console.error('Error updating like service:', error);

        // Even if the service update fails, try to send notification if the user liked the ad
        if (ad._likeStatus && ad.userId) {
          this.sendLikeNotification(ad);
        }
      }
    });
  }

  /**
   * Send a notification to the ad owner when their ad is liked
   */
  private async sendLikeNotification(ad: ExtendedAd): Promise<void> {
    try {
      // Get current user ID
      const currentUserId = localStorage.getItem('userId');

      if (!currentUserId || !ad.id || !ad.userId) {
        console.warn('Cannot send notification: missing user ID or ad information');
        return;
      }

      console.log(`Sending like notification to user ${ad.userId} for ad ${ad.id}`);

      // Get current user's name from localStorage if available
      const currentUserName = localStorage.getItem('userName') || 'A user';

      // Create notification data
      const notification = {
        title: 'New Like on Your Ad',
        message: `${currentUserName} liked your ad "${ad.title}"`,
        type: 'AD_LIKE',
        date: new Date(),
        userId: ad.userId, // Send notification to ad owner
        read: false,
        adId: ad.id // Include the ad ID for reference
      };

      // Send notification to backend using both possible API URLs
      const apiUrl = 'http://localhost:8089/speedygo';
      const fallbackApiUrl = 'http://localhost:8091/speedygo';

      // Try primary URL first
      this.http.post(`${apiUrl}/notification/createNotification`, notification)
        .subscribe({
          next: (response) => {
            console.log('Like notification sent successfully:', response);
          },
          error: (error) => {
            console.error('Error sending like notification to primary URL:', error);

            // Try fallback URL if primary fails
            this.http.post(`${fallbackApiUrl}/notification/createNotification`, notification)
              .subscribe({
                next: (response) => {
                  console.log('Like notification sent successfully using fallback URL:', response);
                },
                error: (fallbackError) => {
                  console.error('Error sending like notification using fallback URL:', fallbackError);
                }
              });
          }
        });
    } catch (error) {
      console.error('Error in sendLikeNotification:', error);
    }
  }

  /**
   * Contact the advertiser
   */
  contactAdvertiser(ad: ExtendedAd): void {
    if (!this.isLoggedIn) {
      this.router.navigate(['/login']);
      return;
    }

    // In a real implementation, you would open a contact form or chat
    // For now, we'll just show a message
    this.snackBar.open(
      `Contact request sent to the advertiser of "${ad.title}"`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom',
        panelClass: 'info-snackbar'
      }
    );
  }

  /**
   * Open ad details in a popup dialog instead of navigating to a new page
   * @param ad The ad to view details for
   * @param event Optional click event to stop propagation
   */
  viewAdDetails(ad: ExtendedAd, event?: Event): void {
    // Stop event propagation if provided
    if (event) {
      event.stopPropagation();
    }

    if (!ad.id) {
      console.error('Cannot view ad details: missing id');
      return;
    }

    // Open the ad details in a dialog
    const dialogRef = this.dialog.open(AdDetailDialogComponent, {
      width: '80vw',
      maxWidth: '800px',
      maxHeight: '85vh',
      panelClass: ['ad-detail-dialog-container', 'stylish-dialog'],
      data: { adId: ad.id },
      autoFocus: false,
      backdropClass: 'stylish-backdrop',
      position: { top: '50px' }
    });

    // Add a class to the body to indicate a dialog is open (for additional styling)
    document.body.classList.add('dialog-open');

    // Remove the class when the dialog is closed
    dialogRef.afterClosed().subscribe(() => {
      document.body.classList.remove('dialog-open');
    });
  }
}
