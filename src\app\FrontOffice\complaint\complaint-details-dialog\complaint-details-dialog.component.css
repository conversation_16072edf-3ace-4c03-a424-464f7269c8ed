/* Dialog Styling */
.complaint-details-dialog {
  max-width: 700px;
  width: 100%;
  animation: scaleIn 0.3s ease-out;
  padding: 20px;
}

/* Dialog Title */
h2[mat-dialog-title] {
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
  display: flex;
  align-items: center;
}

/* Dialog Content */
mat-dialog-content {
  min-height: 200px;
  max-height: 70vh;
  overflow-y: auto;
}

/* Loading Spinner */
mat-spinner {
  margin: 2rem auto;
}

/* Labels for Better Readability */
label {
  font-size: 16px;
  color: #2c3e50;
  font-weight: bold;
}

/* Complaint Detail Fields */
p {
  font-size: 16px;
  padding: 10px;
  border-radius: 8px;
}

/* Animations */
@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Custom scrollbar */
mat-dialog-content::-webkit-scrollbar {
  width: 8px;
}

mat-dialog-content::-webkit-scrollbar-track {
  background: rgba(241, 241, 241, 0.5);
  border-radius: 10px;
  margin: 10px 0;
}

mat-dialog-content::-webkit-scrollbar-thumb {
  background: #3498db;
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

mat-dialog-content::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
  border: 2px solid transparent;
  background-clip: padding-box;
}

/* Close button styling */
button.btn-secondary {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

button.btn-secondary:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Status Badge Colors */
.badge {
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
}

/* Section Titles */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
  display: flex;
  align-items: center;
}

/* Evidence Files Section */
.evidence-files {
  margin-top: 15px;
}

.evidence-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.evidence-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.evidence-image {
  width: 100%;
  text-align: center;
}

.evidence-image img {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
  border-radius: 4px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.evidence-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.evidence-file i {
  margin-bottom: 10px;
  color: #3498db;
}

.evidence-file p {
  margin: 0;
  font-size: 14px;
  color: #555;
}

/* Button styling for evidence actions */
.evidence-item button {
  margin-top: 10px;
  background-color: #3498db;
  color: white;
  border: none;
  transition: all 0.2s ease;
}

.evidence-item button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

/* Contact information styling */
.contact-info {
  margin-top: 15px;
}
