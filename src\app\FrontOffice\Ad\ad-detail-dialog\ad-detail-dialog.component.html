<div class="ad-detail-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 class="dialog-title">Ad Details</h2>
    <button class="close-button" (click)="closeDialog()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="loading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading ad details...</p>
  </div>

  <!-- Error Message -->
  <div class="error-container" *ngIf="error">
    <i class="fas fa-exclamation-circle error-icon"></i>
    <p>Failed to load ad details. Please try again later.</p>
    <button class="btn btn-primary" (click)="loadAdDetails()">Retry</button>
  </div>

  <!-- Ad Content -->
  <div class="ad-content" *ngIf="ad && !loading && !error">
    <!-- Category Badge -->
    <div class="category-badge" [ngClass]="getCategoryClass()">
      {{ ad.category }}
    </div>

    <!-- Price Badge -->
    <div class="price-tag">
      {{ getPrice() }}
    </div>

    <!-- Image Gallery -->
    <div class="image-gallery">
      <div class="image-container">
        <div *ngIf="getImages().length > 0; else noImage">
          <img [src]="'data:image/jpeg;base64,' + getCurrentImage()" alt="{{ ad.title }} image" class="ad-image">

          <!-- Image Navigation Controls -->
          <div class="image-controls" *ngIf="getImages().length > 1">
            <button class="nav-button prev-button" (click)="prevImage()">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button class="nav-button next-button" (click)="nextImage()">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <!-- Image Counter -->
          <div class="image-counter" *ngIf="getImages().length > 1">
            {{ currentImageIndex + 1 }} / {{ getImages().length }}
          </div>
        </div>

        <ng-template #noImage>
          <div class="no-image-placeholder">
            <i class="fas fa-image"></i>
            <p>No image available</p>
          </div>
        </ng-template>
      </div>
    </div>

    <!-- Ad Details -->
    <div class="ad-details">
      <!-- Basic Information Section -->
      <div class="detail-section">
        <h3 class="ad-title">{{ ad.title }}</h3>

        <div class="ad-metadata">
          <div class="metadata-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Posted: {{ getFormattedDate(ad.createdAt) }}</span>
          </div>

          <div class="metadata-item">
            <i class="fas fa-eye"></i>
            <span>Views: {{ ad.viewCount || 0 }}</span>
          </div>

          <!-- Like count will be added in future implementation -->
          <div class="metadata-item">
            <i class="fas fa-heart"></i>
            <span>Likes: 0</span>
          </div>
        </div>
      </div>

      <!-- Category Section -->
      <div class="detail-section">
        <h4 class="section-title">Category</h4>
        <div class="detail-content">
          <div class="category-display" [ngClass]="getCategoryClass()">
            <i class="fas" [ngClass]="getCategoryIcon()"></i>
            <span>{{ ad.category }}</span>
          </div>
        </div>
      </div>

      <!-- Price Section -->
      <div class="detail-section">
        <h4 class="section-title">Price</h4>
        <div class="detail-content">
          <div class="price-display">
            <i class="fas fa-tag"></i>
            <span>{{ getPrice() }}</span>
          </div>
        </div>
      </div>

      <!-- Timeline Section -->
      <div class="detail-section">
        <h4 class="section-title">Timeline</h4>
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-icon start-icon">
              <i class="fas fa-play-circle"></i>
            </div>
            <div class="timeline-content">
              <span class="timeline-date">{{ getFormattedDateTime(ad.startDate, ad.startTime) }}</span>
              <span class="timeline-label">Start</span>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-icon end-icon">
              <i class="fas fa-stop-circle"></i>
            </div>
            <div class="timeline-content">
              <span class="timeline-date">{{ getFormattedDateTime(ad.endDate, ad.endTime) }}</span>
              <span class="timeline-label">End</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Description Section -->
      <div class="detail-section">
        <h4 class="section-title">Description</h4>
        <div class="description-content">
          <p>{{ ad.description || 'No description provided.' }}</p>
        </div>
      </div>

      <!-- Tags Section -->
      <div class="detail-section" *ngIf="ad.tags && ad.tags.length > 0">
        <h4 class="section-title">Tags</h4>
        <div class="tags-container">
          <span class="tag-badge" *ngFor="let tag of getTagsArray()">
            <i class="fas fa-tag"></i> {{ tag }}
          </span>
        </div>
      </div>

      <!-- Location Section -->
      <div class="detail-section" *ngIf="ad.location && ad.location.length === 2">
        <h4 class="section-title">Location</h4>
        <div class="location-details">
          <div class="location-name">
            <i class="fas fa-map-marker-alt"></i>
            <span>{{ ad.locationName || 'Location Available' }}</span>
          </div>

          <div class="coordinates">
            <div class="coordinate">
              <span class="coordinate-label">Latitude:</span>
              <span class="coordinate-value">{{ ad.location[1] }}</span>
            </div>
            <div class="coordinate">
              <span class="coordinate-label">Longitude:</span>
              <span class="coordinate-value">{{ ad.location[0] }}</span>
            </div>
          </div>

          <button class="view-on-map-btn" (click)="viewOnMap()">
            <i class="fas fa-map"></i>
            View on Map
          </button>
        </div>
      </div>

      <!-- Status Section -->
      <div class="detail-section">
        <h4 class="section-title">Status</h4>
        <div class="status-display">
          <div class="status-badge" [ngClass]="getStatusClass()">
            <i class="fas" [ngClass]="getStatusIcon()"></i>
            <span>{{ getStatusText() }}</span>
          </div>

          <div class="expiry-info" *ngIf="ad.endDate">
            <i class="fas fa-clock"></i>
            <span>Expires on: {{ getFormattedDate(ad.endDate) }}</span>
          </div>
        </div>
      </div>

      <!-- Contact Section -->
      <div class="contact-section">
        <button class="contact-button">
          <i class="fas fa-envelope"></i>
          Contact Advertiser
        </button>

        <button class="view-full-button" [routerLink]="['/ad-details', ad.id]" (click)="closeDialog()">
          <i class="fas fa-external-link-alt"></i>
          View Full Page
        </button>
      </div>
    </div>
  </div>
</div>
