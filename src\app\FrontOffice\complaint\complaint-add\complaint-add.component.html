<app-header-front></app-header-front>

<div class="container mt-4 mb-5">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-3">Submit a Complaint</h2>

    <!-- Progress Indicator -->
    <div class="progress-container mb-4">
      <div class="progress-steps">
        <div class="step" [ngClass]="{'active': currentStep >= 1, 'completed': currentStep > 1}">
          <div class="step-number">1</div>
          <div class="step-label">Details</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" [ngClass]="{'active': currentStep >= 2, 'completed': currentStep > 2}">
          <div class="step-number">2</div>
          <div class="step-label">Evidence</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" [ngClass]="{'active': currentStep >= 3}">
          <div class="step-number">3</div>
          <div class="step-label">Contact</div>
        </div>
      </div>
    </div>

    <form [formGroup]="complaintForm" (ngSubmit)="onSubmit()">

      <!-- Step 1: Complaint Details -->
      <div class="form-step" [ngClass]="{'d-none': currentStep !== 1}">
        <h4 class="step-title mb-3">
          <i class="fas fa-info-circle text-primary me-2"></i>Complaint Details
        </h4>

        <!-- Complaint Title -->
        <div class="form-group mb-3">
          <label for="title" class="form-label fw-bold">Title</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-heading"></i></span>
            <input
              id="title"
              type="text"
              formControlName="title"
              class="form-control rounded-end"
              placeholder="Brief title of your complaint"
              [ngClass]="{'is-invalid': complaintForm.get('title')?.invalid && complaintForm.get('title')?.touched}"
              required>
          </div>
          <div class="character-counter" [ngClass]="{'text-danger': getCharacterCount('title') > getMaxCharacterCount('title')}">
            {{ getCharacterCount('title') }}/{{ getMaxCharacterCount('title') }}
          </div>
          <div *ngIf="complaintForm.get('title')?.invalid && complaintForm.get('title')?.touched" class="invalid-feedback d-block">
            <div *ngIf="complaintForm.get('title')?.errors?.['required']">Title is required.</div>
            <div *ngIf="complaintForm.get('title')?.errors?.['minlength']">Title must be at least 5 characters.</div>
            <div *ngIf="complaintForm.get('title')?.errors?.['maxlength']">Title cannot exceed 100 characters.</div>
          </div>
        </div>

        <!-- Complaint Description -->
        <div class="form-group mb-3">
          <label for="description" class="form-label fw-bold">Describe the problem</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-comment-alt"></i></span>
            <textarea
              id="description"
              formControlName="description"
              class="form-control rounded-end"
              rows="4"
              placeholder="Please provide details about your complaint..."
              [ngClass]="{'is-invalid': complaintForm.get('description')?.invalid && complaintForm.get('description')?.touched}"
              required></textarea>
          </div>
          <div class="character-counter" [ngClass]="{'text-danger': getCharacterCount('description') > getMaxCharacterCount('description')}">
            {{ getCharacterCount('description') }}/{{ getMaxCharacterCount('description') }}
          </div>
          <div *ngIf="complaintForm.get('description')?.invalid && complaintForm.get('description')?.touched" class="invalid-feedback d-block">
            <div *ngIf="complaintForm.get('description')?.errors?.['required']">Description is required.</div>
            <div *ngIf="complaintForm.get('description')?.errors?.['minlength']">Description must be at least 10 characters.</div>
            <div *ngIf="complaintForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500 characters.</div>
          </div>
        </div>

        <!-- Complaint Category -->
        <div class="form-group mb-3">
          <label for="category" class="form-label fw-bold">Category</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-tag"></i></span>
            <select
              id="category"
              formControlName="category"
              class="form-control rounded-end"
              [ngClass]="{'is-invalid': complaintForm.get('category')?.invalid && complaintForm.get('category')?.touched}">
              <option *ngFor="let category of categories" [value]="category.value">{{ category.label }}</option>
            </select>
          </div>
          <div *ngIf="complaintForm.get('category')?.invalid && complaintForm.get('category')?.touched" class="invalid-feedback d-block">
            <div *ngIf="complaintForm.get('category')?.errors?.['required']">Category is required.</div>
          </div>
        </div>

        <!-- Priority Selection -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Priority</label>
          <div class="priority-options">
            <div *ngFor="let priority of priorities" class="priority-option">
              <input
                type="radio"
                [id]="'priority-' + priority.value"
                [value]="priority.value"
                formControlName="priority"
                class="priority-radio">
              <label [for]="'priority-' + priority.value" class="priority-label" [attr.data-priority]="priority.value">
                <i class="fas"
                  [ngClass]="{
                    'fa-thermometer-quarter': priority.value === 'LOW',
                    'fa-thermometer-half': priority.value === 'MEDIUM',
                    'fa-thermometer-full': priority.value === 'HIGH'
                  }"></i>
                {{ priority.label }}
              </label>
            </div>
          </div>
        </div>

        <!-- Step Navigation -->
        <div class="d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-secondary" [routerLink]="['/complaint']">
            <i class="fas fa-times me-2"></i>Cancel
          </button>
          <button type="button" class="btn btn-primary" (click)="nextStep()" [disabled]="!isStepValid(1)">
            Next<i class="fas fa-arrow-right ms-2"></i>
          </button>
        </div>
      </div>

      <!-- Step 2: Evidence Upload -->
      <div class="form-step" [ngClass]="{'d-none': currentStep !== 2}">
        <h4 class="step-title mb-3">
          <i class="fas fa-file-upload text-primary me-2"></i>Upload Evidence (Optional)
        </h4>

        <!-- File Upload Area -->
        <div class="custom-file-upload" (click)="openFileSelector()">
          <input #fileInput type="file" (change)="onFileSelected($event)" style="display: none;" multiple accept="image/*,.pdf,.doc,.docx">
          <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt"></i>
          </div>
          <div class="upload-text">Drag & drop files here or click to browse</div>
          <div class="upload-hint">You can select up to 3 files (images or documents)</div>
        </div>

        <!-- Selected Files Preview -->
        <div class="selected-files mt-3" *ngIf="filePreviews.length > 0">
          <h5 class="mb-3">Selected Files:</h5>
          <div class="file-preview" *ngFor="let file of filePreviews; let i = index">
            <div class="file-icon">
              <i class="fas" [ngClass]="{
                'fa-file-image': file.type.includes('image'),
                'fa-file-pdf': file.type.includes('pdf'),
                'fa-file-word': file.type.includes('word') || file.type.includes('doc'),
                'fa-file': !file.type.includes('image') && !file.type.includes('pdf') && !file.type.includes('word') && !file.type.includes('doc')
              }"></i>
            </div>
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ file.size }}</div>
            </div>
            <button type="button" class="btn btn-sm btn-danger" (click)="removeFile(i)">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Step Navigation -->
        <div class="d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-secondary" (click)="prevStep()">
            <i class="fas fa-arrow-left me-2"></i>Back
          </button>
          <button type="button" class="btn btn-primary" (click)="nextStep()">
            Next<i class="fas fa-arrow-right ms-2"></i>
          </button>
        </div>
      </div>

      <!-- Step 3: Contact Information -->
      <div class="form-step" [ngClass]="{'d-none': currentStep !== 3}">
        <h4 class="step-title mb-3">
          <i class="fas fa-address-card text-primary me-2"></i>Contact Information
        </h4>

        <!-- Contact Preference -->
        <div class="form-group mb-3">
          <label class="form-label fw-bold">How would you like to be contacted?</label>
          <div class="contact-options">
            <div *ngFor="let option of contactPreferences" class="contact-option">
              <input
                type="radio"
                [id]="'contact-' + option.value"
                [value]="option.value"
                formControlName="contactPreference"
                class="contact-radio">
              <label [for]="'contact-' + option.value" class="contact-label">
                <i class="fas"
                  [ngClass]="{
                    'fa-envelope': option.value === 'EMAIL',
                    'fa-phone': option.value === 'PHONE',
                    'fa-comment-dots': option.value === 'EITHER'
                  }"></i>
                {{ option.label }}
              </label>
            </div>
          </div>
        </div>

        <!-- Email Address (shown conditionally) -->
        <div class="form-group mb-3" *ngIf="complaintForm.get('contactPreference')?.value === 'EMAIL' || complaintForm.get('contactPreference')?.value === 'EITHER'">
          <label for="contactEmail" class="form-label fw-bold">Email Address</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
            <input
              id="contactEmail"
              type="email"
              formControlName="contactEmail"
              class="form-control rounded-end"
              placeholder="<EMAIL>"
              [ngClass]="{'is-invalid': complaintForm.get('contactEmail')?.invalid && complaintForm.get('contactEmail')?.touched}">
          </div>
          <div *ngIf="complaintForm.get('contactEmail')?.invalid && complaintForm.get('contactEmail')?.touched" class="invalid-feedback d-block">
            <div *ngIf="complaintForm.get('contactEmail')?.errors?.['required']">Email address is required.</div>
            <div *ngIf="complaintForm.get('contactEmail')?.errors?.['email']">Please enter a valid email address.</div>
          </div>
        </div>

        <!-- Phone Number (shown conditionally) -->
        <div class="form-group mb-3" *ngIf="complaintForm.get('contactPreference')?.value === 'PHONE' || complaintForm.get('contactPreference')?.value === 'EITHER'">
          <label for="contactPhone" class="form-label fw-bold">Phone Number</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-phone"></i></span>
            <input
              id="contactPhone"
              type="tel"
              formControlName="contactPhone"
              class="form-control rounded-end"
              placeholder="+****************"
              [ngClass]="{'is-invalid': complaintForm.get('contactPhone')?.invalid && complaintForm.get('contactPhone')?.touched}">
          </div>
          <div *ngIf="complaintForm.get('contactPhone')?.invalid && complaintForm.get('contactPhone')?.touched" class="invalid-feedback d-block">
            <div *ngIf="complaintForm.get('contactPhone')?.errors?.['required']">Phone number is required.</div>
          </div>
        </div>

        <!-- Submission Section -->
        <div class="submission-section mt-4">
          <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            Your complaint will be reviewed by our team. You will be notified about any updates.
          </div>

          <!-- Loading Indicator -->
          <div *ngIf="isSubmitting" class="text-center my-3">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Submitting your complaint...</p>
          </div>

          <!-- Step Navigation -->
          <div class="d-flex justify-content-between mt-4">
            <button type="button" class="btn btn-secondary" (click)="prevStep()" [disabled]="isSubmitting">
              <i class="fas fa-arrow-left me-2"></i>Back
            </button>
            <button type="submit" class="btn btn-success btn-lg" [disabled]="complaintForm.invalid || isSubmitting">
              <i class="fas fa-paper-plane me-2"></i>Submit Complaint
            </button>
          </div>
        </div>
      </div>

    </form>

  </div>
</div>

<app-footer-front></app-footer-front>
