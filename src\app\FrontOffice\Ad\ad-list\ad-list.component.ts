import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Ad, AdControllerService } from 'src/app/openapi';
import { FooterFrontComponent } from '../../footer-front/footer-front.component';
import { HeaderFrontComponent } from '../../header-front/header-front.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { CreateAdComponent } from '../create-ad/create-ad.component'; // adapte le chemin si nécessaire
import { EditAdDialogComponent } from '../edit-ad-dialog/edit-ad-dialog.component';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { AdFilter, defaultFilter } from './ad-filter.model';
import { Subscription, filter, finalize } from 'rxjs';
import { NotificationService } from 'src/app/services/notification.service';
import { AuthService } from 'src/app/services/auth.service';
import { HttpClient } from '@angular/common/http';
import { AdComplaintDialogComponent } from '../../complaint/ad-complaint-dialog/ad-complaint-dialog.component';
import { LocationService, GeoLocation } from 'src/app/services/location.service';
import { AdDetailDialogComponent } from '../ad-detail-dialog/ad-detail-dialog.component';

@Component({
  selector: 'app-ad-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FooterFrontComponent,
    HeaderFrontComponent,
    RouterModule,
    MatDialogModule
  ],
  templateUrl: './ad-list.component.html',
  styleUrls: ['./ad-list.component.css']
})
export class AdListComponent implements OnInit, OnDestroy {
  ads: Ad[] = []; // Array to hold the advertisements
  filteredAds: Ad[] = []; // Array to hold filtered advertisements
  allAds: Ad[] = []; // Array to hold all advertisements (unfiltered)
  categories = Object.values(Ad.CategoryEnum);
  errorMessage: string = '';

  // Filter related properties
  filterForm!: FormGroup;
  showFilters: boolean = false;
  selectedCategories: string[] = [];
  currentFilter: AdFilter = { ...defaultFilter };
  isFiltering: boolean = false;
  searchQuery: string = '';

  // UI related properties
  likedAds: Set<string> = new Set(); // Set of ad IDs that are liked
  newAdsCount: number = 0; // Count of new ads today

  // Subscription to handle navigation events
  private routerSubscription: Subscription | null = null;

  // Format and return the price with proper handling
  getPrice(ad: Ad): string {
    // Check if price exists and is a valid number
    if (ad.price !== undefined && ad.price !== null && !isNaN(Number(ad.price))) {
      // Format the price with 2 decimal places
      return `${Number(ad.price).toFixed(2)} TND`;
    } else {
      return 'N/A';
    }
  }

  // Store the current user ID
  private currentUserId: string | null = null;
  // Store the user roles
  private userRoles: string[] = [];

  // Store the current location for filtering
  private currentLocation: GeoLocation | null = null;
  isLocationLoading: boolean = false;

  constructor(
    private adService: AdControllerService,
    public router: Router,
    private dialog: MatDialog,
    private sanitizer: DomSanitizer,
    private fb: FormBuilder,
    private notificationService: NotificationService,
    private authService: AuthService,
    private http: HttpClient,
    private locationService: LocationService
  ) {
    // Initialize the filter form
    this.initFilterForm();

    // Get the current user ID and roles
    this.loadUserInfo();
  }

  /**
   * Initialize the filter form with default values
   */
  initFilterForm(): void {
    this.filterForm = this.fb.group({
      priceMin: [null],
      priceMax: [null],
      dateFilter: [null],
      startDateFrom: [null],
      startDateTo: [null],
      endDateFrom: [null],
      endDateTo: [null],
      location: [null],
      locationRadius: [10]
    });

    // Subscribe to form value changes
    this.filterForm.valueChanges.subscribe(values => {
      this.currentFilter = {
        ...this.currentFilter,
        ...values
      };
    });

    // Subscribe to location radius changes to automatically apply filters
    this.filterForm.get('locationRadius')?.valueChanges.subscribe(() => {
      // Only apply filters if we have a location set
      if (this.filterForm.get('location')?.value) {
        this.applyFilters();
      }
    });
  }

  ngOnInit(): void {
    this.loadAds();
    this.initRandomLikes();

    // Subscribe to router events to reload ads when navigating back to this component
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        // Check if we're on the ad list page
        if (this.router.url === '/adlist') {
          console.log('Reloading ads after navigation');
          this.loadAds();
        }
      });
  }

  /**
   * Initialize random likes for demo purposes
   */
  initRandomLikes(): void {
    // Randomly like some ads for demo purposes
    setTimeout(() => {
      this.ads.forEach(ad => {
        if (ad.id && Math.random() > 0.7) {
          this.likedAds.add(ad.id);
        }
      });
    }, 1000);
  }

  ngOnDestroy(): void {
    // Clean up subscription when component is destroyed
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
      this.routerSubscription = null;
    }
  }

  ngAfterViewInit(): void {
    // Initialiser les carousels après le chargement des données
    setTimeout(() => {
      this.initCarousels();
    }, 1000);
  }

  /**
   * Initialise manuellement les carousels Bootstrap
   */
  initCarousels(): void {
    // Sélectionner tous les éléments carousel
    const carousels = document.querySelectorAll('.carousel');

    // Initialiser chaque carousel
    carousels.forEach(carouselEl => {
      // @ts-ignore - Bootstrap est chargé globalement
      const carousel = new bootstrap.Carousel(carouselEl, {
        interval: 5000,
        wrap: true,
        touch: true
      });
    });
  }


  loadAds(): void {
    this.isFiltering = true;
    console.log('Loading ads...');

    // Check if we have a recently updated ad
    const lastUpdatedAdId = localStorage.getItem('lastUpdatedAdId');
    const lastUpdateTime = localStorage.getItem('lastUpdateTime');

    if (lastUpdatedAdId) {
      console.log(`Looking for recently updated ad: ${lastUpdatedAdId}, updated at: ${lastUpdateTime}`);
    }

    // Use the existing getAllAds endpoint but filter out expired ads client-side
    this.adService.getAllAds().subscribe({
      next: async (response: any) => {
        let loadedAds: Ad[] = [];
        let allReceivedAds: Ad[] = [];

        try {
          if (response instanceof Blob) {
            const text = await response.text(); // Convertir Blob en texte
            allReceivedAds = JSON.parse(text);
          } else {
            allReceivedAds = response;
          }

          console.log('All received ads (before filtering):', allReceivedAds.length);

          // Log all ads to help with debugging
          allReceivedAds.forEach((ad: Ad, index: number) => {
            console.log(`Ad ${index + 1}: ID=${ad.id}, Title=${ad.title}, Status=${ad.status}`);

            // Check if this is our recently updated ad
            if (ad.id === lastUpdatedAdId) {
              console.log('Found recently updated ad:', ad);
            }
          });

          // Filter out expired ads and keep only approved ads
          loadedAds = allReceivedAds.filter((ad: Ad) => {
            const isApproved = ad.status === 'APPROVED';
            const isNotExpired = !ad.expiresAt || new Date(ad.expiresAt) > new Date();

            if (!isApproved) {
              console.log(`Ad ${ad.id} filtered out: not approved (status=${ad.status})`);
            }
            if (!isNotExpired && ad.expiresAt) {
              console.log(`Ad ${ad.id} filtered out: expired (expiresAt=${ad.expiresAt})`);
            }

            return isApproved && isNotExpired;
          });

          console.log('Filtered ads (approved and not expired):', loadedAds.length);
        } catch (error) {
          console.error('Error processing response:', error);
          this.errorMessage = 'Error processing server response.';
          this.isFiltering = false;
          return;
        }

        // Ensure price is properly parsed as a number
        this.allAds = loadedAds.map(ad => {
          // Convert price to number if it exists
          if (ad.price !== undefined && ad.price !== null) {
            ad.price = Number(ad.price);
          }
          return ad;
        });

        console.log("All ads loaded and processed:", this.allAds.length);

        // Apply any active filters
        this.applyFilters();

        // Clear the last updated ad info after successful load
        if (lastUpdatedAdId) {
          localStorage.removeItem('lastUpdatedAdId');
          localStorage.removeItem('lastUpdateTime');
        }

        this.isFiltering = false;

        // Initialiser les carousels après le chargement des données
        setTimeout(() => {
          this.initCarousels();
        }, 100);
      },
      error: (err: any) => {
        console.error('Error loading ads', err);
        this.errorMessage = 'Failed to load advertisements. Please try again later.';
        this.isFiltering = false;
      }
    });
  }

  /**
   * Apply filters to the ads
   */
  applyFilters(): void {
    console.log('Applying filters');
    this.isFiltering = true;

    // Start with all ads
    let result = [...this.allAds];

    // Apply category filter
    if (this.selectedCategories.length > 0) {
      result = result.filter(ad =>
        this.selectedCategories.includes(ad.category as string)
      );
    }

    // Apply price range filter
    const minPrice = this.filterForm.get('priceMin')?.value;
    const maxPrice = this.filterForm.get('priceMax')?.value;

    if (minPrice !== null && minPrice !== undefined) {
      result = result.filter(ad =>
        ad.price !== undefined && ad.price !== null && ad.price >= minPrice
      );
    }

    if (maxPrice !== null && maxPrice !== undefined) {
      result = result.filter(ad =>
        ad.price !== undefined && ad.price !== null && ad.price <= maxPrice
      );
    }

    // Apply date filter
    const dateFilter = this.filterForm.get('dateFilter')?.value;
    if (dateFilter) {
      const now = new Date();

      switch (dateFilter) {
        case 'last24h':
          // Ads created in the last 24 hours
          const yesterday = new Date(now);
          yesterday.setDate(yesterday.getDate() - 1);
          result = result.filter(ad =>
            ad.createdAt && new Date(ad.createdAt) >= yesterday
          );
          break;

        case 'lastWeek':
          // Ads created in the last week
          const lastWeek = new Date(now);
          lastWeek.setDate(lastWeek.getDate() - 7);
          result = result.filter(ad =>
            ad.createdAt && new Date(ad.createdAt) >= lastWeek
          );
          break;

        case 'lastMonth':
          // Ads created in the last month
          const lastMonth = new Date(now);
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          result = result.filter(ad =>
            ad.createdAt && new Date(ad.createdAt) >= lastMonth
          );
          break;

        case 'custom':
          // Custom date range for start date
          const startDateFrom = this.filterForm.get('startDateFrom')?.value;
          const startDateTo = this.filterForm.get('startDateTo')?.value;

          if (startDateFrom) {
            const fromDate = new Date(startDateFrom);
            result = result.filter(ad =>
              ad.startDate && new Date(ad.startDate) >= fromDate
            );
          }

          if (startDateTo) {
            const toDate = new Date(startDateTo);
            toDate.setHours(23, 59, 59, 999); // End of day
            result = result.filter(ad =>
              ad.startDate && new Date(ad.startDate) <= toDate
            );
          }

          // Custom date range for end date
          const endDateFrom = this.filterForm.get('endDateFrom')?.value;
          const endDateTo = this.filterForm.get('endDateTo')?.value;

          if (endDateFrom) {
            const fromDate = new Date(endDateFrom);
            result = result.filter(ad =>
              ad.endDate && new Date(ad.endDate) >= fromDate
            );
          }

          if (endDateTo) {
            const toDate = new Date(endDateTo);
            toDate.setHours(23, 59, 59, 999); // End of day
            result = result.filter(ad =>
              ad.endDate && new Date(ad.endDate) <= toDate
            );
          }
          break;
      }
    }

    // Apply location filter
    const location = this.filterForm.get('location')?.value;
    const locationRadius = this.filterForm.get('locationRadius')?.value || 10;

    // If no location filter is applied, update the filtered ads and return
    if (!location) {
      this.ads = result;
      console.log(`Filtered ads (no location filter): ${this.ads.length} of ${this.allAds.length}`);
      this.isFiltering = false;
      return;
    }

    console.log('Applying location filter with:', { location, radius: locationRadius });

    // If we already have the current location coordinates from useCurrentLocation
    if (this.currentLocation) {
      console.log('Using stored location coordinates:', this.currentLocation);

      // Apply the filter with the current location
      this.filterAdsByLocation(result, this.currentLocation, locationRadius);
      return;
    }

    // Check if the location input might be coordinates
    const coordsMatch = location.match(/^(-?\d+(\.\d+)?),\s*(-?\d+(\.\d+)?)$/);

    if (coordsMatch) {
      console.log('Location input appears to be coordinates, parsing...');

      // Parse coordinates from the input
      const lat = parseFloat(coordsMatch[1]);
      const lng = parseFloat(coordsMatch[3]);

      // Store the location for future use
      this.currentLocation = { lat, lng };

      console.log('Parsed coordinates:', { lat, lng });

      // Apply the filter with the parsed coordinates
      this.filterAdsByLocation(result, this.currentLocation, locationRadius);
      return;
    }

    // If input is not coordinates, try to geocode the location name
    console.log('Location input is not coordinates, geocoding location name:', location);
    this.isLocationLoading = true;

    // First try to parse the input as a location name
    this.locationService.geocodeLocation(location)
      .pipe(finalize(() => this.isLocationLoading = false))
      .subscribe({
        next: (geoLocation) => {
          if (geoLocation) {
            console.log('Successfully geocoded location:', geoLocation);

            // Store the location for future use
            this.currentLocation = geoLocation;

            // Apply the filter with the geocoded location
            this.filterAdsByLocation(result, geoLocation, locationRadius);
          } else {
            console.warn('Geocoding failed, falling back to text matching');

            // If geocoding fails, fall back to simple text matching
            this.filterAdsByLocationName(result, location);
          }
        },
        error: (error) => {
          console.error('Error geocoding location:', error);

          // If geocoding fails with an error, fall back to simple text matching
          this.filterAdsByLocationName(result, location);
        }
      });

    // Return early since we're handling the update asynchronously
    return;
  }


  /**
   * Filter ads by location coordinates
   * @param ads The list of ads to filter
   * @param location The location coordinates
   * @param radius The radius in kilometers
   */
  private filterAdsByLocation(ads: Ad[], location: GeoLocation, radius: number): void {
    console.log('Filtering ads by location coordinates:', location, 'with radius:', radius);

    // Count ads with location data for debugging
    const adsWithLocation = ads.filter(ad => ad.location && ad.location.length === 2).length;
    console.log(`Ads with location data: ${adsWithLocation} of ${ads.length}`);

    // Filter ads by distance using the Haversine formula
    const filteredAds = ads.filter(ad => {
      // Check if the ad has location data
      if (!ad.location || ad.location.length !== 2) {
        return false;
      }

      // Ad location is stored as [longitude, latitude]
      const adLng = ad.location[0];
      const adLat = ad.location[1];

      // Validate coordinates
      if (isNaN(adLat) || isNaN(adLng)) {
        console.warn(`Ad ${ad.id} has invalid coordinates:`, ad.location);
        return false;
      }

      // Calculate distance between current location and ad location
      const distance = this.locationService.calculateDistance(
        location.lat,
        location.lng,
        adLat,
        adLng
      );

      const isWithinRadius = distance <= radius;

      // Log detailed information for debugging
      if (isWithinRadius) {
        console.log(`Ad ${ad.id} included: distance ${distance.toFixed(2)} km (within ${radius} km radius)`);
      }

      // Return true if the ad is within the specified radius
      return isWithinRadius;
    });

    console.log(`Location filter applied: ${filteredAds.length} of ${ads.length} ads match`);

    // Update the filtered ads
    this.ads = filteredAds;
    this.isFiltering = false;

    // If no ads match, show a message
    if (filteredAds.length === 0) {
      console.warn('No ads found within the specified radius');
    }
  }

  /**
   * Filter ads by location name
   * @param ads The list of ads to filter
   * @param locationName The location name to filter by
   */
  private filterAdsByLocationName(ads: Ad[], locationName: string): void {
    console.log('Filtering ads by location name:', locationName);

    // Filter ads by location name
    const filteredAds = ads.filter(ad =>
      ad.locationName && ad.locationName.toLowerCase().includes(locationName.toLowerCase())
    );

    console.log(`Text filter applied: ${filteredAds.length} of ${ads.length} ads match`);

    // Update the filtered ads
    this.ads = filteredAds;
    this.isFiltering = false;

    // If no ads match, show a message
    if (filteredAds.length === 0) {
      console.warn('No ads found with the specified location name');
    }
  }

  deleteAd(adId: string): void {
    // Find the ad
    const ad = this.ads.find(a => a.id === adId);

    // Check if ad exists and user has permission
    if (!ad) {
      console.error('Ad not found');
      return;
    }

    // Check permissions
    if (!this.canEditOrDeleteAd(ad)) {
      console.error('Permission denied: You do not have permission to delete this ad');
      alert('You do not have permission to delete this ad');
      return;
    }

    if (confirm('Are you sure you want to delete this ad?')) {
      this.adService.deleteAd1(adId).subscribe({
        next: () => {
          this.ads = this.ads.filter(ad => ad.id !== adId); // Update the list by removing the deleted ad
          console.log('Ad successfully deleted');
        },
        error: (err) => console.error('Error deleting ad', err)
      });
    }
  }
  getStatusLabel(status: Ad.StatusEnum | undefined): string {
    switch (status) {
      case Ad.StatusEnum.Approved:
        return '✅ Approuvé';
      case Ad.StatusEnum.PendingAdminReview:
        return '🕒 En attente de validation';
      case Ad.StatusEnum.Rejected:
        return '❌ Rejeté';
      default:
        return '❓ Inconnu';
    }
  }

  /**
   * Reload ads with a loading indicator
   */
  reloadAds(): void {
    console.log('Manually reloading ads...');
    // Clear any stored ad ID to ensure we're getting fresh data
    localStorage.removeItem('lastUpdatedAdId');
    localStorage.removeItem('lastUpdateTime');

    // Show loading indicator
    this.isFiltering = true;

    // Load ads
    this.loadAds();
  }

  openCreateAdDialog(): void {
    // Add a class to the body to indicate a dialog is open
    document.body.classList.add('dialog-open');

    // Hide the main content completely
    const mainContent = document.querySelector('.ads-page-wrapper');
    if (mainContent) {
      (mainContent as HTMLElement).style.display = 'none';
    }

    const dialogRef = this.dialog.open(CreateAdComponent, {
      width: '100vw',
      height: '100vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      panelClass: ['fullscreen-dialog', 'create-ad-dialog'],
      disableClose: false,
      backdropClass: 'fullscreen-backdrop',
      autoFocus: false,
      hasBackdrop: true
    });

    dialogRef.afterClosed().subscribe(result => {
      // Remove the class when dialog is closed
      document.body.classList.remove('dialog-open');

      // Show the main content again
      if (mainContent) {
        (mainContent as HTMLElement).style.display = '';
      }

      if (result) {
        this.loadAds(); // Reload ads if a new ad was created
      }
    });
  }


  // Open Ad Details Dialog
  goToAdDetails(adId: string): void {
    // Open the ad details in a dialog
    const dialogRef = this.dialog.open(AdDetailDialogComponent, {
      width: '90vw',
      maxWidth: '1000px',
      maxHeight: '90vh',
      panelClass: 'ad-detail-dialog-container',
      data: { adId: adId },
      autoFocus: false
    });

    // Add a class to the body to indicate a dialog is open (for additional styling)
    document.body.classList.add('dialog-open');

    // Remove the class when the dialog is closed
    dialogRef.afterClosed().subscribe(() => {
      document.body.classList.remove('dialog-open');
    });
  }

  // Open Edit Ad Dialog
  editAd(adId: string): void {
    // Find the ad
    const ad = this.ads.find(a => a.id === adId);

    // Check if ad exists and user has permission
    if (!ad) {
      console.error('Ad not found');
      return;
    }

    // Check permissions
    if (!this.canEditOrDeleteAd(ad)) {
      console.error('Permission denied: You do not have permission to edit this ad');
      alert('You do not have permission to edit this ad');
      return;
    }

    // Add a class to the body to indicate a dialog is open
    document.body.classList.add('dialog-open');

    // Open the edit dialog instead of navigating to a new page
    const dialogRef = this.dialog.open(EditAdDialogComponent, {
      width: '90vw',
      maxWidth: '900px',
      maxHeight: '90vh',
      panelClass: ['isolated-dialog', 'edit-ad-dialog'],
      disableClose: false,
      backdropClass: 'isolated-backdrop',
      autoFocus: false,
      data: { adId: adId },
      position: { top: '50px' },
      hasBackdrop: true
    });

    // Remove the class when the dialog is closed
    dialogRef.afterClosed().subscribe(result => {
      document.body.classList.remove('dialog-open');
      if (result) {
        this.loadAds(); // Reload ads if the ad was updated
      }
    });
  }

  /**
   * Get all images from an ad
   * Returns an array of image strings (base64)
   */
  getImages(ad: Ad): string[] {
    try {
      // If ad has images array and it's not empty, process it
      if (ad.images && Array.isArray(ad.images) && ad.images.length > 0) {
        // Filter out any invalid or corrupted image data
        const validImages = ad.images.filter(img => {
          // Check if image is a valid string and has reasonable length
          if (!img || typeof img !== 'string') return false;

          // Verify it's a valid base64 string (basic check)
          try {
            // Check if it has a reasonable length for a base64 image
            if (img.length < 100) return false;

            // Check if it contains invalid characters for base64
            if (!/^[A-Za-z0-9+/=]+$/.test(img)) return false;

            return true;
          } catch (e) {
            return false;
          }
        });

        if (validImages.length > 0) {
          return validImages;
        }
      }

      // If ad only has a single image and it's valid, return it as an array
      if (ad.image && typeof ad.image === 'string') {
        // Verify it's a valid base64 string (basic check)
        try {
          // Check if it has a reasonable length for a base64 image
          if (ad.image.length < 100) return [];

          // Check if it contains invalid characters for base64
          if (!/^[A-Za-z0-9+/=]+$/.test(ad.image)) return [];

          return [ad.image];
        } catch (e) {
          return [];
        }
      }

      // If no valid images found, return empty array
      return [];
    } catch (error) {
      console.error('Error processing images for ad:', ad.id, error);
      return [];
    }
  }

  /**
   * Get the carousel ID for a specific ad
   * This ensures each carousel has a unique ID
   */
  getCarouselId(adId: string | undefined): string {
    return `adCarousel_${adId || Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Safely generate an image URL from a base64 string
   * Handles potential errors and provides fallback
   */
  getSafeImageUrl(base64String: string): string {
    try {
      // Verify it's a valid base64 string
      if (!base64String || typeof base64String !== 'string' || base64String.length < 100) {
        return 'assets/images/placeholder-image.jpg';
      }

      // Check if it contains invalid characters for base64
      if (!/^[A-Za-z0-9+/=]+$/.test(base64String)) {
        return 'assets/images/placeholder-image.jpg';
      }

      return `data:image/jpeg;base64,${base64String}`;
    } catch (error) {
      console.error('Error generating image URL:', error);
      return 'assets/images/placeholder-image.jpg';
    }
  }

  /**
   * Get the CSS class for a category badge
   * @param category The category of the ad
   * @returns The CSS class for the category badge
   */
  getCategoryClass(category: string | undefined): string {
    if (!category) return 'category-other';

    switch (category.toUpperCase()) {
      case 'CARPOOLING':
        return 'category-carpooling';
      case 'FASTPOST':
        return 'category-fastpost';
      case 'PRODUCT':
        return 'category-product';
      default:
        return 'category-other';
    }
  }

  /**
   * Toggle the visibility of the filter section
   */
  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  /**
   * Handle category checkbox changes
   */
  onCategoryChange(event: any): void {
    const category = event.target.value;
    const isChecked = event.target.checked;

    if (isChecked) {
      // Add category to selected categories if not already present
      if (!this.selectedCategories.includes(category)) {
        this.selectedCategories.push(category);
      }
    } else {
      // Remove category from selected categories
      this.selectedCategories = this.selectedCategories.filter(c => c !== category);
    }
  }

  /**
   * Remove a category from the selected categories
   */
  removeCategory(category: string): void {
    this.selectedCategories = this.selectedCategories.filter(c => c !== category);

    // Also uncheck the checkbox
    const checkbox = document.getElementById(`category-${category}`) as HTMLInputElement;
    if (checkbox) {
      checkbox.checked = false;
    }

    // Apply filters
    this.applyFilters();
  }

  /**
   * Clear the price filter
   */
  clearPriceFilter(): void {
    this.filterForm.patchValue({
      priceMin: null,
      priceMax: null
    });

    // Apply filters
    this.applyFilters();
  }

  /**
   * Clear the date filter
   */
  clearDateFilter(): void {
    this.filterForm.patchValue({
      dateFilter: null,
      startDateFrom: null,
      startDateTo: null,
      endDateFrom: null,
      endDateTo: null
    });

    // Apply filters
    this.applyFilters();
  }

  /**
   * Clear the location filter
   */
  clearLocationFilter(): void {
    this.filterForm.patchValue({
      location: null,
      locationRadius: 10
    });

    // Clear the stored location
    this.currentLocation = null;

    // Apply filters
    this.applyFilters();
  }

  // Method removed to fix duplicate implementation

  /**
   * Reset all filters to their default values
   */
  resetFilters(): void {
    // Reset form
    this.filterForm.reset({
      locationRadius: 10
    });

    // Reset selected categories
    this.selectedCategories = [];

    // Uncheck all category checkboxes
    this.categories.forEach(category => {
      const checkbox = document.getElementById(`category-${category}`) as HTMLInputElement;
      if (checkbox) {
        checkbox.checked = false;
      }
    });

    // Reset current filter
    this.currentFilter = { ...defaultFilter };

    // Clear the stored location
    this.currentLocation = null;

    // Apply filters (show all ads)
    this.ads = [...this.allAds];
  }

  /**
   * Check if there are any active filters
   */
  hasActiveFilters(): boolean {
    return (
      this.selectedCategories.length > 0 ||
      this.filterForm.get('priceMin')?.value !== null ||
      this.filterForm.get('priceMax')?.value !== null ||
      this.filterForm.get('dateFilter')?.value !== null ||
      this.filterForm.get('location')?.value !== null
    );
  }

  /**
   * Get a human-readable label for the date filter
   */
  getDateFilterLabel(): string {
    const dateFilter = this.filterForm.get('dateFilter')?.value;

    switch (dateFilter) {
      case 'last24h':
        return 'Last 24 hours';
      case 'lastWeek':
        return 'Last week';
      case 'lastMonth':
        return 'Last month';
      case 'custom':
        return 'Custom range';
      default:
        return '';
    }
  }

  // Method removed to fix duplicate implementation

  // Method removed to fix duplicate implementation

  // Method removed to fix duplicate implementation

  // Method removed to fix duplicate implementation

  /**
   * Use the current location for location filter
   */
  useCurrentLocation(): void {
    try {
      if (!navigator.geolocation) {
        alert('Geolocation is not supported by this browser.');
        return;
      }

      this.isLocationLoading = true;

      // Log that we're attempting to get the current location
      console.log('Attempting to get current location...');

      // Use high accuracy option and longer timeout
      const options = {
        enableHighAccuracy: true,
        timeout: 15000, // Increased timeout for better reliability
        maximumAge: 0
      };

      // Wrap the geolocation call in a try-catch block
      try {
        navigator.geolocation.getCurrentPosition(
          // Success callback
          (position) => {
            try {
              const lat = position.coords.latitude;
              const lng = position.coords.longitude;

              // Validate coordinates
              if (isNaN(lat) || isNaN(lng)) {
                throw new Error('Invalid coordinates received from geolocation API');
              }

              console.log('Successfully obtained coordinates:', { lat, lng });
              console.log('Position accuracy:', position.coords.accuracy, 'meters');

              // Store the current location for filtering
              this.currentLocation = { lat, lng };

              // Try multiple geocoding services for better reliability
              this.reverseGeocode(lat, lng);
            } catch (innerError) {
              console.error('Error processing geolocation result:', innerError);
              this.handleLocationError('Error processing your location. Please try again.');
            }
          },

          // Error callback
          (error) => {
            console.error('Geolocation error:', error);

            if (error && typeof error.code === 'number') {
              console.error('Error code:', error.code);

              let errorMessage = 'Could not get your current location.';

              // Provide specific error messages based on error code
              switch(error.code) {
                case 1: // PERMISSION_DENIED
                  errorMessage = 'Location access was denied. Please enable location services in your browser settings.';
                  break;
                case 2: // POSITION_UNAVAILABLE
                  errorMessage = 'Location information is unavailable. Please try again later.';
                  break;
                case 3: // TIMEOUT
                  errorMessage = 'The request to get your location timed out. Please try again.';
                  break;
                default:
                  errorMessage = 'An unknown error occurred while trying to get your location.';
                  break;
              }

              this.handleLocationError(errorMessage);
            } else {
              this.handleLocationError('An unexpected error occurred while getting your location.');
            }
          },
          options
        );
      } catch (geoError) {
        console.error('Exception during geolocation call:', geoError);
        this.handleLocationError('Failed to access location services. Please try again later.');
      }
    } catch (outerError) {
      console.error('Unexpected error in useCurrentLocation:', outerError);
      alert('An unexpected error occurred. Please try again later.');
      this.isLocationLoading = false;
    }
  }

  /**
   * Handle location errors consistently
   */
  private handleLocationError(message: string): void {
    console.error(message);
    alert(message);
    this.isLocationLoading = false;
  }

  /**
   * Try multiple geocoding services to get the location name from coordinates
   */
  private reverseGeocode(lat: number, lng: number): void {
    try {
      console.log('Starting reverse geocoding for coordinates:', { lat, lng });

      // Set a timeout to ensure we don't wait too long for geocoding
      const geocodeTimeout = setTimeout(() => {
        if (this.isLocationLoading) {
          console.warn('Geocoding timeout reached, using coordinates directly');
          this.useGeocodeApiFallback(lat, lng);
        }
      }, 8000); // 8 second timeout

      // First try Nominatim (OpenStreetMap)
      const nominatimUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`;

      // Create an abort controller for the fetch request
      const controller = new AbortController();
      const signal = controller.signal;

      // Set a timeout to abort the fetch if it takes too long
      const fetchTimeout = setTimeout(() => {
        controller.abort();
      }, 7000); // 7 second timeout

      fetch(nominatimUrl, {
        headers: {
          'Accept-Language': 'en', // Request English results
          'User-Agent': 'SpeedyGoApp' // Identify our application
        },
        signal: signal
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`Nominatim API returned ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          // Clear the timeouts since we got a response
          clearTimeout(geocodeTimeout);
          clearTimeout(fetchTimeout);

          console.log('Nominatim geocoding response:', data);

          if (data && data.display_name) {
            // Update the form with the location name
            this.filterForm.patchValue({
              location: data.display_name
            });

            this.currentLocation = {
              lat,
              lng,
              name: data.display_name
            };

            console.log('Successfully geocoded to:', data.display_name);
            this.isLocationLoading = false;

            // Apply filters automatically when location is set
            this.applyFilters();
          } else {
            console.warn('Nominatim returned no display_name, trying fallback...');
            this.useGeocodeApiFallback(lat, lng);
          }
        })
        .catch(error => {
          // Clear the timeouts since we got an error response
          clearTimeout(geocodeTimeout);
          clearTimeout(fetchTimeout);

          console.error('Error with Nominatim geocoding:', error);
          this.useGeocodeApiFallback(lat, lng);
        });
    } catch (error) {
      console.error('Unexpected error in reverseGeocode:', error);
      // Fall back to using coordinates directly
      this.useGeocodeApiFallback(lat, lng);
    }
  }

  /**
   * Fallback to a different geocoding service if Nominatim fails
   */
  private useGeocodeApiFallback(lat: number, lng: number): void {
    try {
      console.log('Using geocoding fallback for coordinates:', { lat, lng });

      // Format coordinates with proper precision
      const coords = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

      // Update the form with the coordinates
      this.filterForm.patchValue({
        location: coords
      });

      // Store the location for filtering
      this.currentLocation = {
        lat,
        lng,
        name: coords // Use the coordinates string as the name
      };

      // End the loading state
      this.isLocationLoading = false;

      console.log('Using raw coordinates as fallback:', coords);

      // Apply filters automatically when location is set
      this.applyFilters();
    } catch (error) {
      console.error('Error in geocode fallback:', error);
      this.handleLocationError('Error processing location. Using default settings.');

      // Reset location filter
      this.clearLocationFilter();
    }
  }

  /**
   * Get the number of new ads today
   * Returns the actual count of ads created today
   */
  getNewAdsCount(): number {
    // Get today's date at midnight (start of the day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Count ads created today
    const newAdsCount = this.allAds.filter(ad => {
      if (!ad.createdAt) return false;
      const createdDate = new Date(ad.createdAt);
      return createdDate >= today;
    }).length;

    return newAdsCount;
  }

  /**
   * Handle search input
   */
  onSearchInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchQuery = input.value.trim().toLowerCase();

    if (this.searchQuery) {
      // Filter ads by title or description
      this.ads = this.allAds.filter(ad =>
        (ad.title && ad.title.toLowerCase().includes(this.searchQuery)) ||
        (ad.description && ad.description.toLowerCase().includes(this.searchQuery))
      );
    } else {
      // If search is cleared, apply other active filters
      this.applyFilters();
    }
  }

  /**
   * Select all categories (clear category filter)
   */
  selectAllCategories(): void {
    this.selectedCategories = [];

    // Uncheck all category checkboxes
    this.categories.forEach(category => {
      const checkbox = document.getElementById(`category-${category}`) as HTMLInputElement;
      if (checkbox) {
        checkbox.checked = false;
      }
    });

    this.applyFilters();
  }

  /**
   * Toggle a category selection
   */
  toggleCategory(category: string): void {
    const index = this.selectedCategories.indexOf(category);

    if (index === -1) {
      // Add category
      this.selectedCategories.push(category);
    } else {
      // Remove category
      this.selectedCategories.splice(index, 1);
    }

    this.applyFilters();
  }

  /**
   * Sort ads by different criteria
   */
  sortAds(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const sortBy = select.value;

    switch (sortBy) {
      case 'newest':
        this.ads.sort((a, b) => {
          return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
        });
        break;

      case 'oldest':
        this.ads.sort((a, b) => {
          return new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime();
        });
        break;

      case 'priceAsc':
        this.ads.sort((a, b) => {
          const priceA = a.price !== undefined && a.price !== null ? Number(a.price) : Infinity;
          const priceB = b.price !== undefined && b.price !== null ? Number(b.price) : Infinity;
          return priceA - priceB;
        });
        break;

      case 'priceDesc':
        this.ads.sort((a, b) => {
          const priceA = a.price !== undefined && a.price !== null ? Number(a.price) : -Infinity;
          const priceB = b.price !== undefined && b.price !== null ? Number(b.price) : -Infinity;
          return priceB - priceA;
        });
        break;
    }
  }

  /**
   * Check if an ad is liked
   */
  isLiked(ad: Ad): boolean {
    return ad.id ? this.likedAds.has(ad.id) : false;
  }

  /**
   * Toggle like status for an ad
   */
  toggleLike(ad: Ad, event: Event): void {
    event.stopPropagation(); // Prevent event bubbling

    if (!ad.id) return;

    const wasLiked = this.likedAds.has(ad.id);

    if (wasLiked) {
      this.likedAds.delete(ad.id);
    } else {
      this.likedAds.add(ad.id);
      // Send notification to ad owner when liked
      this.sendLikeNotification(ad);
    }
  }

  /**
   * Send a notification to the ad owner when their ad is liked
   */
  async sendLikeNotification(ad: Ad): Promise<void> {
    try {
      // Get current user ID
      const currentUserId = await this.getCurrentUserId();

      if (!currentUserId || !ad.id || !ad.userId) {
        console.warn('Cannot send notification: missing user ID or ad information');
        return;
      }

      // Create notification data
      const notification = {
        title: 'New Like on Your Ad',
        message: `Someone liked your ad "${ad.title}"`,
        type: 'AD_LIKE',
        date: new Date(),
        userId: ad.userId, // Send notification to ad owner
        read: false
      };

      // Send notification to backend
      this.http.post(`${this.notificationService['apiUrl']}/notification/createNotification`, notification)
        .subscribe({
          next: (response) => {
            console.log('Like notification sent successfully:', response);
          },
          error: (error) => {
            console.error('Error sending like notification:', error);

            // Try fallback URL if primary fails
            this.http.post(`${this.notificationService['fallbackApiUrl']}/notification/createNotification`, notification)
              .subscribe({
                next: (response) => {
                  console.log('Like notification sent successfully using fallback URL:', response);
                },
                error: (fallbackError) => {
                  console.error('Error sending like notification using fallback URL:', fallbackError);
                }
              });
          }
        });
    } catch (error) {
      console.error('Error in sendLikeNotification:', error);
    }
  }

  /**
   * Get the current user ID
   */
  private async getCurrentUserId(): Promise<string> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();

      if (isLoggedIn) {
        // Get the user info from Keycloak token
        const token = this.authService.getToken();
        if (token) {
          // Extract user ID from the token if possible
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            if (tokenData && tokenData.sub) {
              return tokenData.sub;
            }
          } catch (e) {
            console.warn('Error parsing token:', e);
          }
        }

        // Try to get user profile
        const userProfile = await this.authService.getUserProfile();
        if (userProfile && userProfile.email) {
          return userProfile.email;
        }

        // If we couldn't get the user ID from Keycloak, use a default
        return 'test-user-id';
      } else {
        return 'anonymous-user';
      }
    } catch (error) {
      console.warn('Could not get user ID from auth service, using default');
      return 'test-user-id';
    }
  }

  /**
   * Get a random avatar URL
   */
  getRandomAvatar(): string {
    // Return a random avatar from a public service
    const randomId = Math.floor(Math.random() * 70) + 1;
    return `https://i.pravatar.cc/150?img=${randomId}`;
  }

  /**
   * Format the start date and time of an ad
   * @param ad The ad to format the start date and time for
   * @returns Formatted start date and time string
   */
  getFormattedStartDateTime(ad: Ad): string {
    if (!ad.startDate) {
      return 'N/A';
    }

    const startDate = new Date(ad.startDate);
    const startTime = ad.startTime || '00:00';

    return `${startDate.toLocaleDateString()} ${startTime}`;
  }

  /**
   * Format the end date and time of an ad
   * @param ad The ad to format the end date and time for
   * @returns Formatted end date and time string
   */
  getFormattedEndDateTime(ad: Ad): string {
    if (!ad.endDate) {
      return 'N/A';
    }

    const endDate = new Date(ad.endDate);
    const endTime = ad.endTime || '00:00';

    return `${endDate.toLocaleDateString()} ${endTime}`;
  }

  /**
   * Load user information (ID and roles)
   */
  async loadUserInfo(): Promise<void> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();

      if (isLoggedIn) {
        // Get the user ID
        this.currentUserId = await this.getCurrentUserId();

        // Get user roles
        const token = this.authService.getToken();
        if (token) {
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            if (tokenData && tokenData.realm_access && tokenData.realm_access.roles) {
              this.userRoles = tokenData.realm_access.roles;
              console.log('User roles:', this.userRoles);
            }
          } catch (e) {
            console.warn('Error parsing token for roles:', e);
          }
        }
      } else {
        this.currentUserId = null;
        this.userRoles = [];
      }
    } catch (error) {
      console.error('Error loading user info:', error);
      this.currentUserId = null;
      this.userRoles = [];
    }
  }

  /**
   * Check if the current user is the owner of the ad
   * @param ad The ad to check ownership for
   * @returns True if the current user is the owner of the ad
   */
  isAdOwner(ad: Ad): boolean {
    if (!ad.userId || !this.currentUserId) {
      return false;
    }
    return ad.userId === this.currentUserId;
  }

  /**
   * Check if the current user has admin role
   * @returns True if the current user has admin role
   */
  isAdmin(): boolean {
    // Check if userRoles array exists and includes 'ADMIN'
    return this.userRoles && this.userRoles.includes('ADMIN');
  }

  /**
   * Check if the current user can edit or delete the ad
   * @param ad The ad to check permissions for
   * @returns True if the current user can edit or delete the ad
   */
  canEditOrDeleteAd(ad: Ad): boolean {
    return this.isAdOwner(ad) || this.isAdmin();
  }

  /**
   * Navigue manuellement dans le carousel
   * @param carouselId ID du carousel
   * @param direction Direction ('prev' ou 'next')
   */
  navigateCarousel(carouselId: string, direction: 'prev' | 'next'): void {
    // Récupérer l'élément carousel
    const carouselEl = document.getElementById(carouselId);
    if (!carouselEl) return;

    // Récupérer toutes les slides
    const items = carouselEl.querySelectorAll('.carousel-item');
    if (items.length <= 1) return;

    // Trouver l'index de la slide active
    let activeIndex = 0;
    items.forEach((item, index) => {
      if (item.classList.contains('active')) {
        activeIndex = index;
      }
    });

    // Calculer le nouvel index
    let newIndex;
    if (direction === 'next') {
      newIndex = (activeIndex + 1) % items.length;
    } else {
      newIndex = (activeIndex - 1 + items.length) % items.length;
    }

    // Retirer la classe active de toutes les slides
    items.forEach(item => item.classList.remove('active'));

    // Ajouter la classe active à la nouvelle slide
    items[newIndex].classList.add('active');

    // Mettre à jour les indicateurs
    const indicators = carouselEl.querySelectorAll('.carousel-indicators button');
    if (indicators.length > 0) {
      indicators.forEach((indicator, index) => {
        if (index === newIndex) {
          indicator.classList.add('active');
          indicator.setAttribute('aria-current', 'true');
        } else {
          indicator.classList.remove('active');
          indicator.removeAttribute('aria-current');
        }
      });
    }
  }

  /**
   * Open the complaint dialog to report an ad
   * @param ad The ad to report
   * @param event The click event
   */
  openComplaintDialog(ad: Ad, event: Event): void {
    // Prevent event propagation to avoid navigating to ad details
    event.stopPropagation();

    if (!ad.id || !ad.title) {
      console.error('Cannot report ad: missing id or title');
      return;
    }

    const dialogRef = this.dialog.open(AdComplaintDialogComponent, {
      width: '500px',
      maxWidth: '95vw',
      panelClass: 'complaint-dialog-panel',
      data: {
        adId: ad.id,
        adTitle: ad.title
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        // Show success message
        alert('Thank you for your report. We will review it shortly.');
      }
    });
  }

  /**
   * Debug ad location data (for admin use)
   * @param ad The ad to debug
   */
  debugAdLocation(ad: Ad): void {
    if (!ad.location || ad.location.length !== 2) {
      alert('This ad has no location data.');
      return;
    }

    // Ad location is stored as [longitude, latitude]
    const adLng = ad.location[0];
    const adLat = ad.location[1];

    // Show location details
    const locationInfo = `
      Ad ID: ${ad.id}
      Location: [${adLat}, ${adLng}]
      Location Name: ${ad.locationName || 'Not specified'}

      Use this location for filtering? (This will update the location filter)
    `;

    if (confirm(locationInfo)) {
      // Set the location in the filter form
      this.filterForm.patchValue({
        location: `${adLat}, ${adLng}`
      });

      // Store the location for future use
      this.currentLocation = {
        lat: adLat,
        lng: adLng,
        source: 'debug'
      };

      // Apply filters
      this.applyFilters();
    }
  }

  /**
   * Clear the location cache (for admin use)
   */
  clearLocationCache(): void {
    // Clear the location service cache
    this.locationService.clearCache();

    // Clear the current location
    this.currentLocation = null;

    // Reset the location form controls
    this.filterForm.patchValue({
      location: null,
      locationRadius: 10
    });

    // Apply filters to refresh the list
    this.applyFilters();

    // Show confirmation to the user
    alert('Location cache cleared successfully.');

    console.log('Location cache cleared');
  }

  /**
   * Set a manual location for testing (for admin use)
   * @param lat Latitude
   * @param lng Longitude
   */
  setManualLocation(lat: number, lng: number): void {
    if (!this.isAdmin()) {
      console.warn('Only admins can use manual location setting');
      return;
    }

    console.log('Setting manual location:', { lat, lng });

    // Store the location
    this.currentLocation = {
      lat,
      lng,
      source: 'manual'
    };

    // Format coordinates
    const coords = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

    // Update the form
    this.filterForm.patchValue({
      location: coords
    });

    // Apply filters
    this.applyFilters();

    console.log('Manual location set successfully');
  }

  /**
   * Use the current location for filtering
   */
  useCurrentLocationForFilter(): void {
    console.log('Using current location for filtering');
    this.isLocationLoading = true;

    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser.');
      this.isLocationLoading = false;
      return;
    }

    // Get the current position with improved options
    navigator.geolocation.getCurrentPosition(
      // Success callback
      (position) => {
        try {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          const accuracy = position.coords.accuracy;

          // Validate coordinates
          if (isNaN(lat) || isNaN(lng)) {
            throw new Error('Invalid coordinates received from geolocation API');
          }

          console.log('Current location:', { lat, lng, accuracy });
          console.log('Position accuracy:', accuracy, 'meters');

          // Store the location for future use
          this.currentLocation = {
            lat,
            lng,
            accuracy,
            source: 'browser-geolocation'
          };

          // Format coordinates with proper precision
          const coords = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

          // Update the location input field
          this.filterForm.patchValue({
            location: coords
          });

          // Apply filters
          this.applyFilters();
        } catch (innerError) {
          console.error('Error processing geolocation result:', innerError);
          this.handleLocationError('Error processing your location. Please try again.');
        }
      },
      // Error callback
      (error) => {
        console.error('Geolocation error:', error);

        let errorMessage = 'Could not get your current location.';

        // Provide specific error messages based on error code
        switch (error.code) {
          case 1: // PERMISSION_DENIED
            errorMessage = 'Location access was denied. Please enable location services in your browser settings.';
            break;
          case 2: // POSITION_UNAVAILABLE
            errorMessage = 'Location information is unavailable. Please try again later.';
            break;
          case 3: // TIMEOUT
            errorMessage = 'The request to get your location timed out. Please try again.';
            break;
          default:
            errorMessage = 'An unknown error occurred while trying to get your location.';
            break;
        }

        alert(errorMessage);
        this.isLocationLoading = false;
      },
      // Options
      {
        enableHighAccuracy: true,
        timeout: 15000, // Increased timeout for better reliability
        maximumAge: 0
      }
    );
  }
}
