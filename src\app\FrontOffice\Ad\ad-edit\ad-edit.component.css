/* ad-edit.component.css */
.edit-ad-container {
    width: 80%;
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    background: #f4f4f4;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }
  
  .edit-ad-container h1 {
    text-align: center;
    color: #333;
  }
  
  .edit-ad-container form {
    display: flex;
    flex-direction: column;
  }
  
  .edit-ad-container label {
    margin-top: 10px;
  }
  
  .edit-ad-container input[type="text"],
  .edit-ad-container input[type="date"],
  .edit-ad-container textarea,
  .edit-ad-container select {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  
  .edit-ad-container button {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .edit-ad-container button:hover {
    background-color: #0056b3;
  }
  
  .edit-ad-container button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
  