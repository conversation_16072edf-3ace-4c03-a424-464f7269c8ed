<app-header-front></app-header-front>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">Leave Request</h2>

    <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()">
      
      <!-- Start Date -->
      <div class="form-group mb-3">
        <label for="startDate" class="form-label fw-bold">Start Date</label>
        <input id="startDate" type="date" formControlName="startDate" class="form-control rounded" required>
      </div>

      <!-- End Date -->
      <div class="form-group mb-3">
        <label for="endDate" class="form-label fw-bold">End Date</label>
        <input id="endDate" type="date" formControlName="endDate" class="form-control rounded" required>
      </div>

      <!-- Reason for Leave -->
      <div class="form-group mb-3">
        <label for="reason" class="form-label fw-bold">Reason</label>
        <textarea id="reason" formControlName="reason" class="form-control rounded" rows="4" required></textarea>
      </div>

      <!-- Submit Button -->
      <div class="d-flex justify-content-center">
        <button type="submit" class="btn btn-primary btn-lg px-4" [disabled]="leaveForm.invalid">
          <i class="fas fa-paper-plane"></i> Submit
        </button>
      </div>

    </form>

    <!-- Return Button -->
    <div class="d-flex justify-content-center mt-3">
      <a class="btn btn-secondary btn-lg px-4" [routerLink]="['/leave']">
        <i class="fas fa-arrow-left"></i> Return
      </a>
    </div>

  </div>
</div>

<app-footer-front></app-footer-front>
