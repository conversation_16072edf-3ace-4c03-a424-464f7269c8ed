import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { Ad, AdControllerService } from 'src/app/openapi';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-ad-detail-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    FormsModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './ad-detail-dialog.component.html',
  styleUrls: ['./ad-detail-dialog.component.css']
})
export class AdDetailDialogComponent {
  rejectionReason: string = '';
  isProcessing: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<AdDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { ad: Ad },
    private adService: AdControllerService
  ) {}

  close(result?: { action: string, adId: string, rejectionReason?: string }): void {
    this.dialogRef.close(result);
  }

  // Approve the ad
  approve(): void {
    if (this.isProcessing) return;

    this.isProcessing = true;
    this.adService.approveAd(this.data.ad.id!).subscribe({
      next: () => {
        this.isProcessing = false;
        this.close({ action: 'approved', adId: this.data.ad.id! });
      },
      error: (error) => {
        console.error('Error approving ad:', error);
        this.isProcessing = false;
      }
    });
  }

  // Reject the ad
  reject(): void {
    if (this.isProcessing) return;

    this.isProcessing = true;

    // Store the rejection reason to pass it back to the parent component
    const rejectionReason = this.rejectionReason || 'Rejected by administrator';

    // Use the standard API to reject the ad
    this.adService.rejectAd(this.data.ad.id!).subscribe({
      next: () => {
        this.isProcessing = false;
        this.close({
          action: 'rejected',
          adId: this.data.ad.id!,
          rejectionReason: rejectionReason
        });
      },
      error: (error) => {
        console.error('Error rejecting ad:', error);
        this.isProcessing = false;
      }
    });
  }

  // Helper method to format date with time
  formatDateTime(date: string | Date | null | undefined): string {
    if (!date) return 'Not specified';
    return new Date(date).toLocaleString();
  }

  // Helper method to get image URL
  getImageUrl(image: string): string {
    return `data:image/jpeg;base64,${image}`;
  }
}
