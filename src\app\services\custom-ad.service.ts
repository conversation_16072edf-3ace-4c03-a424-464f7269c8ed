import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, from } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { Ad, AdControllerService } from '../openapi';
import { Configuration } from '../openapi/configuration';
import { BASE_PATH } from '../openapi/variables';
import { Inject, Optional } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CustomAdService {
  private basePath = 'http://localhost:8089/speedygo';

  constructor(
    private httpClient: HttpClient,
    private adService: AdControllerService,
    @Optional() @Inject(BASE_PATH) basePath: string,
    @Optional() configuration: Configuration
  ) {
    if (configuration) {
      this.basePath = configuration.basePath || this.basePath;
    }
    if (basePath) {
      this.basePath = basePath;
    }
  }

  /**
   * Create an ad with multiple images
   * @param ad The ad data
   * @param images Array of image files
   * @returns Observable of the created ad
   */
  createAdWithMultipleImages(ad: Ad, images: File[]): Observable<Ad> {
    if (!ad || !images || images.length === 0) {
      throw new Error('Required parameters were null or undefined when calling createAdWithMultipleImages.');
    }

    console.log('Creating ad with multiple images:', images.length, 'images');

    const formData = new FormData();

    // Add the ad data as JSON
    formData.append('ad', new Blob([JSON.stringify(ad)], { type: 'application/json' }));
    console.log('Ad data added to form:', ad);

    // Add multiple images
    if (images.length === 1) {
      // If only one image, use the 'image' parameter for backward compatibility
      console.log('Adding single image with name "image"');
      formData.append('image', images[0]);
    } else {
      // If multiple images, use the 'images' parameter
      console.log('Adding multiple images with name "images"');
      images.forEach((image, index) => {
        console.log(`Adding image ${index + 1}/${images.length}: ${image.name}`);
        formData.append('images', image);
      });
    }

    // Log form data entries (can't directly log FormData content)
    console.log('Form data created with the following entries:');
    for (const pair of (formData as any).entries()) {
      console.log(pair[0], pair[1] instanceof Blob ? `Blob (${pair[1].size} bytes)` : pair[1]);
    }

    // Add headers for better error handling
    const headers = new HttpHeaders({
      'Accept': 'application/json, text/plain, */*'
    });

    console.log('Sending request to:', `${this.basePath}/ad/createAd`);

    // Use catchError to handle errors more gracefully
    return this.httpClient.post<Ad>(
      `${this.basePath}/ad/createAd`,
      formData,
      {
        headers,
        // Add observe: 'response' to get the full response including headers and status
        observe: 'response'
      }
    ).pipe(
      // Map the response to just the body
      map(response => {
        console.log('Response received:', response);
        return response.body as Ad;
      }),
      // Catch and handle errors
      catchError(error => {
        console.error('Error in createAdWithMultipleImages:', error);

        // Try to extract a meaningful error message
        let errorMessage = 'An error occurred while creating the ad';

        if (error.error instanceof Blob) {
          // If the error is a Blob, read it as text
          return from(error.error.text()).pipe(
            mergeMap((errorText: unknown) => {
              const errorTextString = String(errorText);
              try {
                const errorObj = JSON.parse(errorTextString) as { message?: string };
                errorMessage = errorObj.message || errorMessage;
              } catch (e) {
                // Si le texte n'est pas du JSON valide, utilisez-le directement
                errorMessage = errorTextString || errorMessage;
              }
              return throwError(() => new Error(errorMessage));
            })
          );
        } else if (error.error) {
          // If the error has an error property, use it
          if (typeof error.error === 'object' && error.error !== null && 'message' in error.error) {
            errorMessage = error.error.message || errorMessage;
          } else if (typeof error.error === 'string') {
            errorMessage = error.error || errorMessage;
          }
        }

        return throwError(() => new Error(errorMessage));
      })
    );
  }

  // Delegate other methods to the original service
  getAllAds(): Observable<Ad[]> {
    return this.adService.getAllAds();
  }

  getAdById(id: string): Observable<Ad> {
    return this.adService.getAdById(id);
  }

  updateAd(ad: Ad): Observable<Ad> {
    return this.adService.updateAd(ad);
  }

  deleteAd(id: string): Observable<any> {
    return this.adService.deleteAd1(id);
  }

  approveAd(id: string): Observable<Ad> {
    return this.adService.approveAd(id);
  }

  rejectAd(id: string): Observable<Ad> {
    return this.adService.rejectAd(id);
  }

  incrementViewCount(id: string): Observable<Ad> {
    return this.adService.incrementViewCount(id);
  }
}
