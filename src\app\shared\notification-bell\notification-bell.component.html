<div class="notification-bell-container">
  <a class="nav-link" [matMenuTriggerFor]="notificationMenu" aria-label="Notifications">
    <span [matBadge]="unreadCount" [matBadgeHidden]="unreadCount === 0" matBadgeColor="warn" matBadgeSize="small" matBadgePosition="above after">
      <i class="fe fe-bell"></i>
    </span>
  </a>

  <mat-menu #notificationMenu="matMenu" class="notification-menu">
    <div class="notification-header">
      <h3 class="notification-title">Notifications</h3>
      <button class="btn btn-sm btn-link" [disabled]="unreadCount === 0" (click)="markAllAsRead($event)">
        <i class="fe fe-check-circle me-1"></i> Mark all
      </button>
    </div>

    <div class="notification-list-container">
      <ng-container *ngIf="notifications.length > 0; else noNotifications">
        <mat-list class="notification-list">
          <ng-container *ngFor="let notification of notifications">
            <mat-list-item
              [ngClass]="{'unread': notification.read === false}"
              (click)="markAsRead(notification, $event)">
              <div class="notification-item" [attr.data-type]="notification.type">
                <div class="notification-icon">
                  <!-- Icon based on notification type -->
                  <i *ngIf="notification.type === 'AD_REPORT'" class="fe fe-flag notification-icon-report"></i>
                  <i *ngIf="notification.type === 'AD_WARNING'" class="fe fe-alert-triangle notification-icon-warning"></i>
                  <i *ngIf="notification.type === 'AD_DELETION'" class="fe fe-trash notification-icon-delete"></i>
                  <i *ngIf="notification.type === 'AD_LIKE'" class="fe fe-heart notification-icon-like"></i>
                  <i *ngIf="notification.type !== 'AD_REPORT' && notification.type !== 'AD_WARNING' && notification.type !== 'AD_DELETION' && notification.type !== 'AD_LIKE'" class="fe fe-bell"></i>
                </div>
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatDate(notification.date) }}</div>
                </div>
              </div>
            </mat-list-item>
          </ng-container>
        </mat-list>
      </ng-container>

      <ng-template #noNotifications>
        <div class="no-notifications">
          <i class="fe fe-bell-off"></i>
          <p>No notifications</p>
          <small>You'll see your notifications here</small>
          <button *ngIf="userId" class="btn btn-sm btn-outline-primary mt-2" (click)="createTestNotification($event)">
            Test
          </button>
        </div>
      </ng-template>
    </div>

    <div class="notification-footer">
      <button class="btn btn-sm btn-link" routerLink="/notifications" (click)="$event.stopPropagation()">
        <i class="fe fe-list me-1"></i> View all
      </button>
    </div>
  </mat-menu>
</div>
