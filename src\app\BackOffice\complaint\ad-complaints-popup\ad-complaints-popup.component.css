/* Variables */
:root {
  --primary-color: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3f37c9;
  --secondary-color: #f72585;
  --secondary-light: #ff4d6d;
  --accent-color: #4cc9f0;
  --success-color: #4cc9a0;
  --warning-color: #ffbe0b;
  --danger-color: #f72585;
  --info-color: #4cc9f0;
  --dark-color: #212121;
  --light-color: #f8f9fa;
  --bg-gradient: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
  --card-gradient: linear-gradient(135deg, rgba(248, 249, 250, 0.5) 0%, rgba(255, 255, 255, 1) 100%);
  --text-color: #333333;
  --text-secondary: #6c757d;
  --border-color: #e3e6f0;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Dialog Styling */
.ad-complaints-popup {
  max-width: 600px;
  width: 100%;
  animation: scaleIn 0.3s ease-out;
}

/* Dialog Title */
h2[mat-dialog-title] {
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(67, 97, 238, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

/* Badge Styling */
.badge {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.badge.bg-danger {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%) !important;
  color: white !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, #ffbe0b 0%, #fb8500 100%) !important;
  color: #000 !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%) !important;
  color: white !important;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%) !important;
  color: white !important;
}

/* Complaints List */
.complaints-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
  margin-top: 1rem;
}

/* Custom Scrollbar */
.complaints-list::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.complaints-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.complaints-list::-webkit-scrollbar-thumb {
  background: linear-gradient(var(--primary-light), var(--primary-dark));
  border-radius: 10px;
}

.complaints-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Complaint Item */
.complaint-item {
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  background-color: rgba(67, 97, 238, 0.03);
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease-out;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.complaint-item:hover {
  background-color: rgba(67, 97, 238, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Complaint Title */
.complaint-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--primary-dark);
}

/* Complaint Description */
.complaint-description {
  font-size: 14px;
  color: var(--text-secondary);
  white-space: pre-line;
  margin-bottom: 10px;
  line-height: 1.5;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
}

/* Dialog Content */
mat-dialog-content {
  min-height: 200px;
}

/* Loading Spinner */
mat-spinner {
  margin: 2rem auto;
}

/* Close Button */
button[mat-button] {
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 30px;
  background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
  letter-spacing: 0.5px;
  text-transform: uppercase;
  margin-top: 1rem;
}

button[mat-button]:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
}

/* Divider */
mat-divider {
  margin: 15px 0;
  opacity: 0.5;
}

/* Alert */
.alert-danger {
  background-color: rgba(247, 37, 133, 0.1);
  color: var(--danger-color);
  border: none;
  border-radius: 8px;
  padding: 15px;
  font-weight: 500;
  border-left: 4px solid var(--danger-color);
}

/* Empty State */
.text-center p {
  color: var(--text-secondary);
  font-size: 15px;
  margin: 2rem 0;
}
