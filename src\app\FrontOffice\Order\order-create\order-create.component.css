/* Container général */
.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: linear-gradient(135deg, #6ca3dc, #a3b1cf);  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

/* Titres principaux */
h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  font-weight: bold;
  color: #0056b3;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #0074D9;
  border-bottom: 2px solid #0074D9;
  padding-bottom: 0.5rem;
}

/* Liste des produits */
div[ngFor] {
  margin-bottom: 1rem;
}

/* Style des paragraphes listant le produit */
p {
  margin: 0.5rem 0;
  font-size: 1rem;
}

/* Style des inputs */
input[type="number"] {
  width: 80px;
  padding: 0.5rem;
  margin-right: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="number"]:focus {
  outline: none;
  border-color: #0074D9;
  box-shadow: 0 0 5px rgba(0, 116, 217, 0.5);
}

/* Boutons génériques */
button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  font-size: 0.9rem;
}

/* Bouton Ajouter (petit bouton pour chaque produit) */
button:nth-of-type(1) {
  background-color: #28a745;
  color: #fff;
  margin-left: 0.5rem;
}

button:nth-of-type(1):hover {
  background-color: #218838;
  transform: scale(1.05);
}

/* Bouton Valider la commande */
button:nth-of-type(2) {
  display: block;
  margin: 1rem auto;
  background-color: #0074D9;
  color: #fff;
}

button:nth-of-type(2):hover {
  background-color: #0056b3;
  transform: scale(1.05);
}

/* Animation pour l'apparition des éléments */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Appliquer l'animation aux blocs de liste */
.container > div {
  animation: fadeInUp 0.6s ease-out;
}
