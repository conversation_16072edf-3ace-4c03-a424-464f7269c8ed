<div class="complaint-edit-dialog">
  <h2 mat-dialog-title>
    <i class="fas fa-edit me-2"></i>Modify Complaint
  </h2>

  <mat-dialog-content>
    <div *ngIf="loading" class="text-center p-4">
      <mat-spinner diameter="40" class="mx-auto"></mat-spinner>
      <p class="mt-3">Loading complaint data...</p>
    </div>

    <div *ngIf="error" class="alert alert-danger">
      <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
    </div>

    <div *ngIf="!loading && !error" class="complaint-form">
      <form [formGroup]="complaintForm" (ngSubmit)="updateComplaint()">
        <!-- Complaint Title -->
        <div class="form-group mb-3">
          <label for="title" class="form-label fw-bold">Title</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-heading"></i></span>
            <input
              id="title"
              type="text"
              formControlName="title"
              class="form-control rounded-end"
              [ngClass]="{'is-invalid': complaintForm.get('title')?.invalid && complaintForm.get('title')?.touched}"
              required>
          </div>
          <div class="character-counter" [ngClass]="{'text-danger': getCharacterCount('title') > getMaxCharacterCount('title')}">
            {{ getCharacterCount('title') }}/{{ getMaxCharacterCount('title') }}
          </div>
          <div *ngIf="complaintForm.get('title')?.invalid && complaintForm.get('title')?.touched" class="text-danger mt-1">
            <small *ngIf="complaintForm.get('title')?.errors?.['required']">Title is required</small>
            <small *ngIf="complaintForm.get('title')?.errors?.['minlength']">Title must be at least 5 characters</small>
            <small *ngIf="complaintForm.get('title')?.errors?.['maxlength']">Title cannot exceed 100 characters</small>
          </div>
        </div>

        <!-- Complaint Description -->
        <div class="form-group mb-3">
          <label for="description" class="form-label fw-bold">Description</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-comment-alt"></i></span>
            <textarea
              id="description"
              formControlName="description"
              class="form-control rounded-end"
              rows="4"
              [ngClass]="{'is-invalid': complaintForm.get('description')?.invalid && complaintForm.get('description')?.touched}"
              required></textarea>
          </div>
          <div class="character-counter" [ngClass]="{'text-danger': getCharacterCount('description') > getMaxCharacterCount('description')}">
            {{ getCharacterCount('description') }}/{{ getMaxCharacterCount('description') }}
          </div>
          <div *ngIf="complaintForm.get('description')?.invalid && complaintForm.get('description')?.touched" class="text-danger mt-1">
            <small *ngIf="complaintForm.get('description')?.errors?.['required']">Description is required</small>
            <small *ngIf="complaintForm.get('description')?.errors?.['minlength']">Description must be at least 10 characters</small>
            <small *ngIf="complaintForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500 characters</small>
          </div>
        </div>

        <!-- Complaint Category -->
        <div class="form-group mb-3">
          <label for="category" class="form-label fw-bold">Category</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-tag"></i></span>
            <select
              id="category"
              formControlName="category"
              class="form-control rounded-end"
              [ngClass]="{'is-invalid': complaintForm.get('category')?.invalid && complaintForm.get('category')?.touched}">
              <option *ngFor="let category of categories" [value]="category.value">{{ category.label }}</option>
            </select>
          </div>
          <div *ngIf="complaintForm.get('category')?.invalid && complaintForm.get('category')?.touched" class="text-danger mt-1">
            <small *ngIf="complaintForm.get('category')?.errors?.['required']">Category is required</small>
          </div>
        </div>

        <!-- Priority Selection -->
        <div class="form-group mb-3">
          <label class="form-label fw-bold">Priority</label>
          <div class="priority-options">
            <div *ngFor="let priority of priorities" class="priority-option">
              <input
                type="radio"
                [id]="'priority-' + priority.value"
                [value]="priority.value"
                formControlName="priority"
                class="priority-radio">
              <label [for]="'priority-' + priority.value" class="priority-label" [attr.data-priority]="priority.value">
                <i class="fas"
                  [ngClass]="{
                    'fa-thermometer-quarter': priority.value === 'LOW',
                    'fa-thermometer-half': priority.value === 'MEDIUM',
                    'fa-thermometer-full': priority.value === 'HIGH'
                  }"></i>
                {{ priority.label }}
              </label>
            </div>
          </div>
        </div>

        <!-- Evidence Files Section -->
        <div class="form-group mb-3">
          <label class="form-label fw-bold">Evidence Files (Optional)</label>

          <!-- File Upload Area -->
          <div class="custom-file-upload" (click)="openFileSelector()">
            <input #fileInput type="file" (change)="onFileSelected($event)" style="display: none;" multiple accept="image/*,.pdf,.doc,.docx">
            <div class="upload-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">Drag & drop files here or click to browse</div>
            <div class="upload-hint">You can select up to 3 files (images or documents)</div>
          </div>

          <!-- Selected Files Preview -->
          <div class="selected-files mt-3" *ngIf="filePreviews.length > 0">
            <h6 class="mb-2">Selected Files:</h6>
            <div class="file-preview" *ngFor="let file of filePreviews; let i = index">
              <div class="file-icon">
                <i class="fas" [ngClass]="{
                  'fa-file-image': file.type.includes('image'),
                  'fa-file-pdf': file.type.includes('pdf'),
                  'fa-file-word': file.type.includes('word') || file.type.includes('doc'),
                  'fa-file': !file.type.includes('image') && !file.type.includes('pdf') && !file.type.includes('word') && !file.type.includes('doc')
                }"></i>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ file.size }}</div>
              </div>
              <button type="button" class="btn btn-sm btn-danger" (click)="removeFile(i)">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="form-group mb-3">
          <label class="form-label fw-bold">Contact Preference</label>
          <div class="contact-options">
            <div *ngFor="let option of contactPreferences" class="contact-option">
              <input
                type="radio"
                [id]="'contact-' + option.value"
                [value]="option.value"
                formControlName="contactPreference"
                class="contact-radio">
              <label [for]="'contact-' + option.value" class="contact-label">
                <i class="fas"
                  [ngClass]="{
                    'fa-envelope': option.value === 'EMAIL',
                    'fa-phone': option.value === 'PHONE',
                    'fa-comment-dots': option.value === 'EITHER'
                  }"></i>
                {{ option.label }}
              </label>
            </div>
          </div>
        </div>

        <!-- Email Address (shown conditionally) -->
        <div class="form-group mb-3" *ngIf="complaintForm.get('contactPreference')?.value === 'EMAIL' || complaintForm.get('contactPreference')?.value === 'EITHER'">
          <label for="contactEmail" class="form-label fw-bold">Email Address</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
            <input
              id="contactEmail"
              type="email"
              formControlName="contactEmail"
              class="form-control rounded-end"
              placeholder="<EMAIL>"
              [ngClass]="{'is-invalid': complaintForm.get('contactEmail')?.invalid && complaintForm.get('contactEmail')?.touched}">
          </div>
          <div *ngIf="complaintForm.get('contactEmail')?.invalid && complaintForm.get('contactEmail')?.touched" class="text-danger mt-1">
            <small *ngIf="complaintForm.get('contactEmail')?.errors?.['required']">Email address is required</small>
            <small *ngIf="complaintForm.get('contactEmail')?.errors?.['email']">Please enter a valid email address</small>
          </div>
        </div>

        <!-- Phone Number (shown conditionally) -->
        <div class="form-group mb-3" *ngIf="complaintForm.get('contactPreference')?.value === 'PHONE' || complaintForm.get('contactPreference')?.value === 'EITHER'">
          <label for="contactPhone" class="form-label fw-bold">Phone Number</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-phone"></i></span>
            <input
              id="contactPhone"
              type="tel"
              formControlName="contactPhone"
              class="form-control rounded-end"
              placeholder="+****************"
              [ngClass]="{'is-invalid': complaintForm.get('contactPhone')?.invalid && complaintForm.get('contactPhone')?.touched}">
          </div>
          <div *ngIf="complaintForm.get('contactPhone')?.invalid && complaintForm.get('contactPhone')?.touched" class="text-danger mt-1">
            <small *ngIf="complaintForm.get('contactPhone')?.errors?.['required']">Phone number is required</small>
          </div>
        </div>
      </form>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button type="button" class="btn btn-secondary me-2" (click)="close()">
      <i class="fas fa-times me-2"></i>Cancel
    </button>
    <button type="button" class="btn btn-primary" [disabled]="complaintForm.invalid || submitting" (click)="updateComplaint()">
      <i class="fas fa-save me-2"></i>Update
      <span *ngIf="submitting" class="spinner-border spinner-border-sm ms-1" role="status" aria-hidden="true"></span>
    </button>
  </mat-dialog-actions>
</div>
