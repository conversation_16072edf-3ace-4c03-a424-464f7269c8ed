import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { Ad, AdControllerService } from 'src/app/openapi';
import { CommonModule } from '@angular/common';
import { Inject } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import * as L from 'leaflet';
import { CustomAdService } from 'src/app/services/custom-ad.service';


@Component({
  standalone: true,
  selector: 'app-create-ad',
  templateUrl: './create-ad.component.html',
  styleUrls: ['./create-ad.component.css'],
  imports: [CommonModule, ReactiveFormsModule,MatDialogModule ],
})
export class CreateAdComponent implements OnInit, AfterViewInit {
  adForm: FormGroup;

  @ViewChild('mapContainer') mapContainer!: ElementRef;
  map!: L.Map;
  marker!: L.Marker;
  showMap: boolean = false;
  isLocationLoading: boolean = false;
  locationSet: boolean = false;
  locationAccuracy: number | null = null;
  locationAccuracyText: string = '';

  // Icône par défaut pour le marqueur
  defaultIcon = L.icon({
    iconUrl: 'assets/leaflet/marker-icon.png',
    shadowUrl: 'assets/leaflet/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  });

  constructor(
    private fb: FormBuilder,
    private adService: AdControllerService,
    private customAdService: CustomAdService,
    @Inject(MatDialogRef)
    public dialogRef: MatDialogRef<CreateAdComponent>
  ) {
    this.adForm = this.fb.group({
      title: ['', Validators.required],
      description: ['', [Validators.required, Validators.minLength(5)]],
      image: [''],
      category: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      startTime: ['08:00', Validators.required], // Default to 8:00 AM
      endTime: ['18:00', Validators.required],   // Default to 6:00 PM
      useEndDateAsExpiry: [true], // New toggle to use end date as expiry date
      tags: [''], // New field for tags (comma-separated)
      locationName: [''], // New field for location name
      longitude: [''], // New field for longitude
      latitude: [''], // New field for latitude
      price: ['', [Validators.min(0)]], // New field for price (optional, but must be non-negative)
    });
  }

  ngOnInit(): void {
    // Add event listeners for the location fields
    this.adForm.get('longitude')?.valueChanges.subscribe(value => {
      if (value && this.adForm.get('latitude')?.value) {
        this.updateMarkerPosition();
      }
    });

    this.adForm.get('latitude')?.valueChanges.subscribe(value => {
      if (value && this.adForm.get('longitude')?.value) {
        this.updateMarkerPosition();
      }
    });
  }

  ngAfterViewInit(): void {
    // Leaflet est déjà chargé via l'import, donc pas besoin de charger de script
  }

  toggleMap(): void {
    this.showMap = !this.showMap;
    if (this.showMap) {
      // Utiliser setTimeout pour s'assurer que le conteneur de la carte est visible
      setTimeout(() => this.initMap(), 100);
    }
  }

  /**
   * Check if the browser supports high-accuracy geolocation
   * @returns True if high-accuracy geolocation is likely available
   */
  private checkHighAccuracySupport(): boolean {
    // Check if we're on a mobile device (which typically has GPS)
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // Check if we're on HTTPS (required for geolocation on modern browsers)
    const isSecure = window.location.protocol === 'https:' || window.location.hostname === 'localhost';

    // Check if geolocation is available
    const hasGeolocation = 'geolocation' in navigator;

    console.log('Geolocation support check:', { isMobile, isSecure, hasGeolocation });

    // Return true if all conditions are met
    return hasGeolocation && (isMobile || isSecure);
  }

  /**
   * Use the current location for the ad location
   */
  useCurrentLocation(): void {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    this.isLocationLoading = true;

    // Check if high-accuracy geolocation is likely available
    const highAccuracySupport = this.checkHighAccuracySupport();

    if (!highAccuracySupport) {
      console.warn('High-accuracy geolocation may not be available');

      // Show a warning to the user
      const useAnyway = confirm(
        'Your browser or device may not support high-accuracy location.\n\n' +
        'This could result in less accurate location detection.\n\n' +
        'Do you want to try anyway? (Click Cancel to see a map of Tunisia instead)'
      );

      if (!useAnyway) {
        // User chose to use the Tunisia map instead
        this.setDefaultTunisiaLocation();
        return;
      }
    }

    // Show the map if it's not already visible
    if (!this.showMap) {
      this.showMap = true;
      // Wait for the map container to be visible before initializing
      setTimeout(() => this.getCurrentPosition(), 100);
    } else {
      this.getCurrentPosition();
    }
  }

  /**
   * Get the current position using the Geolocation API
   */
  private getCurrentPosition(): void {
    // Use high accuracy option and longer timeout
    // maximumAge: 0 forces a fresh location reading
    // enableHighAccuracy: true requests the most accurate location available
    // timeout: 15000 gives more time for accurate GPS readings
    const options = {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 0
    };

    console.log('Requesting position with options:', options);

    navigator.geolocation.getCurrentPosition(
      // Success callback
      (position) => {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;
        const accuracy = position.coords.accuracy; // Accuracy in meters

        console.log('Successfully obtained coordinates:', {
          lat,
          lng,
          accuracy: `${accuracy} meters`,
          timestamp: new Date(position.timestamp).toISOString()
        });

        // Check if accuracy is extremely poor (more than 10km)
        const isExtremePoorAccuracy = accuracy > 10000;
        // Check if accuracy is poor (more than 100 meters)
        const isPoorAccuracy = accuracy > 100 && accuracy <= 10000;

        if (isExtremePoorAccuracy) {
          console.warn(`Extremely poor location accuracy: ${accuracy} meters`);

          // For extremely poor accuracy, don't even offer to use it
          alert(`Your location accuracy is extremely poor (${Math.round(accuracy/1000)} kilometers).\n\nWe'll show you a map of Tunisia instead. Please use the search box or click on the map to set your precise location.`);

          // Set a default location in Tunisia (Tunis city center)
          this.setDefaultTunisiaLocation();
          return;
        } else if (isPoorAccuracy) {
          console.warn(`Poor location accuracy: ${accuracy} meters`);

          // Show a confirmation dialog to the user
          if (confirm(`Your location accuracy is approximately ${Math.round(accuracy)} meters, which may not be precise. Do you want to use this location anyway?\n\nTip: For better accuracy, try using this feature outdoors or near a window, or use the search box to find your location.`)) {
            this.setLocationCoordinates(lat, lng, accuracy);
          } else {
            // If user declines, show them a default map of Tunisia
            this.setDefaultTunisiaLocation();
            return;
          }
        } else {
          // Good accuracy, proceed normally
          this.setLocationCoordinates(lat, lng, accuracy);
        }

        // The map initialization and other steps are now handled in setLocationCoordinates
      },

      // Error callback
      (error) => {
        console.error('Geolocation error:', error);

        let errorMessage = 'Could not get your current location.';
        let errorDetails = '';
        let suggestedAction = '';

        // Provide specific error messages based on error code
        switch(error.code) {
          case 1: // PERMISSION_DENIED
            errorMessage = 'Location access was denied.';
            errorDetails = 'Your browser blocked access to your location.';
            suggestedAction = 'Please enable location services in your browser settings and try again.';
            break;
          case 2: // POSITION_UNAVAILABLE
            errorMessage = 'Location information is unavailable.';
            errorDetails = 'Your device could not determine your current position.';
            suggestedAction = 'Try moving to an area with better GPS signal or enable Wi-Fi for better location accuracy.';
            break;
          case 3: // TIMEOUT
            errorMessage = 'The request to get your location timed out.';
            errorDetails = 'Your device took too long to provide location information.';
            suggestedAction = 'Try again in a different location or check if your device has GPS enabled.';
            break;
          default:
            errorMessage = 'An unknown error occurred while trying to get your location.';
            errorDetails = 'Error code: ' + (error.code || 'unknown');
            suggestedAction = 'Please try again later or enter your location manually.';
            break;
        }

        // Try IP-based geolocation as a fallback
        this.tryIpBasedGeolocation(errorMessage, errorDetails, suggestedAction);
      },
      options
    );
  }

  initMap(): void {
    if (!this.mapContainer || !this.showMap) return;

    // Default to a location in Tunisia if no coordinates are set
    const defaultLat = 36.806389;
    const defaultLng = 10.181667;

    // Use form values if available
    const lat = this.adForm.get('latitude')?.value ? parseFloat(this.adForm.get('latitude')?.value) : defaultLat;
    const lng = this.adForm.get('longitude')?.value ? parseFloat(this.adForm.get('longitude')?.value) : defaultLng;

    // Check if we're using the default location
    const usingDefaultLocation = !this.adForm.get('latitude')?.value || !this.adForm.get('longitude')?.value;

    // Only reset the locationSet flag if we're using the default location
    if (usingDefaultLocation) {
      this.locationSet = false;
    }

    // Si la carte existe déjà, la supprimer pour éviter les doublons
    if (this.map) {
      this.map.remove();
    }

    // Initialiser la carte Leaflet
    this.map = L.map(this.mapContainer.nativeElement).setView([lat, lng], 13);

    // Ajouter la couche de tuiles OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(this.map);

    // Ajouter un marqueur à la position initiale
    this.marker = L.marker([lat, lng], {
      icon: this.defaultIcon,
      draggable: true
    }).addTo(this.map);

    // Mettre à jour les coordonnées quand le marqueur est déplacé
    this.marker.on('dragend', () => {
      const position = this.marker.getLatLng();
      this.adForm.patchValue({
        latitude: position.lat,
        longitude: position.lng
      });

      // Obtenir l'adresse à partir des coordonnées
      this.getAddressFromCoordinates(position.lat, position.lng);
    });

    // Ajouter un événement de clic sur la carte
    this.map.on('click', (e: L.LeafletMouseEvent) => {
      const { lat, lng } = e.latlng;

      // Déplacer le marqueur à la nouvelle position
      this.marker.setLatLng([lat, lng]);

      // Mettre à jour le formulaire
      this.adForm.patchValue({
        latitude: lat,
        longitude: lng
      });

      // Obtenir l'adresse à partir des coordonnées
      this.getAddressFromCoordinates(lat, lng);
    });

    // Forcer un redimensionnement de la carte après l'initialisation
    setTimeout(() => {
      this.map.invalidateSize();
    }, 200);
  }

  updateMarkerPosition(): void {
    if (!this.map || !this.marker) return;

    const lat = parseFloat(this.adForm.get('latitude')?.value);
    const lng = parseFloat(this.adForm.get('longitude')?.value);

    if (isNaN(lat) || isNaN(lng)) return;

    // Mettre à jour la position du marqueur
    this.marker.setLatLng([lat, lng]);

    // Centrer la carte sur la nouvelle position
    this.map.setView([lat, lng], this.map.getZoom());
  }

  /**
   * Set location coordinates and update the UI
   * @param lat Latitude
   * @param lng Longitude
   * @param accuracy Accuracy in meters (optional)
   */
  private setLocationCoordinates(lat: number, lng: number, accuracy?: number): void {
    console.log('Setting location coordinates:', { lat, lng, accuracy });

    // Update the form with the coordinates
    this.adForm.patchValue({
      latitude: lat,
      longitude: lng
    });

    // Set accuracy information if available
    if (accuracy) {
      this.locationAccuracy = accuracy;

      // Set accuracy text based on the accuracy level
      if (accuracy <= 10) {
        this.locationAccuracyText = 'Very high accuracy (±' + Math.round(accuracy) + 'm)';
      } else if (accuracy <= 50) {
        this.locationAccuracyText = 'Good accuracy (±' + Math.round(accuracy) + 'm)';
      } else if (accuracy <= 100) {
        this.locationAccuracyText = 'Moderate accuracy (±' + Math.round(accuracy) + 'm)';
      } else if (accuracy <= 500) {
        this.locationAccuracyText = 'Low accuracy (±' + Math.round(accuracy) + 'm)';
      } else if (accuracy <= 1000) {
        this.locationAccuracyText = 'Poor accuracy (±' + Math.round(accuracy) + 'm)';
      } else {
        this.locationAccuracyText = 'Very poor accuracy (±' + Math.round(accuracy) + 'm)';
      }
    } else {
      this.locationAccuracy = null;
      this.locationAccuracyText = '';
    }

    // Initialize the map if it doesn't exist yet
    if (!this.map) {
      this.initMap();
    } else {
      // Update the map view and marker position
      // Use a zoom level based on accuracy if available
      const zoomLevel = accuracy ? this.getZoomLevelForAccuracy(accuracy) : 15;
      this.map.setView([lat, lng], zoomLevel);
      this.marker.setLatLng([lat, lng]);

      // Add an accuracy circle if accuracy is available
      if (accuracy) {
        this.showAccuracyCircle(lat, lng, accuracy);
      }
    }

    // Get the address from the coordinates
    this.getAddressFromCoordinates(lat, lng);

    // Set the location flag
    this.locationSet = true;
    this.isLocationLoading = false;
  }

  /**
   * Calculate an appropriate zoom level based on accuracy
   * @param accuracy Accuracy in meters
   * @returns Zoom level (higher is more zoomed in)
   */
  private getZoomLevelForAccuracy(accuracy: number): number {
    if (accuracy <= 10) return 18; // Very high accuracy
    if (accuracy <= 50) return 16; // Good accuracy
    if (accuracy <= 100) return 15; // Moderate accuracy
    if (accuracy <= 500) return 14; // Poor accuracy
    if (accuracy <= 1000) return 13; // Very poor accuracy
    return 12; // Extremely poor accuracy
  }

  /**
   * Show an accuracy circle on the map
   * @param lat Latitude
   * @param lng Longitude
   * @param accuracy Accuracy in meters
   */
  private showAccuracyCircle(lat: number, lng: number, accuracy: number): void {
    // Remove any existing accuracy circles
    if (this.map) {
      this.map.eachLayer(layer => {
        if (layer instanceof L.Circle) {
          this.map.removeLayer(layer);
        }
      });

      // Add a new accuracy circle
      const circle = L.circle([lat, lng], {
        radius: accuracy,
        color: '#2196F3',
        fillColor: '#2196F3',
        fillOpacity: 0.15,
        weight: 2
      }).addTo(this.map);

      // Add a popup to the circle
      circle.bindPopup(`Location accuracy: ~${Math.round(accuracy)} meters`).openPopup();
    }
  }

  /**
   * Try to get location based on IP address as a fallback
   * @param errorMessage Primary error message
   * @param errorDetails Additional error details
   * @param suggestedAction Suggested action for the user
   */
  private tryIpBasedGeolocation(errorMessage: string, errorDetails: string, suggestedAction: string): void {
    console.log('Trying IP-based geolocation as fallback');

    // Ask the user if they want to try IP-based geolocation or use the Tunisia map
    const userChoice = confirm(
      `${errorMessage}\n\n${errorDetails}\n\n${suggestedAction}\n\nWould you like to try approximate location based on your IP address? Click OK for IP-based location or Cancel to see a map of Tunisia.`
    );

    if (userChoice) {
      // Try multiple IP geolocation services for better reliability
      this.tryMultipleIpGeoServices();
    } else {
      // User chose to use the Tunisia map
      this.setDefaultTunisiaLocation();
    }
  }

  /**
   * Try multiple IP geolocation services for better reliability
   */
  private tryMultipleIpGeoServices(): void {
    console.log('Trying multiple IP geolocation services');

    // First try ipapi.co
    fetch('https://ipapi.co/json/')
      .then(response => {
        if (!response.ok) {
          throw new Error(`ipapi.co returned ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('ipapi.co response:', data);

        // Check if we got valid coordinates and if the country is Tunisia
        if (data && data.latitude && data.longitude) {
          const isTunisia = data.country_code === 'TN' || data.country_name === 'Tunisia';

          if (isTunisia) {
            // We got a valid Tunisian location
            this.handleIpGeoSuccess(data.latitude, data.longitude, data.city);
          } else {
            // Not in Tunisia, try to get a default Tunisia location
            console.log('IP location is not in Tunisia, using default Tunisia location');
            this.setDefaultTunisiaLocation();
          }
        } else {
          // No valid coordinates, try the next service
          throw new Error('ipapi.co did not return valid coordinates');
        }
      })
      .catch(error => {
        console.error('ipapi.co error:', error);

        // Try a second service as fallback
        this.trySecondaryIpGeoService();
      });
  }

  /**
   * Try a secondary IP geolocation service
   */
  private trySecondaryIpGeoService(): void {
    console.log('Trying secondary IP geolocation service');

    // Try ipinfo.io as a fallback
    fetch('https://ipinfo.io/json')
      .then(response => {
        if (!response.ok) {
          throw new Error(`ipinfo.io returned ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('ipinfo.io response:', data);

        if (data && data.loc) {
          // ipinfo.io returns coordinates as "lat,lng"
          const [lat, lng] = data.loc.split(',').map((coord: string) => parseFloat(coord));

          // Check if we're in Tunisia
          const isTunisia = data.country === 'TN';

          if (isTunisia && !isNaN(lat) && !isNaN(lng)) {
            // We got a valid Tunisian location
            this.handleIpGeoSuccess(lat, lng, data.city);
          } else {
            // Not in Tunisia or invalid coordinates
            console.log('IP location is not in Tunisia or invalid, using default Tunisia location');
            this.setDefaultTunisiaLocation();
          }
        } else {
          throw new Error('ipinfo.io did not return valid coordinates');
        }
      })
      .catch(error => {
        console.error('Secondary IP geolocation error:', error);

        // Both services failed, use default Tunisia location
        alert('Could not determine your location. Showing a map of Tunisia instead.');
        this.setDefaultTunisiaLocation();
      });
  }

  /**
   * Handle successful IP geolocation
   */
  private handleIpGeoSuccess(lat: number, lng: number, city?: string): void {
    // IP-based geolocation is typically accurate to city level at best
    // Set a default accuracy of 5000 meters (5km)
    const accuracy = 5000;

    // Construct a location name
    const locationName = city ? `${city}, Tunisia` : 'Tunisia';

    alert(`Using approximate location based on your IP address (${locationName}). This is only accurate to the city level.`);

    // Set the location with the approximate coordinates
    this.setLocationCoordinates(lat, lng, accuracy);

    // Update the location name if we have a city
    if (city) {
      this.adForm.patchValue({
        locationName: locationName
      });
    }
  }

  /**
   * Show a quick location picker dialog
   */
  showQuickLocationPicker(): void {
    console.log('Showing quick location picker');

    // Define major cities in Tunisia with their coordinates
    const tunisiaCities = [
      { name: 'Tunis', lat: 36.8065, lng: 10.1815 },
      { name: 'Sfax', lat: 34.7406, lng: 10.7603 },
      { name: 'Sousse', lat: 35.8245, lng: 10.6346 },
      { name: 'Kairouan', lat: 35.6781, lng: 10.0986 },
      { name: 'Bizerte', lat: 37.2746, lng: 9.8714 },
      { name: 'Gabès', lat: 33.8881, lng: 10.0986 },
      { name: 'Ariana', lat: 36.8625, lng: 10.1956 },
      { name: 'Gafsa', lat: 34.4311, lng: 8.7757 },
      { name: 'Monastir', lat: 35.7780, lng: 10.8262 },
      { name: 'Nabeul', lat: 36.4513, lng: 10.7357 },
      { name: 'Hammamet', lat: 36.4000, lng: 10.6167 },
      { name: 'Djerba', lat: 33.8075, lng: 10.8451 }
    ];

    // Create a list of cities for the user to choose from
    let cityOptions = '';
    tunisiaCities.forEach((city, index) => {
      cityOptions += `${index + 1}. ${city.name}\n`;
    });

    // Show a prompt with the list of cities
    const userInput = prompt(
      `Please select a city by entering its number:\n\n${cityOptions}\nOr type a different location name:`,
      '1'
    );

    if (userInput === null) {
      // User cancelled
      return;
    }

    // Try to parse the input as a number (city index)
    const cityIndex = parseInt(userInput) - 1;

    if (!isNaN(cityIndex) && cityIndex >= 0 && cityIndex < tunisiaCities.length) {
      // User selected a city from the list
      const selectedCity = tunisiaCities[cityIndex];
      console.log('Selected city:', selectedCity);

      // Show the map if it's not already visible
      if (!this.showMap) {
        this.showMap = true;
        // Wait for the map container to be visible before initializing
        setTimeout(() => {
          this.initMap();

          // Set the location to the selected city
          this.setLocationCoordinates(selectedCity.lat, selectedCity.lng, 1000); // City-level accuracy (1km)

          // Update the location name
          this.adForm.patchValue({
            locationName: selectedCity.name + ', Tunisia'
          });
        }, 100);
      } else {
        // Set the location to the selected city
        this.setLocationCoordinates(selectedCity.lat, selectedCity.lng, 1000); // City-level accuracy (1km)

        // Update the location name
        this.adForm.patchValue({
          locationName: selectedCity.name + ', Tunisia'
        });
      }
    } else {
      // User entered a custom location name
      const locationName = userInput.trim();

      if (locationName) {
        // Search for the entered location
        this.searchLocation(locationName);
      }
    }
  }

  /**
   * Set a default location in Tunisia
   */
  private setDefaultTunisiaLocation(): void {
    console.log('Setting default Tunisia location');

    // Default to Tunis city center
    const tunisLat = 36.8065;
    const tunisLng = 10.1815;

    // Show the map if it's not already visible
    if (!this.showMap) {
      this.showMap = true;
      // Wait for the map container to be visible before initializing
      setTimeout(() => {
        this.initMap();

        // Add quick location buttons for major cities in Tunisia
        this.addTunisiaCityButtons();

        // End loading state
        this.isLocationLoading = false;
      }, 100);
    } else {
      // If map is already visible, just add the city buttons
      this.addTunisiaCityButtons();
      this.isLocationLoading = false;
    }
  }

  /**
   * Add quick location buttons for major cities in Tunisia
   */
  private addTunisiaCityButtons(): void {
    if (!this.map) return;

    // Remove any existing city buttons
    const existingControl = document.querySelector('.tunisia-cities-control');
    if (existingControl) {
      existingControl.remove();
    }

    // Create a custom control for the map
    const citiesControl = L.Control.extend({});
    const control = new citiesControl({ position: 'bottomright' });

    // Define major cities in Tunisia with their coordinates
    const tunisiaCities = [
      { name: 'Tunis', lat: 36.8065, lng: 10.1815 },
      { name: 'Sfax', lat: 34.7406, lng: 10.7603 },
      { name: 'Sousse', lat: 35.8245, lng: 10.6346 },
      { name: 'Kairouan', lat: 35.6781, lng: 10.0986 },
      { name: 'Bizerte', lat: 37.2746, lng: 9.8714 },
      { name: 'Gabès', lat: 33.8881, lng: 10.0986 },
      { name: 'Ariana', lat: 36.8625, lng: 10.1956 },
      { name: 'Gafsa', lat: 34.4311, lng: 8.7757 }
    ];

    // Add the control to the map
    control.onAdd = (map: L.Map) => {
      const div = L.DomUtil.create('div', 'tunisia-cities-control leaflet-bar leaflet-control');
      div.style.backgroundColor = 'white';
      div.style.padding = '10px';
      div.style.borderRadius = '4px';
      div.style.boxShadow = '0 1px 5px rgba(0,0,0,0.4)';

      const title = document.createElement('div');
      title.innerHTML = '<strong>Major Cities</strong>';
      title.style.marginBottom = '5px';
      title.style.textAlign = 'center';
      div.appendChild(title);

      // Create buttons for each city
      tunisiaCities.forEach(city => {
        const button = document.createElement('button');
        button.textContent = city.name;
        button.style.display = 'block';
        button.style.width = '100%';
        button.style.margin = '2px 0';
        button.style.padding = '5px';
        button.style.backgroundColor = '#f8f9fa';
        button.style.border = '1px solid #ddd';
        button.style.borderRadius = '4px';
        button.style.cursor = 'pointer';
        button.style.fontSize = '12px';

        button.addEventListener('click', (e) => {
          // Prevent the click from propagating to the map
          L.DomEvent.stopPropagation(e);

          // Set the location to the selected city
          this.setLocationCoordinates(city.lat, city.lng, 1000); // City-level accuracy (1km)

          // Update the location name
          this.adForm.patchValue({
            locationName: city.name + ', Tunisia'
          });

          // Center the map on the selected city
          this.map.setView([city.lat, city.lng], 13);
        });

        div.appendChild(button);
      });

      return div;
    };

    control.addTo(this.map);
  }

  /**
   * Search for a location by name
   * @param searchTerm The location name to search for
   */
  searchLocation(searchTerm: string): void {
    if (!searchTerm || searchTerm.trim() === '') {
      alert('Please enter a location to search for.');
      return;
    }

    console.log('Searching for location:', searchTerm);
    this.isLocationLoading = true;

    // Add "Tunisia" to the search term if it's not already included
    // This helps prioritize Tunisian locations
    let enhancedSearchTerm = searchTerm;
    if (!searchTerm.toLowerCase().includes('tunisia') && !searchTerm.toLowerCase().includes('tunisie')) {
      enhancedSearchTerm = searchTerm + ' Tunisia';
    }

    console.log('Enhanced search term:', enhancedSearchTerm);

    // Use Nominatim for geocoding with country code to prioritize Tunisia
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(enhancedSearchTerm)}&limit=1&countrycodes=tn`;

    fetch(url, {
      headers: {
        'Accept-Language': 'en', // Request English results
        'User-Agent': 'SpeedyGoApp' // Identify our application
      }
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Nominatim API returned ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('Search results:', data);

        if (data && data.length > 0) {
          const result = data[0];
          const lat = parseFloat(result.lat);
          const lng = parseFloat(result.lon);

          // Set a default accuracy based on the type of result
          // This is an approximation since Nominatim doesn't provide accuracy
          let accuracy = 1000; // Default to 1km

          if (result.type === 'house' || result.type === 'building') {
            accuracy = 20; // Building level accuracy
          } else if (result.type === 'street' || result.type === 'road') {
            accuracy = 100; // Street level accuracy
          } else if (result.type === 'suburb' || result.type === 'neighbourhood') {
            accuracy = 500; // Neighborhood level accuracy
          } else if (result.type === 'city' || result.type === 'town') {
            accuracy = 2000; // City level accuracy
          } else if (result.type === 'state' || result.type === 'region') {
            accuracy = 10000; // State level accuracy
          } else if (result.type === 'country') {
            accuracy = 50000; // Country level accuracy
          }

          // Set the location with the found coordinates
          this.setLocationCoordinates(lat, lng, accuracy);

          // Update the location name
          this.adForm.patchValue({
            locationName: result.display_name
          });

          alert(`Location found: ${result.display_name}`);
        } else {
          alert('No locations found for your search. Please try a different search term.');
          this.isLocationLoading = false;
        }
      })
      .catch(error => {
        console.error('Error searching for location:', error);
        alert('Error searching for location. Please try again later.');
        this.isLocationLoading = false;
      });
  }

  getAddressFromCoordinates(lat: number, lng: number): void {
    console.log('Getting address for coordinates:', { lat, lng });

    // Create an abort controller for the fetch request
    const controller = new AbortController();
    const signal = controller.signal;

    // Set a timeout to abort the fetch if it takes too long
    const fetchTimeout = setTimeout(() => {
      controller.abort();
      console.warn('Geocoding request timed out');
      this.isLocationLoading = false;
    }, 7000); // 7 second timeout

    // Utiliser le service de géocodage inverse de Nominatim (OpenStreetMap)
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`;

    fetch(url, {
      headers: {
        'Accept-Language': 'en', // Request English results
        'User-Agent': 'SpeedyGoApp' // Identify our application
      },
      signal: signal
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Nominatim API returned ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        // Clear the timeout since we got a response
        clearTimeout(fetchTimeout);

        console.log('Geocoding response:', data);

        if (data && data.display_name) {
          this.adForm.patchValue({
            locationName: data.display_name
          });
          console.log('Successfully set location name:', data.display_name);
        } else {
          console.warn('No display_name found in geocoding response');
          // Set a generic location name based on coordinates
          this.adForm.patchValue({
            locationName: `Location at ${lat.toFixed(6)}, ${lng.toFixed(6)}`
          });
        }
      })
      .catch(error => {
        // Clear the timeout since we got an error
        clearTimeout(fetchTimeout);

        console.error('Error fetching address:', error);

        // Set a generic location name based on coordinates
        this.adForm.patchValue({
          locationName: `Location at ${lat.toFixed(6)}, ${lng.toFixed(6)}`
        });
      });
  }

  selectedFiles: File[] = [];
  rejectedByAI: boolean = false;
  pendingReview: boolean = false;
  adAccepted: boolean = false;
  isLoading: boolean = false;

  createdAd?: Ad; // pour afficher le rejectionReason

  onFileSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      // Limit to 5 files maximum
      const newFiles = Array.from(event.target.files) as File[];
      const totalFiles = [...this.selectedFiles, ...newFiles];

      if (totalFiles.length > 5) {
        alert('You can select a maximum of 5 images.');
        this.selectedFiles = totalFiles.slice(0, 5) as File[];
      } else {
        this.selectedFiles = totalFiles as File[];
      }

      console.log('Files selected:', this.selectedFiles.length);
      this.selectedFiles.forEach((file, index) => {
        console.log(`File ${index + 1}: ${file.name}, ${file.type}, ${file.size} bytes`);
      });
    } else {
      console.log('No files selected');
    }
  }

  /**
   * Remove a file from the selected files array
   * @param index Index of the file to remove
   */
  removeFile(index: number): void {
    if (index >= 0 && index < this.selectedFiles.length) {
      this.selectedFiles = [
        ...this.selectedFiles.slice(0, index),
        ...this.selectedFiles.slice(index + 1)
      ] as File[];
    }
  }

  /**
   * Helper method to format a date with time
   * @param dateStr Date string in format YYYY-MM-DD
   * @param timeStr Time string in format HH:MM
   * @returns ISO string with correct date and time
   */
  formatDateWithTime(dateStr: string, timeStr: string): string {
    // Create a date object from the date string
    const date = new Date(dateStr);

    // Parse the time string (format: HH:MM)
    if (timeStr) {
      const [hours, minutes] = timeStr.split(':').map(Number);

      // Set the hours and minutes on the date
      date.setHours(hours, minutes, 0, 0);
    }

    // Return the ISO string
    return date.toISOString();
  }

  /**
   * Calculate the form completion progress as a percentage
   * @returns Percentage of form completion (0-100)
   */
  getFormProgress(): number {
    if (!this.adForm) return 0;

    // Define required fields
    const requiredFields = ['title', 'description', 'category', 'startDate', 'endDate', 'startTime', 'endTime'];

    // Count how many required fields are filled
    let filledFields = 0;
    requiredFields.forEach(field => {
      if (this.adForm.get(field)?.valid) {
        filledFields++;
      }
    });

    // Calculate percentage
    const progress = Math.round((filledFields / requiredFields.length) * 100);
    return progress;
  }

  onSubmit(): void {
    // Vérifier si le formulaire est valide
    if (!this.adForm.valid) {
      alert('Veuillez remplir correctement tous les champs obligatoires.');
      return;
    }

    // Vérifier si des images ont été sélectionnées
    if (!this.selectedFiles || this.selectedFiles.length === 0) {
      alert('Veuillez sélectionner au moins une image.');
      return;
    }

    this.isLoading = true; // ✅ Affiche le loader

      // Process tags (convert comma-separated string to array)
      const tagsString = this.adForm.value.tags || '';
      const tagsArray = tagsString.split(',')
        .map((tag: string) => tag.trim())
        .filter((tag: string) => tag.length > 0);

      // Process location coordinates
      let locationArray: number[] | undefined = undefined;
      if (this.adForm.value.longitude && this.adForm.value.latitude) {
        locationArray = [
          parseFloat(this.adForm.value.longitude),
          parseFloat(this.adForm.value.latitude)
        ];
      }

      // Determine expiration date based on toggle
      let expiresAt: string | undefined = undefined;

      // If toggle is enabled, use end date and time as expiration date
      if (this.adForm.value.useEndDateAsExpiry) {
        // Use the helper method to format the date with time
        expiresAt = this.formatDateWithTime(this.adForm.value.endDate, this.adForm.value.endTime);

        // Log the expiration date for debugging
        console.log('Setting expiration date:', expiresAt);
        console.log('End date:', this.adForm.value.endDate);
        console.log('End time:', this.adForm.value.endTime);
      }

      // Create a copy of the form values and remove the toggle field
      const formValues = { ...this.adForm.value };
      delete formValues.useEndDateAsExpiry;

      const formattedData: Ad = {
        title: formValues.title,
        description: formValues.description,
        category: formValues.category,
        startDate: this.formatDateWithTime(formValues.startDate, formValues.startTime),
        endDate: this.formatDateWithTime(formValues.endDate, formValues.endTime),
        startTime: formValues.startTime,
        endTime: formValues.endTime,
        expiresAt: expiresAt,
        tags: tagsArray,
        locationName: formValues.locationName,
        location: locationArray,
        price: formValues.price ? parseFloat(formValues.price) : undefined
      };

      // Use the custom service to send all selected images
      this.customAdService.createAdWithMultipleImages(formattedData, this.selectedFiles).subscribe({
        next: async (response) => {
          this.isLoading = false; // ✅ Masque le loader

          let createdAd: Ad;

          if (response instanceof Blob) {
            const text = await response.text();
            createdAd = JSON.parse(text);
          } else {
            createdAd = response;
          }

          this.createdAd = createdAd;

          console.log('🧾 Ad reçue (JSON)', createdAd);
          console.log('Images in response:', createdAd.images ? createdAd.images.length : 0);
          console.log('Single image in response:', createdAd.image ? 'Yes' : 'No');

          this.rejectedByAI = false;
          this.pendingReview = false;
          this.adAccepted = false;

          switch (createdAd.status) {
            case 'REJECTED':
              this.rejectedByAI = true;
              break;
            case 'PENDING_ADMIN_REVIEW':
              this.pendingReview = true;
              break;
              case 'APPROVED':
                this.adAccepted = true;
                setTimeout(() => {
                  this.dialogRef.close(true); // ✅ ferme la popup
                }, 1500);
                break;
          }
        },
        error: (err) => {
          this.isLoading = false; // ✅ Masque le loader en cas d'erreur

          console.error('Erreur lors de la soumission', err);
          console.error('Détails de l\'erreur:', {
            status: err.status,
            statusText: err.statusText,
            error: err.error,
            message: err.message,
            name: err.name
          });

          // Log the request that was sent
          console.log('Données envoyées:', {
            ad: formattedData,
            images: this.selectedFiles.map(f => f.name)
          });

          const message = err.error?.message || '';
          console.log('Message d\'erreur:', message);

          if (message.includes('REJECTED') || message.toLowerCase().includes('automatiquement rejetée')) {
            this.rejectedByAI = true;
          } else {
            alert(`Erreur: ${message || 'Veuillez vérifier les données saisies'}`);
          }
        }
      });
    }
  }
