<app-header-front></app-header-front>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">Create Promotion</h2>

    <form [formGroup]="promotionForm" (ngSubmit)="onSubmit()">
      
      <!-- Promotion Title -->
      <div class="form-group mb-3">
        <label for="title" class="form-label fw-bold">Promotion Title</label>
        <input id="title" type="text" formControlName="title" class="form-control rounded" required>
      </div>

      <!-- Description -->
      <div class="form-group mb-3">
        <label for="description" class="form-label fw-bold">Description</label>
        <textarea id="description" formControlName="description" class="form-control rounded" rows="4" required></textarea>
      </div>

      <!-- Discount Type -->
      <div class="form-group mb-3">
        <label for="discountType" class="form-label fw-bold">Discount Type</label>
        <select id="discountType" formControlName="discountType" class="form-control rounded">
          <option value="POURCENTAGE">Percentage (%)</option>
          <option value="FIXEDAMOUNT">Fixed Amount</option>
          <option value="FREEDELIVERY">Free Delivery</option>
        </select>
      </div>

      <!-- Discount Amount (Only for POURCENTAGE & FIXEDAMOUNT) -->
      <div class="form-group mb-3" *ngIf="promotionForm.get('discountType')?.value !== 'FREEDELIVERY'">
        <label for="discount" class="form-label fw-bold">Discount Amount</label>
        <input id="discount" type="number" formControlName="discount" class="form-control rounded" min="0" required>
      </div>

      <!-- Start Date -->
      <div class="form-group mb-3">
        <label for="startDate" class="form-label fw-bold">Start Date</label>
        <input id="startDate" type="date" formControlName="startDate" class="form-control rounded" required>
      </div>

      <!-- End Date -->
      <div class="form-group mb-3">
        <label for="endDate" class="form-label fw-bold">End Date</label>
        <input id="endDate" type="date" formControlName="endDate" class="form-control rounded" required>
      </div>

      <!-- Promotion Status -->
      <div class="form-group mb-3">
        <label for="promotionStatus" class="form-label fw-bold">Promotion Status</label>
        <select id="promotionStatus" formControlName="promotionStatus" class="form-control rounded">
          <option value="ACTIVE">Active</option>
          <option value="EXPIRED">Expired</option>
        </select>
      </div>

      <!-- Submit Button -->
      <div class="d-flex justify-content-center">
        <button type="submit" class="btn btn-primary btn-lg px-4" [disabled]="promotionForm.invalid">
          <i class="fas fa-paper-plane"></i> Submit
        </button>
      </div>

    </form>

    <!-- Return Button -->
    <div class="d-flex justify-content-center mt-3">
      <a class="btn btn-secondary btn-lg px-4" [routerLink]="['/promo']">
        <i class="fas fa-arrow-left"></i> Return
      </a>
    </div>

  </div>
</div>

<app-footer-front></app-footer-front>
