/* Dialog Container */
.ad-detail-dialog {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Dialog Header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
  position: relative;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-title i {
  margin-right: 8px;
}

.header-status {
  margin-left: auto;
  margin-right: 16px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Dialog Content */
.dialog-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
  background-color: #f8f9fa;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.tab-item {
  padding: 12px 20px;
  font-weight: 600;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab-item.active {
  color: #4361ee;
  border-bottom-color: #4361ee;
}

.tab-item i {
  margin-right: 8px;
}

/* Main Content Area */
.main-content-area {
  padding: 0 10px;
}

/* Content Row for Images and Description */
.content-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.content-column {
  flex: 1;
  min-width: 0; /* Prevents flex items from overflowing */
}

/* Section Styling */
.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  position: relative;
  padding-bottom: 8px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, #4361ee, #4895ef);
  border-radius: 3px;
}

/* Info Card Styling */
.info-card {
  background-color: white;
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.schedule-row, .metadata-row {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.schedule-row .info-item, .metadata-row .info-item {
  flex: 1;
}

.user-id {
  word-break: break-all;
}

.rejection-reason {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

/* Ad Images Section */
.ad-images-section {
  margin-bottom: 24px;
}

.ad-images-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: white;
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ad-image-main {
  width: 100%;
  height: 250px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.03);
}

.ad-images-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.gallery-image-container {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-image:hover {
  transform: scale(1.1);
}

.no-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #adb5bd;
}

.no-images i {
  font-size: 48px;
  margin-bottom: 16px;
}

/* Info Section */
.info-section {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

/* Info Item Styling */
.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
}

.info-label i {
  margin-right: 6px;
  width: 16px;
  text-align: center;
  color: #4361ee;
}

.info-value {
  font-size: 15px;
  color: #333;
  line-height: 1.4;
}

/* Description Section */
.ad-description-section {
  margin-bottom: 24px;
}

.description-content {
  background-color: white;
  padding: 16px;
  border-radius: 10px;
  white-space: pre-line;
  line-height: 1.6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  max-height: 300px;
  overflow-y: auto;
}

/* Location Section */
.ad-location-section {
  margin-bottom: 24px;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.no-location {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: white;
  border-radius: 10px;
  color: #adb5bd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.no-location i {
  font-size: 32px;
  margin-bottom: 8px;
}

/* Tags Section */
.ad-tags-section, .tags-section {
  margin-bottom: 24px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  background-color: white;
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tag-badge {
  background: linear-gradient(135deg, #4895ef 0%, #4361ee 100%);
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 20px;
  display: inline-block;
  transition: all 0.2s ease;
}

.tag-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

.no-tags {
  color: #adb5bd;
  font-style: italic;
}

/* Badge Category */
.badge-category {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.2s ease;
}

.badge-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

/* Price Tag */
.price-tag {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.2s ease;
}

.price-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(76, 201, 160, 0.2);
}

.price-na {
  color: #adb5bd;
  font-style: italic;
}

/* Dialog Footer */
.dialog-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e9ecef;
  background-color: white;
}

.footer-actions {
  display: flex;
  gap: 10px;
}

.btn {
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Rejection Form */
.rejection-form {
  background-color: white;
  padding: 16px 24px;
  border-top: 1px solid #e9ecef;
}

.form-container {
  max-width: 100%;
}

.form-field {
  margin-bottom: 16px;
}

.form-field label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.rejection-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.rejection-textarea:focus {
  outline: none;
  border-color: #4361ee;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-hint {
  display: block;
  margin-top: 5px;
  color: #6c757d;
  font-style: italic;
  font-size: 12px;
}

/* Status Badges */
.badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.bg-success {
  background: linear-gradient(135deg, #4cc9a0 0%, #1a936f 100%) !important;
  color: white !important;
}

.bg-warning {
  background: linear-gradient(135deg, #ffbe0b 0%, #fb8500 100%) !important;
  color: #000 !important;
}

.bg-danger {
  background: linear-gradient(135deg, #f72585 0%, #b5179e 100%) !important;
  color: white !important;
}

.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
  color: white !important;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .content-row {
    flex-direction: column;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 16px;
  }

  .footer-actions {
    width: 100%;
    justify-content: space-between;
  }

  .btn-primary {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dialog-header {
    flex-wrap: wrap;
    gap: 8px;
  }

  .dialog-title {
    max-width: 100%;
    margin-bottom: 8px;
    font-size: 18px;
  }

  .header-status {
    margin-left: 0;
    margin-right: auto;
  }

  .ad-image-main {
    height: 200px;
  }

  .gallery-image-container {
    width: 70px;
    height: 70px;
  }

  .section-title {
    font-size: 16px;
  }

  .info-label {
    font-size: 13px;
  }

  .info-value {
    font-size: 14px;
  }

  .description-content {
    max-height: 200px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .content-row {
    gap: 16px;
  }
}
