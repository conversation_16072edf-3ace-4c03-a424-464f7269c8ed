import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AdReportMonitoringService {
  private apiUrl = 'http://localhost:8091/speedygo';

  constructor(private http: HttpClient) { }

  /**
   * Manually trigger the check for reported ads
   * @returns Observable with the response containing warning and deletion counts
   */
  processReportedAds(): Observable<any> {
    console.log('Manually triggering check for reported ads');
    return this.http.post(`${this.apiUrl}/adReportMonitoring/processReportedAds`, {});
  }

  /**
   * Manually trigger the check for highly reported ads (legacy method)
   * @returns Observable with the response
   * @deprecated Use processReportedAds() instead
   */
  checkAndDeleteHighlyReportedAds(): Observable<any> {
    console.log('Manually triggering check for highly reported ads (legacy method)');
    return this.http.post(`${this.apiUrl}/adReportMonitoring/checkAndDeleteHighlyReportedAds`, {});
  }

  /**
   * Get the current ad complaint counts
   * @returns Observable with the response
   */
  getAdComplaintCounts(): Observable<any> {
    console.log('Getting ad complaint counts');
    return this.http.get(`${this.apiUrl}/adReportMonitoring/adComplaintCounts`);
  }


}
