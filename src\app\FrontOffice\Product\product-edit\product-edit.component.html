<!-- src/app/product-edit/product-edit.component.html -->
<app-header-front></app-header-front>
<div class="container" *ngIf="productForm">

  <h2>Modifier le produit</h2>
  <form [formGroup]="productForm" (ngSubmit)="onSubmit()">
    <!-- Champ caché pour l'ID -->
    <input type="hidden" formControlName="id">

    <div class="form-group">
      <label for="name">Nom</label>
      <input id="name" formControlName="name" class="form-control" required>
    </div>
    <div class="form-group">
      <label for="description">Description</label>
      <textarea id="description" formControlName="description" class="form-control"></textarea>
    </div>
    <div class="form-group">
      <label for="price">Prix</label>
      <input id="price" type="number" formControlName="price" class="form-control" required>
    </div>
    <div class="form-group">
      <label for="stockQuantity">Quantité en stock</label>
      <input id="stockQuantity" type="number" formControlName="stockQuantity" class="form-control" required>
    </div>
    <div class="form-group">
      <label for="category">Catégorie</label>
      <input id="category" formControlName="category" class="form-control">
    </div>
    <div class="form-group">
      <label for="image">URL de l'image</label>
      <input id="image" formControlName="image" class="form-control">
    </div>
    <button type="submit" class="btn btn-primary">Mettre à jour</button>
  </form>
</div>
<app-footer-front></app-footer-front>
