import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Ad, AdControllerService } from 'src/app/openapi';
import { CommonModule } from '@angular/common';
import { FooterFrontComponent } from '../../footer-front/footer-front.component';
import { HeaderFrontComponent } from '../../header-front/header-front.component';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import * as L from 'leaflet';

@Component({
  selector: 'app-ad-details',
  standalone: true,
  imports: [CommonModule, FooterFrontComponent, HeaderFrontComponent],
  templateUrl: './ad-details.component.html',
  styleUrls: ['./ad-details.component.css']
})
export class AdDetailsComponent implements OnInit, AfterViewInit {
  ad: Ad = { title: '', description: '', image: '', category: 'OTHER', startDate: '', endDate: '', status: Ad.StatusEnum.Approved };
  adId!: string;

  @ViewChild('map') mapElement!: ElementRef;
  map!: L.Map;

  // Icône par défaut pour le marqueur
  defaultIcon = L.icon({
    iconUrl: 'assets/leaflet/marker-icon.png',
    shadowUrl: 'assets/leaflet/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  });

  constructor(
    private adService: AdControllerService,
    private route: ActivatedRoute,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.adId = this.route.snapshot.paramMap.get('id')!;
    if (!this.adId) {
      alert('Ad ID is missing');
      this.router.navigate(['/ads']);
      return;
    }
    this.loadAdDetails();
    this.incrementViewCount();
  }

  ngAfterViewInit(): void {
    // La carte sera initialisée après le chargement des détails de l'annonce
    // Initialiser le carousel après le chargement des données
    setTimeout(() => {
      this.initCarousel();
    }, 1000);
  }

  /**
   * Initialise manuellement le carousel Bootstrap
   */
  initCarousel(): void {
    const carouselEl = document.getElementById('adImagesCarousel');
    if (carouselEl) {
      // @ts-ignore - Bootstrap est chargé globalement
      const carousel = new bootstrap.Carousel(carouselEl, {
        interval: 5000,
        wrap: true,
        touch: true
      });
    }
  }

  initMap(): void {
    if (!this.mapElement || !this.ad.location || this.ad.location.length !== 2) return;

    const latitude = this.ad.location[1];
    const longitude = this.ad.location[0];

    // Si la carte existe déjà, la supprimer pour éviter les doublons
    if (this.map) {
      this.map.remove();
    }

    // Initialiser la carte Leaflet
    this.map = L.map(this.mapElement.nativeElement).setView([latitude, longitude], 13);

    // Ajouter la couche de tuiles OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(this.map);

    // Ajouter un marqueur à la position
    L.marker([latitude, longitude], {
      icon: this.defaultIcon
    }).addTo(this.map)
      .bindPopup(this.ad.locationName || 'Location')
      .openPopup();

    // Forcer un redimensionnement de la carte après l'initialisation
    setTimeout(() => {
      this.map.invalidateSize();
    }, 200);
  }

  loadAdDetails(): void {
    this.adService.getAdById(this.adId).subscribe({
      next: (response: any) => {
        if (response instanceof Blob) {
          // Example: Convert Blob to text if expecting JSON encoded in Blob
          const reader = new FileReader();
          reader.onload = () => {
            this.ad = JSON.parse(reader.result as string);
          };
          reader.readAsText(response);
        } else {
          this.ad = response; // Assuming response is directly in JSON format
        }
        console.log("Ad details:", this.ad);
        console.log("Ad price:", this.ad.price);
        console.log("Ad price type:", typeof this.ad.price);
        console.log("Ad expiration date:", this.ad.expiresAt);
        console.log("Ad end date:", this.ad.endDate);
        console.log("Ad end time:", this.ad.endTime);

        // Initialiser la carte si des coordonnées sont disponibles
        setTimeout(() => {
          if (this.ad.location && this.ad.location.length === 2) {
            this.initMap();
          }

          // Initialiser le carousel après le chargement des données
          this.initCarousel();
        }, 100);
      },
      error: (err: any) => {
        console.error('Error retrieving ad details', err);
        alert('Failed to load ad details.');
      }
    });
  }

  incrementViewCount(): void {
    if (this.adId) {
      this.adService.incrementViewCount(this.adId).subscribe({
        next: (updatedAd) => {
          console.log('View count incremented:', updatedAd.viewCount);
        },
        error: (err: any) => {
          console.error('Error incrementing view count', err);
        }
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/adlist']);
  }

  // Debug function to check price
  getPrice(): string {
    console.log('Details - Price type:', typeof this.ad.price);
    console.log('Details - Price value:', this.ad.price);
    return this.ad.price !== undefined && this.ad.price !== null ? `${this.ad.price} TND` : 'Not specified';
  }

  /**
   * Check if the ad is expiring soon (within 7 days)
   * @returns True if the ad expires within 7 days
   */
  isExpiringSoon(): boolean {
    // If no expiration date is set, it's not expiring soon
    if (!this.ad || !this.ad.expiresAt) {
      return false;
    }

    try {
      const expiresAt = new Date(this.ad.expiresAt);
      const now = new Date();

      // Calculate the difference in days
      const diffTime = expiresAt.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Return true if the ad expires within 7 days
      return diffDays >= 0 && diffDays <= 7;
    } catch (error) {
      console.error('Error checking expiration date:', error);
      return false;
    }
  }

  /**
   * Get all images from the ad
   * Returns an array of image strings (base64)
   */
  getImages(): string[] {
    // Log the ad object to see what images it contains
    console.log('Ad in getImages (details):', this.ad.id, 'Images:', this.ad.images, 'Image:', this.ad.image);

    // If ad has images array, return it
    if (this.ad.images && this.ad.images.length > 0) {
      console.log('Returning multiple images (details):', this.ad.images.length);
      return this.ad.images;
    }

    // If ad only has a single image, return it as an array
    if (this.ad.image) {
      console.log('Returning single image (details)');
      return [this.ad.image];
    }

    // If no images, return empty array
    console.log('No images found (details)');
    return [];
  }

  /**
   * Vérifie si la carte doit être affichée
   * @returns true si l'annonce a des coordonnées de localisation
   */
  shouldShowMap(): boolean {
    return this.ad.location !== undefined &&
           this.ad.location !== null &&
           this.ad.location.length === 2;
  }

  /**
   * Format the expiry date to show the correct time
   * @returns Formatted expiry date string
   */
  formatExpiryDate(): string {
    if (!this.ad.expiresAt) {
      return 'Not specified';
    }

    try {
      // Create a date object from the expiration date
      const expiryDate = new Date(this.ad.expiresAt);

      // Log the expiration date for debugging
      console.log('Expiry date object:', expiryDate);
      console.log('Expiry date ISO string:', this.ad.expiresAt);
      console.log('Expiry date local string:', expiryDate.toLocaleString());

      // Format the date with the correct time
      return expiryDate.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      console.error('Error formatting expiry date:', error);
      return 'Invalid date';
    }
  }

  /**
   * Navigue manuellement dans le carousel
   * @param direction Direction ('prev' ou 'next')
   */
  navigateCarousel(direction: 'prev' | 'next'): void {
    // Récupérer l'élément carousel
    const carouselEl = document.getElementById('adImagesCarousel');
    if (!carouselEl) return;

    // Récupérer toutes les slides
    const items = carouselEl.querySelectorAll('.carousel-item');
    if (items.length <= 1) return;

    // Trouver l'index de la slide active
    let activeIndex = 0;
    items.forEach((item, index) => {
      if (item.classList.contains('active')) {
        activeIndex = index;
      }
    });

    // Calculer le nouvel index
    let newIndex;
    if (direction === 'next') {
      newIndex = (activeIndex + 1) % items.length;
    } else {
      newIndex = (activeIndex - 1 + items.length) % items.length;
    }

    // Retirer la classe active de toutes les slides
    items.forEach(item => item.classList.remove('active'));

    // Ajouter la classe active à la nouvelle slide
    items[newIndex].classList.add('active');

    // Mettre à jour les indicateurs
    const indicators = carouselEl.querySelectorAll('.carousel-indicators button');
    if (indicators.length > 0) {
      indicators.forEach((indicator, index) => {
        if (index === newIndex) {
          indicator.classList.add('active');
          indicator.setAttribute('aria-current', 'true');
        } else {
          indicator.classList.remove('active');
          indicator.removeAttribute('aria-current');
        }
      });
    }
  }
}
