<app-navbar-back></app-navbar-back>

<app-sidebar-back></app-sidebar-back>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">Modify Add Request</h2>

    <!-- 🚗 Vehicle Edit Form -->
    <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">

      <!-- Brand -->
      <div class="form-group mb-3">
        <label for="brand" class="form-label fw-bold">Brand</label>
        <input type="text" id="brand" class="form-control rounded" formControlName="brand" readonly>
      </div>

      <!-- Model -->
      <div class="mb-3">
        <label for="model" class="form-label">Model</label>
        <input type="text" id="model" class="form-control" formControlName="model" readonly>
      </div>

      <!-- Capacity -->
      <div class="mb-3">
        <label for="capacity" class="form-label">Capacity</label>
        <input type="text" id="capacity" class="form-control" formControlName="capacity" readonly>
      </div>

      <!-- License Plate -->
      <div class="mb-3">
        <label for="licensePlate" class="form-label">License Plate</label>
        <input type="text" id="licensePlate" class="form-control" formControlName="licensePlate" readonly>
      </div>

      <!-- VIN -->
      <div class="mb-3">
        <label for="vin" class="form-label">VIN</label>
        <input type="text" id="vin" class="form-control" formControlName="vin" readonly>
      </div>

      <!-- Fabrication Date -->
      <div class="mb-3">
        <label for="fabricationDate" class="form-label">Fabrication Date</label>
        <input type="date" id="fabricationDate" class="form-control" formControlName="fabricationDate" readonly>
      </div>

      <!-- Fuel Type -->
      <div class="mb-3">
        <label for="fuelType" class="form-label">Fuel Type</label>
        <input type="text" id="fuelType" class="form-control" formControlName="fuelType" readonly>
      </div>

      <!-- Registration Document Upload -->
      <div class="form-group mb-3">
        <label for="image" class="form-label">Registration Document</label>
        <input type="file" id="image" class="form-control" (change)="onFileSelected($event)" accept="image/*">
      </div>

      <!-- Vehicle Status Dropdown -->
      <div class="form-group mb-3">
        <label for="vehicleStatus" class="form-label">Vehicle Status</label>
        <select id="vehicleStatus" formControlName="vehicleStatus" class="form-control">
          <option value="" disabled selected>-- Select Vehicle Status --</option>
          <option *ngFor="let status of vehicleStatuses" [value]="status">{{ status }}</option>
        </select>
      </div>

      <div class="form-group mb-3">
        <label for="vehicleType" class="form-label">Vehicle Status</label>
        <select id="vehicleType" formControlName="vehicleType" class="form-control" readonly >
          <option value="" disabled selected>-- Select Vehicle Type --</option>
          <option *ngFor="let type of vehicleTypes" [value]="type">{{ type }}</option>
        </select>
      </div>

      <div class="form-group mb-3">
        <label for="vehicleStatusD" class="form-label">Vehicle Status</label>
        <select id="vehicleStatusD" formControlName="vehicleStatusD" class="form-control">
          <option value="" disabled selected>-- Select Vehicle add Status --</option>
          <option *ngFor="let addstatus of vehicleStatusD" [value]="addstatus">{{ addstatus }}</option>
        </select>
      </div>



      <!-- 🚀 Action Buttons -->
      <div class="d-flex justify-content-center gap-3">
        <button type="submit" class="btn btn-primary btn-lg px-4">
          <i class="fas fa-save"></i> Update
        </button>

        <button type="button" class="btn btn-secondary btn-lg px-4" (click)="cancelEdit()">
          <i class="fas fa-times"></i> Cancel
        </button>
      </div>

    </form>
  </div>
</div>
