/* ✅ Centered Form Card */
.card {
  max-width: 700px;
  margin: auto;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
  border: none;
}

/* ✅ Form Labels */
.form-label {
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 8px;
}

/* ✅ Input Fields */
.form-control {
  font-size: 16px;
  padding: 10px;
  border-radius: 0 8px 8px 0 !important;
  border: 1px solid #ced4da;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.input-group-text {
  background-color: #f8f9fa;
  border-radius: 8px 0 0 8px !important;
  color: #6c757d;
}

/* ✅ Character Counter */
.character-counter {
  text-align: right;
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

/* ✅ Progress Steps */
.progress-container {
  margin: 20px 0 30px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e9ecef;
  color: #6c757d;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 14px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background-color: #4e73df;
  color: white;
}

.step.active .step-label {
  color: #4e73df;
  font-weight: 600;
}

.step.completed .step-number {
  background-color: #1cc88a;
  color: white;
}

.step-connector {
  flex-grow: 1;
  height: 3px;
  background-color: #e9ecef;
  margin: 0 10px;
  position: relative;
  top: -20px;
  z-index: 0;
}

/* ✅ Form Steps */
.form-step {
  transition: all 0.3s ease;
}

.step-title {
  color: #4e73df;
  font-weight: 600;
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

/* ✅ Priority Options */
.priority-options {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.priority-option {
  flex: 1;
}

.priority-radio {
  display: none;
}

.priority-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.priority-label i {
  font-size: 24px;
  margin-bottom: 8px;
}

.priority-label[data-priority="LOW"] i {
  color: #36b9cc;
}

.priority-label[data-priority="MEDIUM"] i {
  color: #f6c23e;
}

.priority-label[data-priority="HIGH"] i {
  color: #e74a3b;
}

.priority-radio:checked + .priority-label {
  border-color: #4e73df;
  background-color: rgba(78, 115, 223, 0.1);
  font-weight: 600;
}

/* ✅ Contact Options */
.contact-options {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.contact-option {
  flex: 1;
}

.contact-radio {
  display: none;
}

.contact-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.contact-label i {
  font-size: 24px;
  margin-bottom: 8px;
  color: #4e73df;
}

.contact-radio:checked + .contact-label {
  border-color: #4e73df;
  background-color: rgba(78, 115, 223, 0.1);
  font-weight: 600;
}

/* ✅ File Upload Area */
.custom-file-upload {
  border: 2px dashed #4e73df;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  background-color: rgba(78, 115, 223, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 20px;
}

.custom-file-upload:hover {
  background-color: rgba(78, 115, 223, 0.1);
  border-color: #2e59d9;
}

.upload-icon {
  font-size: 40px;
  color: #4e73df;
  margin-bottom: 15px;
}

.upload-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.upload-hint {
  font-size: 14px;
  color: #6c757d;
}

/* ✅ Selected Files */
.selected-files {
  margin-top: 15px;
}

.file-preview {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e9ecef;
}

.file-icon {
  font-size: 24px;
  color: #4e73df;
  margin-right: 15px;
  width: 40px;
  text-align: center;
}

.file-info {
  flex-grow: 1;
}

.file-name {
  font-weight: 600;
  margin-bottom: 3px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #6c757d;
}

/* ✅ Submit & Cancel Buttons */
.btn {
  font-size: 16px;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-lg {
  font-size: 18px;
  padding: 12px 24px;
}

.btn-primary {
  background-color: #4e73df;
  border-color: #4e73df;
}

.btn-success {
  background-color: #1cc88a;
  border-color: #1cc88a;
}

/* ✅ Button Hover Effects */
.btn-primary:hover {
  background-color: #2e59d9;
  border-color: #2e59d9;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #5a6268;
}

.btn-success:hover {
  background-color: #17a673;
  border-color: #17a673;
}

/* ✅ Submission Section */
.submission-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

/* ✅ Responsive Adjustments */
@media (max-width: 768px) {
  .priority-options, .contact-options {
    flex-direction: column;
    gap: 10px;
  }

  .step-label {
    font-size: 12px;
  }
}

/* ✅ Success & Error Snackbars */
::ng-deep .success-snackbar {
  background-color: #1cc88a;
  color: white;
}

::ng-deep .error-snackbar {
  background-color: #e74a3b;
  color: white;
}

::ng-deep .warning-snackbar {
  background-color: #f6c23e;
  color: white;
}