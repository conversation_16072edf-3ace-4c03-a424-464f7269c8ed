.container {
    padding: 20px;
  }
  
  .card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    background-color: #fff;
    transition: transform 0.2s ease-in-out;
  }
  
  .card:hover {
    transform: translateY(-2px);
  }
  
  .card-body {
    padding: 16px;
  }
  
  .card-body h5 {
    font-weight: 600;
    color: #333;
  }
  
  .card-body p {
    color: #666;
    font-size: 14px;
  }
  
  .card-body strong {
    color: #444;
  }
  
  .btn {
    padding: 8px 16px;
    font-weight: 500;
    font-size: 14px;
  }
  
  .btn-success {
    background-color: #28a745;
    color: white;
    border: none;
  }
  
  .btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
  }
  
  .btn-success:hover,
  .btn-danger:hover {
    opacity: 0.9;
  }
  
  .text-center {
    text-align: center;
  }
  
  h2.text-center {
    margin-top: 24px;
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 600;
  }
  