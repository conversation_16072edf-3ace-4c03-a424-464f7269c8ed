.edit-ad-dialog {
  padding: 25px;
  max-height: 80vh;
  overflow-y: auto;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 0 15px rgba(52, 152, 219, 0.3);
  animation: dialogGlow 2s ease-in-out infinite alternate;
}

/* Isolated Dialog Container Styles */
::ng-deep .isolated-dialog .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  background-color: #f9f9fa !important;
  position: relative;
  z-index: 10001 !important;
}

/* Isolated Dialog Backdrop */
::ng-deep .isolated-backdrop {
  background-color: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(8px) !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 10000 !important;
  opacity: 1 !important;
}

/* Ensure edit-ad dialog appears in the foreground with nothing else visible */
::ng-deep .isolated-dialog {
  z-index: 10001 !important;
  isolation: isolate !important;
}

@keyframes dialogGlow {
  from {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 0 15px rgba(52, 152, 219, 0.3);
  }
  to {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15), 0 0 25px rgba(52, 152, 219, 0.5);
  }
}

/* Form sections */
.form-section {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.1rem;
}

.section-header i {
  color: #3498db;
}

/* Action buttons */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

/* Expiry toggle */
.expiry-toggle {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
  border: 1px solid #e0e0e0;
}

/* Dialog title */
.edit-ad-dialog h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 10px;
}

.edit-ad-dialog h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 3px;
}

/* Form groups */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #34495e;
  font-size: 0.95rem;
  display: block;
  transition: all 0.2s ease;
}

/* Form controls */
.form-control {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 15px;
  transition: all 0.3s ease;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

textarea.form-control {
  min-height: 120px;
  resize: vertical;
}

/* Image preview */
.image-preview {
  display: flex;
  justify-content: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  border: 2px dashed #e0e0e0;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.image-preview:hover {
  border-color: #3498db;
}

.image-preview img {
  max-width: 100%;
  max-height: 250px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* File input */
input[type="file"] {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  cursor: pointer;
}

input[type="file"]:hover {
  background-color: #eef2f7;
}

/* Toggle switch */
.form-check-input {
  width: 3em;
  height: 1.5em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid #e0e0e0;
  appearance: none;
  -webkit-print-color-adjust: exact;
  transition: all 0.3s ease-in-out;
}

.form-check-input:checked {
  background-color: #3498db;
  border-color: #3498db;
}

.form-check-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

.form-switch .form-check-input {
  width: 3em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.25s ease-in-out;
}

.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

/* Buttons */
.btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9, #2573a7);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background-color: #95a5a6;
  border: none;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Spinner */
.spinner-border {
  width: 1.2rem;
  height: 1.2rem;
  border-width: 0.2em;
}

/* Input groups */
.input-group {
  border-radius: 8px;
  overflow: hidden;
}

.input-group-text {
  background-color: #f5f5f5;
  border: 2px solid #e0e0e0;
  border-left: none;
  color: #7f8c8d;
  font-weight: 600;
}

/* Small text */
small.text-muted {
  color: #7f8c8d !important;
  font-size: 0.8rem;
  margin-top: 5px;
  display: block;
}

/* Alert */
.alert {
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Custom styling for the dialog */
:host ::ng-deep .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  position: relative;
  z-index: 10002 !important; /* Ensure content is above everything */
}

/* Custom scrollbar for the dialog */
.edit-ad-dialog::-webkit-scrollbar {
  width: 8px;
}

.edit-ad-dialog::-webkit-scrollbar-track {
  background: rgba(241, 241, 241, 0.5);
  border-radius: 10px;
  margin: 10px 0;
}

.edit-ad-dialog::-webkit-scrollbar-thumb {
  background: #3498db;
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.edit-ad-dialog::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
  border: 2px solid transparent;
  background-clip: padding-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .edit-ad-dialog {
    padding: 15px;
  }

  .btn-lg {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .edit-ad-dialog h2 {
    font-size: 1.5rem;
  }

  .form-group label {
    font-size: 0.9rem;
  }
}
