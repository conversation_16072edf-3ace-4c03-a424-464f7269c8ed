<app-navbar-back></app-navbar-back>

<div class="d-flex">
  <!-- Sidebar (Fixed on the Left) -->
  <div class="sidebar">
    <app-sidebar-back></app-sidebar-back>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <h2 class="text-center mb-4">
      <i class="fas fa-clipboard-check me-2"></i>Pending Ads Review
    </h2>

    <div class="table-container">
      <div class="table-responsive">
        <div class="ad-cards-container">
          <!-- Ad Card for each pending ad -->
          <div *ngFor="let ad of pendingAds" class="ad-card">
            <!-- Card Header with Image and Title -->
            <div class="ad-card-header">
              <div class="ad-image-container">
                <img *ngIf="ad.image" [src]="'data:image/jpeg;base64,' + ad.image" alt="Ad Image" class="ad-thumbnail">
                <div *ngIf="!ad.image" class="no-image">
                  <i class="fas fa-image"></i>
                </div>
                <div *ngIf="ad.images && ad.images.length > 1" class="image-count">
                  +{{ ad.images.length - 1 }}
                </div>
              </div>
              <div class="ad-title-container">
                <h3 class="ad-title">{{ ad.title }}</h3>
                <span class="badge-category" [attr.data-category]="ad.category">{{ ad.category }}</span>
              </div>
              <div class="ad-status">
                <span [ngClass]="getStatusClass(ad.status)">
                  {{ getStatusLabel(ad.status) }}
                </span>
              </div>
            </div>

            <!-- Card Body with Main Info -->
            <div class="ad-card-body">
              <div class="ad-info-row">
                <div class="ad-info-item">
                  <i class="fas fa-align-left info-icon"></i>
                  <span class="info-label">Description:</span>
                  <span class="info-value description-text" title="{{ ad.description }}">{{ ad.description }}</span>
                </div>
              </div>

              <div class="ad-info-row">
                <div class="ad-info-item">
                  <i class="fas fa-calendar-alt info-icon"></i>
                  <span class="info-label">Period:</span>
                  <span class="info-value">{{ ad.startDate | date:'mediumDate' }} - {{ ad.endDate | date:'mediumDate' }}</span>
                </div>

                <div class="ad-info-item">
                  <i class="fas fa-clock info-icon"></i>
                  <span class="info-label">Time:</span>
                  <span class="info-value">{{ ad.startTime || '08:00' }} - {{ ad.endTime || '18:00' }}</span>
                </div>
              </div>

              <div class="ad-info-row">
                <div class="ad-info-item">
                  <i class="fas fa-dollar-sign info-icon"></i>
                  <span class="info-label">Price:</span>
                  <span *ngIf="ad.price" class="price-tag">{{ ad.price }} TND</span>
                  <span *ngIf="!ad.price" class="price-na">N/A</span>
                </div>

                <div class="ad-info-item">
                  <i class="fas fa-map-marker-alt info-icon"></i>
                  <span class="info-label">Location:</span>
                  <span *ngIf="ad.locationName" class="info-value" title="{{ ad.locationName }}">{{ ad.locationName }}</span>
                  <span *ngIf="!ad.locationName" class="text-muted">Not specified</span>
                </div>

                <div class="ad-info-item">
                  <i class="fas fa-eye info-icon"></i>
                  <span class="info-label">Views:</span>
                  <span class="info-value">{{ ad.viewCount || 0 }}</span>
                </div>
              </div>

              <div class="ad-info-row" *ngIf="ad.tags && ad.tags.length > 0">
                <div class="ad-info-item">
                  <i class="fas fa-tags info-icon"></i>
                  <span class="info-label">Tags:</span>
                  <div class="tags-container">
                    <span *ngFor="let tag of ad.tags" class="tag-badge">{{ tag }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Card Footer with Actions -->
            <div class="ad-card-footer">
              <button class="btn btn-success btn-action" (click)="approve(ad.id!)">
                <i class="fas fa-check me-1"></i> Approve
              </button>
              <button class="btn btn-danger btn-action" (click)="reject(ad.id!)">
                <i class="fas fa-times me-1"></i> Reject
              </button>
              <button class="btn btn-info btn-action" (click)="viewDetails(ad)">
                <i class="fas fa-eye me-1"></i> Details
              </button>
            </div>
          </div>

          <!-- Empty state message -->
          <div *ngIf="pendingAds.length === 0" class="empty-state">
            <i class="fas fa-clipboard-list"></i>
            <h5>No pending ads</h5>
            <p>There are no ads waiting for review at the moment.</p>
          </div>
        </div>
      </div>


    </div>
  </div>
</div>
