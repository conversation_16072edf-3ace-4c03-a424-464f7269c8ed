<app-header-front></app-header-front>

<div class="py-3 bg-primary bg-pattern mb-4">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="text-center text-white">
                    <span class="heading-xxs letter-spacing-xl">
                        Your ride, your comfort, your choice with SpeedyGo!
                    </span>
        </div>
      </div>
    </div>
  </div>
</div>
  <h2 class="mb-3">My Deliveries</h2>
  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead class="table-dark">
      <tr>

        <th>Estimated Delivery Time</th>
        <th>Delivery Status</th>
        <th>Payment Status</th>
        <th>Status</th>
        <th class="text-center">Actions</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let delivery of deliveries">

        <td>{{ delivery.estimatedDeliveryTime | date:'short' }}</td>
        <td>{{ delivery.deliveryStatus}}</td>
        <td>{{ delivery.pamentStatus }}</td>
        <td>
              <span
                class="badge"
                [ngClass]="{
                  'bg-warning text-dark': delivery.status === 'PENDING',
                  'bg-success': delivery.status === 'APPROVED',
                  'bg-danger': delivery.status === 'REJECTED'
                }">{{delivery.status}}
              </span>
        </td>
        <td class="text-center">
          <!-- ✅ Approve Button (Visible only if leave is PENDING) -->
          <button *ngIf="delivery.status === 'PENDING'" class="btn btn-success btn-sm me-2" (click)="approveDelivery(delivery)">
            <i class="fas fa-check-circle"></i> Approve
          </button>

          <!-- ❌ Reject Button (Visible only if leave is PENDING) -->
          <button *ngIf="delivery.status === 'PENDING'" class="btn btn-danger btn-sm" (click)="rejectDelivery(delivery)">
            <i class="fas fa-times-circle"></i> Reject
          </button>



          <!-- Delete Button -->
          <button class="btn btn-sm btn-danger" (click)="deleteDelivery(delivery.idD)">
            <i class="bi bi-trash"></i> Delete
          </button>
        </td>
      </tr>
      </tbody>
    </table>
  </div>


<app-footer-front></app-footer-front>
