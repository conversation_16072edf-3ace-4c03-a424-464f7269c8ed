/* ✅ General Styles */
:root {
  --primary-color: #3a0ca3;
  --primary-light: rgba(58, 12, 163, 0.08);
  --primary-dark: #240479;
  --secondary-color: #f72585;
  --secondary-light: rgba(247, 37, 133, 0.1);
  --accent-color: #4cc9f0;
  --accent-light: rgba(76, 201, 240, 0.1);
  --tertiary-color: #7209b7;
  --tertiary-light: rgba(114, 9, 183, 0.1);
  --success-color: #4cc9a0;
  --warning-color: #ffbe0b;
  --danger-color: #f72585;
  --info-color: #4cc9f0;
  --dark-color: #212121;
  --light-color: #f8f9fa;
  --bg-gradient: linear-gradient(135deg, #3a0ca3 0%, #7209b7 100%);
  --card-gradient: linear-gradient(135deg, rgba(248, 249, 250, 0.5) 0%, rgba(255, 255, 255, 1) 100%);
  --text-color: #333333;
  --text-secondary: #6c757d;
  --border-color: #e3e6f0;
  --card-shadow: 0 0.25rem 0.75rem rgba(58, 12, 163, 0.08);
  --hover-shadow: 0 0.5rem 1rem rgba(58, 12, 163, 0.15);
  --transition: all 0.3s ease;

  /* Animation Variables */
  --animation-duration: 0.5s;
  --animation-timing: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ✅ Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding: 2.25rem 2rem;
  background: linear-gradient(135deg, #3a0ca3 0%, #7209b7 70%, #f72585 100%);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(58, 12, 163, 0.2);
  color: white;
  animation: fadeInDown var(--animation-duration) var(--animation-timing);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(30deg);
  pointer-events: none;
  animation: shimmer 8s infinite linear;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
  opacity: 0.8;
}

/* Add decorative elements */

/* Add decorative circles */
.page-header .header-content::before {
  content: '';
  position: absolute;
  top: -100px;
  left: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(247, 37, 133, 0.4) 0%, rgba(247, 37, 133, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 6s infinite alternate;
}

.page-header .header-content::after {
  content: '';
  position: absolute;
  bottom: -80px;
  right: 20%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(76, 201, 240, 0.4) 0%, rgba(76, 201, 240, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 8s infinite alternate-reverse;
}

@keyframes shimmer {
  0% {
    transform: rotate(30deg) translateX(-30%);
  }
  100% {
    transform: rotate(30deg) translateX(30%);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

.header-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.page-title {
  color: white;
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 0.75rem;
  letter-spacing: -0.5px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
  padding-bottom: 0.5rem;
  animation: titleFadeIn 1s ease-out forwards;
  transform-origin: left center;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
  border-radius: 2px;
  animation: lineExpand 1.2s ease-out forwards;
  animation-delay: 0.5s;
  transform-origin: left center;
  opacity: 0;
}

.page-title .badge {
  position: relative;
  top: -5px;
  margin-left: 12px;
  background-color: white;
  color: var(--primary-color);
  border: none;
  padding: 0.35rem 0.75rem;
  font-size: 1rem;
  font-weight: 700;
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
  animation: badgePop 0.6s var(--animation-timing) forwards;
  animation-delay: 0.8s;
  opacity: 0;
  transform: scale(0.8);
}

.page-title:hover .badge {
  background-color: var(--secondary-color);
  color: white;
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

@keyframes titleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes lineExpand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 60px;
    opacity: 1;
  }
}

@keyframes badgePop {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  70% {
    opacity: 1;
    transform: scale(1.15);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-bottom: 0;
  max-width: 80%;
  font-weight: 400;
  opacity: 0;
  animation: subtitleFadeIn 1s ease-out forwards;
  animation-delay: 0.3s;
  position: relative;
  padding-left: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.page-subtitle::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 70%;
  background-color: var(--secondary-color);
  border-radius: 2px;
}

@keyframes subtitleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.add-btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 2rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(247, 37, 133, 0.3);
  transition: var(--transition);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.add-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition);
  z-index: -1;
}

.add-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px rgba(247, 37, 133, 0.4);
}

.add-btn:hover::before {
  left: 100%;
  transition: 0.7s;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ✅ Filters Section */
.filters-section {
  background: white;
  border-radius: 1rem;
  padding: 1.75rem;
  margin-bottom: 2.5rem;
  box-shadow: 0 8px 25px rgba(58, 12, 163, 0.1);
  border: 1px solid rgba(58, 12, 163, 0.08);
  position: relative;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.1s;
  animation-fill-mode: both;
  overflow: hidden;
}

.filters-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--tertiary-color), var(--accent-color));
  opacity: 0.8;
}

.filters-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(58, 12, 163, 0.03) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

/* Add decorative corner accents */
.filters-section .search-box::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  width: 20px;
  height: 20px;
  border-top: 3px solid var(--primary-color);
  border-left: 3px solid var(--primary-color);
  border-top-left-radius: 5px;
  opacity: 0.5;
  z-index: 1;
}

.search-box {
  position: relative;
  margin-bottom: 2rem;
  animation: slideInRight 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.search-icon {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1.1rem;
  transition: var(--transition);
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 1rem 3rem;
  border-radius: 2rem;
  border: 2px solid var(--primary-light);
  font-size: 1rem;
  transition: var(--transition);
  background-color: white;
  box-shadow: 0 6px 15px rgba(58, 12, 163, 0.08);
  color: var(--text-color);
  font-weight: 500;
  letter-spacing: 0.3px;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 8px 20px rgba(58, 12, 163, 0.15);
  transform: translateY(-2px);
}

.search-input:focus + .search-icon {
  color: var(--secondary-color);
  transform: translateY(-50%) scale(1.2);
  animation: pulse 1s infinite alternate;
}

.search-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
  font-weight: 400;
  font-style: italic;
}

.clear-search-btn {
  position: absolute;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(247, 37, 133, 0.1);
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 0;
  font-size: 0.9rem;
  width: 1.8rem;
  height: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
  opacity: 0.8;
  box-shadow: 0 2px 5px rgba(247, 37, 133, 0.2);
}

.clear-search-btn:hover {
  color: white;
  background-color: var(--secondary-color);
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
  box-shadow: 0 4px 8px rgba(247, 37, 133, 0.3);
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: flex-end;
  animation: fadeInUp 0.8s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
  position: relative;
}

.filter-group {
  flex: 1;
  min-width: 220px;
  position: relative;
  transition: var(--transition);
}

.filter-group:hover {
  transform: translateY(-3px);
}

.filter-label {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: 0.5px;
  transition: var(--transition);
  position: relative;
  padding-left: 0.5rem;
}

.filter-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 70%;
  background-color: var(--primary-color);
  border-radius: 2px;
  opacity: 0.7;
  transition: var(--transition);
}

.filter-label i {
  margin-right: 0.5rem;
  font-size: 1rem;
  color: var(--primary-color);
  transition: var(--transition);
}

.filter-select {
  width: 100%;
  padding: 1rem 1.25rem;
  border-radius: 1rem;
  border: 2px solid var(--primary-light);
  background-color: white;
  font-size: 1rem;
  transition: var(--transition);
  color: var(--text-color);
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%233a0ca3' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: calc(100% - 1.25rem) center;
  padding-right: 3rem;
  box-shadow: 0 6px 15px rgba(58, 12, 163, 0.08);
  font-weight: 500;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 8px 20px rgba(58, 12, 163, 0.15);
  transform: translateY(-2px);
}

.filter-select:hover {
  border-color: var(--primary-color);
  box-shadow: 0 8px 20px rgba(58, 12, 163, 0.12);
}

.filter-group:hover .filter-label {
  color: var(--secondary-color);
}

.filter-group:hover .filter-label::before {
  background-color: var(--secondary-color);
  height: 90%;
}

.filter-group:hover .filter-label i {
  color: var(--secondary-color);
  transform: scale(1.1);
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.reset-btn {
  padding: 1rem 1.75rem;
  background-color: white;
  border: 2px solid var(--secondary-light);
  border-radius: 2rem;
  color: var(--secondary-color);
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 6px 15px rgba(247, 37, 133, 0.12);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.reset-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: var(--transition);
  z-index: 1;
}

.reset-btn:hover:not(:disabled)::before {
  left: 100%;
  transition: 0.7s;
}

.reset-btn:hover:not(:disabled) {
  background-color: var(--secondary-color);
  color: white;
  border-color: var(--secondary-color);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 20px rgba(247, 37, 133, 0.25);
}

.reset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: var(--border-color);
  color: var(--text-secondary);
  box-shadow: none;
}

.view-toggle {
  display: flex;
  gap: 0.25rem;
  background-color: var(--primary-light);
  padding: 0.35rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(58, 12, 163, 0.1);
  border: 1px solid rgba(58, 12, 163, 0.05);
  animation: fadeInUp 0.8s ease-out forwards;
  animation-delay: 0.5s;
  opacity: 0;
}

.view-btn {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1.1rem;
  border-radius: 0.75rem;
  position: relative;
  overflow: hidden;
}

.view-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(58, 12, 163, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.5s, height 0.5s;
  z-index: -1;
}

.view-btn:hover::after {
  width: 150%;
  height: 150%;
}

.view-btn:hover {
  color: var(--primary-dark);
}

.view-btn.active {
  background-color: white;
  color: var(--primary-color);
  box-shadow: 0 6px 12px rgba(58, 12, 163, 0.2);
  transform: scale(1.05);
}

.view-btn.active i {
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ✅ Active Filters */
.active-filters {
  margin-top: 2rem;
  padding-top: 1.75rem;
  border-top: 2px dashed rgba(58, 12, 163, 0.15);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1.25rem;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.6s;
  animation-fill-mode: both;
  position: relative;
}

.active-filters::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background-color: white;
  border: 2px dashed rgba(58, 12, 163, 0.15);
  border-radius: 50%;
  z-index: 1;
}

.active-filters-label {
  font-weight: 700;
  color: white;
  font-size: 0.95rem;
  background: linear-gradient(135deg, #f72585, #7209b7, #3a0ca3);
  background-size: 200% 200%;
  padding: 0.6rem 1.2rem;
  border-radius: 2rem;
  box-shadow: 0 6px 15px rgba(247, 37, 133, 0.3);
  position: relative;
  overflow: hidden;
  animation: gradientShift 5s ease infinite;
  border: 2px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  transform: translateZ(0);
}

.active-filters-label i {
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  animation: pulse 2s infinite alternate;
}

.active-filters-label::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: rgba(255, 255, 255, 0.15);
  transform: rotate(30deg);
  pointer-events: none;
  animation: shimmer 3s infinite linear;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.85rem;
  flex: 1;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  background-color: white;
  color: var(--text-color);
  padding: 0.6rem 1.2rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  font-weight: 600;
  border: none;
  transition: var(--transition);
  box-shadow: 0 6px 15px rgba(76, 201, 240, 0.15);
  animation: scaleIn 0.4s ease forwards;
  position: relative;
  overflow: hidden;
}

/* Different colors for different filter types */
.filter-tag:nth-child(1) {
  background: linear-gradient(to right, rgba(76, 201, 240, 0.1), rgba(255, 255, 255, 1));
  border-left: 4px solid #4cc9f0;
}

.filter-tag:nth-child(2) {
  background: linear-gradient(to right, rgba(114, 9, 183, 0.1), rgba(255, 255, 255, 1));
  border-left: 4px solid #7209b7;
}

.filter-tag:nth-child(3) {
  background: linear-gradient(to right, rgba(247, 37, 133, 0.1), rgba(255, 255, 255, 1));
  border-left: 4px solid #f72585;
}

.filter-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);
  pointer-events: none;
}

.filter-tag:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 20px rgba(76, 201, 240, 0.25);
}

.filter-tag:nth-child(1):hover {
  box-shadow: 0 8px 20px rgba(76, 201, 240, 0.3);
}

.filter-tag:nth-child(2):hover {
  box-shadow: 0 8px 20px rgba(114, 9, 183, 0.3);
}

.filter-tag:nth-child(3):hover {
  box-shadow: 0 8px 20px rgba(247, 37, 133, 0.3);
}

.filter-tag i {
  margin-right: 0.5rem;
  font-size: 1.1rem;
  transition: var(--transition);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.filter-tag:nth-child(1) i {
  color: #4cc9f0;
}

.filter-tag:nth-child(2) i {
  color: #7209b7;
}

.filter-tag:nth-child(3) i {
  color: #f72585;
}

.filter-tag:hover i {
  transform: scale(1.2) rotate(5deg);
}

.tag-remove {
  background: rgba(247, 37, 133, 0.1);
  border: none;
  color: var(--secondary-color);
  margin-left: 0.75rem;
  width: 1.8rem;
  height: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  padding: 0;
  box-shadow: 0 3px 8px rgba(247, 37, 133, 0.15);
  position: relative;
  overflow: hidden;
}

.filter-tag:nth-child(1) .tag-remove {
  background: rgba(76, 201, 240, 0.1);
  color: #4cc9f0;
  box-shadow: 0 3px 8px rgba(76, 201, 240, 0.15);
}

.filter-tag:nth-child(2) .tag-remove {
  background: rgba(114, 9, 183, 0.1);
  color: #7209b7;
  box-shadow: 0 3px 8px rgba(114, 9, 183, 0.15);
}

.filter-tag:nth-child(3) .tag-remove {
  background: rgba(247, 37, 133, 0.1);
  color: #f72585;
  box-shadow: 0 3px 8px rgba(247, 37, 133, 0.15);
}

.tag-remove::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tag-remove:hover::before {
  opacity: 1;
}

.tag-remove:hover {
  color: white;
  transform: rotate(90deg) scale(1.1);
}

.filter-tag:nth-child(1) .tag-remove:hover {
  background-color: #4cc9f0;
  box-shadow: 0 4px 10px rgba(76, 201, 240, 0.3);
}

.filter-tag:nth-child(2) .tag-remove:hover {
  background-color: #7209b7;
  box-shadow: 0 4px 10px rgba(114, 9, 183, 0.3);
}

.filter-tag:nth-child(3) .tag-remove:hover {
  background-color: #f72585;
  box-shadow: 0 4px 10px rgba(247, 37, 133, 0.3);
}

.results-count {
  background: linear-gradient(135deg, #4cc9f0, #4361ee, #3a0ca3);
  background-size: 200% 200%;
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 2rem;
  font-size: 0.95rem;
  font-weight: 700;
  box-shadow: 0 6px 15px rgba(76, 201, 240, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  animation: gradientShift 5s ease infinite alternate;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  transform: translateZ(0);
}

.results-count i {
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  animation: bounce 2s infinite alternate;
}

.results-count::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

.results-count::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: rgba(255, 255, 255, 0.15);
  transform: rotate(30deg);
  pointer-events: none;
  animation: shimmer 3s infinite linear;
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  70% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(114, 9, 183, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(114, 9, 183, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(114, 9, 183, 0);
  }
}

/* ✅ Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loading-text {
  margin-top: 1rem;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* ✅ No Results */
.no-results {
  background-color: white;
  border-radius: 0.5rem;
  padding: 3rem 1rem;
  text-align: center;
  box-shadow: var(--card-shadow);
}

.no-results-content {
  max-width: 400px;
  margin: 0 auto;
}

.no-results-content i {
  color: var(--text-secondary);
  opacity: 0.5;
}

.no-results-content h4 {
  margin: 1rem 0 0.5rem;
  color: var(--text-color);
  font-weight: 600;
}

.no-results-content p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.no-results-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* ✅ Complaint Cards (Grid View) */
.complaints-grid {
  margin-bottom: 2rem;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.complaint-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  border: none;
  transform: translateY(0);
}

/* Elegant gradient border effect */
.complaint-card::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  border-radius: 12px;
  padding: 2px; /* Border width */
  background: linear-gradient(135deg, #3a0ca3, #7209b7, #f72585);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.complaint-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.complaint-card:hover::before {
  opacity: 1;
}

/* Status badge with improved styling */
.status-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 20px;
  font-size: 0.6rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  animation: fadeInRight 0.5s ease forwards;
  backdrop-filter: blur(5px);
}

.status-badge i {
  font-size: 0.65rem;
}

.status-pending {
  background-color: rgba(255, 190, 11, 0.9);
  color: #000;
  border: 1px solid rgba(255, 190, 11, 0.3);
}

.status-opened {
  background-color: rgba(76, 201, 240, 0.9);
  color: white;
  border: 1px solid rgba(76, 201, 240, 0.3);
}

.status-treated {
  background-color: rgba(76, 201, 160, 0.9);
  color: white;
  border: 1px solid rgba(76, 201, 160, 0.3);
}

/* Card content with improved styling */
.card-content {
  padding: 1.25rem;
  flex-grow: 1;
  position: relative;
  z-index: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Elegant title styling */
.complaint-title {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #2c3e50;
  padding-right: 4.5rem; /* Space for status badge */
  line-height: 1.2;
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.complaint-title::after {
  content: '';
  position: absolute;
  bottom: -0.3rem;
  left: 0;
  width: 2rem;
  height: 1.5px;
  background: linear-gradient(to right, #f72585, #7209b7);
  border-radius: 2px;
  transform: scaleX(0.3);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.complaint-card:hover .complaint-title::after {
  transform: scaleX(1);
}

/* Complaint meta information */
.complaint-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

/* Category badge with improved styling */
.complaint-category {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.65rem;
  color: #7209b7;
  background-color: rgba(114, 9, 183, 0.08);
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-weight: 600;
  border: 1px solid rgba(114, 9, 183, 0.1);
  box-shadow: 0 2px 5px rgba(114, 9, 183, 0.05);
  transition: all 0.3s ease;
}

.complaint-card:hover .complaint-category {
  transform: translateX(5px);
  background-color: rgba(114, 9, 183, 0.12);
}

.complaint-category i {
  color: #7209b7;
}

/* Date badge styling */
.complaint-date {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.65rem;
  color: #4cc9f0;
  background-color: rgba(76, 201, 240, 0.08);
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-weight: 600;
  border: 1px solid rgba(76, 201, 240, 0.1);
  box-shadow: 0 2px 5px rgba(76, 201, 240, 0.05);
  transition: all 0.3s ease;
}

.complaint-card:hover .complaint-date {
  transform: translateX(5px);
  background-color: rgba(76, 201, 240, 0.12);
}

.complaint-date i {
  color: #4cc9f0;
}

/* Description with improved styling */
.complaint-description {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0;
  line-height: 1.5;
  position: relative;
  padding: 0.5rem 0.75rem;
  border-left: 2px solid rgba(76, 201, 240, 0.3);
  background-color: rgba(76, 201, 240, 0.03);
  border-radius: 0 8px 8px 0;
  transition: all 0.3s ease;
}

.complaint-card:hover .complaint-description {
  border-left-color: rgba(76, 201, 240, 1);
  background-color: rgba(76, 201, 240, 0.05);
}

/* Card actions with improved styling */
.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: rgba(248, 249, 250, 0.9);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.complaint-card:hover .card-actions {
  background-color: rgba(58, 12, 163, 0.03);
}

/* Action buttons with improved styling */
.action-btn {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  font-size: 0.8rem;
  transform: translateY(0);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.view-btn {
  background: linear-gradient(135deg, #4cc9f0, #3ab7db);
  color: white;
}

.view-btn:hover {
  background: linear-gradient(135deg, #3ab7db, #2a9fc0);
}

.edit-btn {
  background: linear-gradient(135deg, #ffbe0b, #e6ac00);
  color: #000;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #e6ac00, #d19c09);
}

.delete-btn {
  background: linear-gradient(135deg, #f72585, #e01e79);
  color: white;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #e01e79, #c51268);
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ✅ Complaint List Items (List View) */
.complaints-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.complaint-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  border-radius: 12px;
  padding: 1.75rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  cursor: pointer;
  border: none;
  position: relative;
  overflow: hidden;
}

/* Elegant gradient border effect for list items */
.complaint-list-item::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  border-radius: 12px;
  padding: 2px; /* Border width */
  background: linear-gradient(135deg, #3a0ca3, #7209b7, #f72585);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.complaint-list-item:hover {
  transform: translateX(8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.complaint-list-item:hover::before {
  opacity: 1;
}

.list-item-content {
  flex: 1;
  padding-right: 2rem;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

/* Match the title styling from grid view */
.list-item-header .complaint-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin-bottom: 0;
}

.list-item-header .complaint-title::after {
  content: '';
  position: absolute;
  bottom: -0.3rem;
  left: 0;
  width: 2rem;
  height: 1.5px;
  background: linear-gradient(to right, #f72585, #7209b7);
  border-radius: 2px;
  transform: scaleX(0.3);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.complaint-list-item:hover .complaint-title::after {
  transform: scaleX(1);
}

.list-item-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 0.5rem;
}

/* Style the description in list view */
.list-item-body p {
  font-size: 0.95rem;
  color: #6c757d;
  line-height: 1.7;
  position: relative;
  padding: 0.75rem 1rem;
  border-left: 3px solid rgba(76, 201, 240, 0.3);
  background-color: rgba(76, 201, 240, 0.03);
  border-radius: 0 8px 8px 0;
  transition: all 0.3s ease;
  margin-bottom: 0;
}

.complaint-list-item:hover .list-item-body p {
  border-left-color: rgba(76, 201, 240, 1);
  background-color: rgba(76, 201, 240, 0.05);
}

/* Category badge in list view */
.list-item-body .complaint-category {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: #7209b7;
  background-color: rgba(114, 9, 183, 0.08);
  padding: 0.4rem 0.85rem;
  border-radius: 20px;
  font-weight: 600;
  border: 1px solid rgba(114, 9, 183, 0.1);
  box-shadow: 0 2px 5px rgba(114, 9, 183, 0.05);
  transition: all 0.3s ease;
  width: fit-content;
}

.complaint-list-item:hover .list-item-body .complaint-category {
  transform: translateX(5px);
  background-color: rgba(114, 9, 183, 0.12);
}

.list-item-actions {
  display: flex;
  gap: 0.75rem;
}

/* ✅ Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  animation: pulse 2s infinite;
}

.loading-container .spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--secondary-color);
  animation: spin 1.5s linear infinite;
}

.loading-text {
  margin-top: 1.5rem;
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: 600;
  background: linear-gradient(to right, var(--primary-color), var(--tertiary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 2s infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ✅ No Results */
.no-results {
  background-color: white;
  border-radius: 0.5rem;
  padding: 4rem 2rem;
  text-align: center;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  animation: fadeIn var(--animation-duration) var(--animation-timing);
}

.no-results::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--tertiary-color), var(--accent-color));
}

.no-results-content {
  max-width: 450px;
  margin: 0 auto;
}

.no-results-content i {
  color: var(--tertiary-color);
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: bounce 2s ease infinite;
}

.no-results-content h4 {
  margin: 1.25rem 0 0.75rem;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.5rem;
}

.no-results-content p {
  color: var(--text-color);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.no-results-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.no-results-actions .btn {
  padding: 0.85rem 1.5rem;
  font-weight: 600;
  border-radius: 2rem;
  transition: var(--transition);
}

.no-results-actions .btn-outline-primary {
  border: 2px solid var(--primary-light);
  color: var(--primary-color);
}

.no-results-actions .btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1rem rgba(58, 12, 163, 0.2);
}

.no-results-actions .btn-primary {
  background-color: var(--secondary-color);
  border: none;
  box-shadow: 0 0.25rem 0.75rem rgba(247, 37, 133, 0.2);
}

.no-results-actions .btn-primary:hover {
  background-color: #e01e79;
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1rem rgba(247, 37, 133, 0.3);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* ✅ Success & Error Snackbars */
::ng-deep .success-snackbar {
  background-color: var(--success-color);
  color: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(76, 201, 160, 0.3);
  animation: slideInUp 0.3s ease;
}

::ng-deep .error-snackbar {
  background-color: var(--danger-color);
  color: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(247, 37, 133, 0.3);
  animation: slideInUp 0.3s ease;
}

::ng-deep .info-snackbar {
  background-color: var(--info-color);
  color: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(76, 201, 240, 0.3);
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ✅ Responsive Adjustments */
@media (max-width: 992px) {
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    min-width: 100%;
  }

  .view-toggle {
    margin-top: 1rem;
    justify-content: center;
  }

  .reset-btn {
    margin-top: 1rem;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .page-subtitle {
    font-size: 0.95rem;
    max-width: 100%;
  }

  .add-btn {
    width: 100%;
  }

  .complaint-list-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .list-item-content {
    width: 100%;
    padding-right: 0;
  }

  .list-item-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .complaint-card {
    margin-bottom: 1rem;
  }

  .card-content {
    padding: 1.25rem;
  }
}