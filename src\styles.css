/* You can add global styles to this file, and also import other style files */

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Dialog styles */
.complaint-dialog-panel .mat-mdc-dialog-container {
  padding: 0;
  border-radius: 8px;
}

.complaint-dialog-panel .mdc-dialog__surface {
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Carousel styles */
.carousel {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-inner {
  border-radius: 8px;
}

.carousel-item img {
  object-fit: cover;
  width: 100%;
  border-radius: 8px;
}

.carousel-control-prev,
.carousel-control-next {
  width: 10%;
  opacity: 0.7;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0;
  transition: all 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.4);
}

.carousel-indicators {
  margin-bottom: 0.5rem;
}

.carousel-indicators button {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  margin: 0 3px;
}

.carousel-indicators button.active {
  background-color: white;
}

/* Map styles */
.map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.leaflet-container {
  border-radius: 8px;
  z-index: 1; /* Ensure map doesn't overlap other elements */
}

.leaflet-control-zoom {
  margin-top: 10px !important;
  margin-left: 10px !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

.leaflet-control-attribution {
  font-size: 10px !important;
}
