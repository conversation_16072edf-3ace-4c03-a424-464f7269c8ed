<app-navbar-back></app-navbar-back>

<app-sidebar-back></app-sidebar-back>

<div class="container mt-4">
  <div class="row">
    <div class="offset-md-2 col-md-10">
      <h2 class="mb-3 text-primary">🚚 Deliveries List</h2>

      <!-- The offset adds left space -->
      <div class="search-container">
        <label for="pamentStatus" class="search-label">Filtrer par statut de paiement:</label>

        <select id="pamentStatus" [(ngModel)]="searchPamentStatus" class="search-select">
          <option *ngFor="let status of pamentStatuses" [value]="status">{{ status }}</option>
        </select>

        <button (click)="search()" class="search-btn">
          🔍 Search
        </button>
      </div>


      <!-- Add Button -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <!-- Left Side: Add Assignment Button -->
        <a class="btn btn-success px-4 py-2" routerLink="/deliveries/assign">
          <i class="bi bi-plus-circle"></i> Add Assignment
        </a>

        <!-- Right Side: Dashboard Button -->
        <a class="btn btn-danger px-4 py-2" routerLink="/admin">
          <i class="bi bi-speedometer2"></i> Dashboard
        </a>
      </div>


      <!-- Deliveries Table -->
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
          <tr>
            <th>Delivery Status</th>
            <th>Estimated Delivery Time</th>
            <th>Payment Status</th>
            <th>Driver ID</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let delivery of deliveries">
            <td>{{ delivery.deliveryStatus }}</td>
            <td>{{ delivery.estimatedDeliveryTime }}</td>
            <td>
                <span class="badge text-white"
                      [ngClass]="{
                        'bg-success': delivery.pamentStatus === 'PAID',
                        'bg-danger': delivery.pamentStatus === 'UNPAID'
                      }">
                  {{ delivery.pamentStatus }}
                </span>
            </td>
            <td>{{ delivery.driverId || 'N/A' }}</td>
            <td>
              <span
                class="badge"
                [ngClass]="{
                  'bg-warning text-dark': delivery.status === 'PENDING',
                  'bg-success': delivery.status === 'APPROVED',
                  'bg-danger': delivery.status === 'REJECTED'
                }">{{delivery.status}}
              </span>
            </td>
            <td>
              <a class="btn btn-sm btn-info me-2" [routerLink]="['/deliveries/edit', delivery.idD]">
                <i class="bi bi-pencil"></i> Edit
              </a>
              <!-- Delete Button -->
              <button class="btn btn-sm btn-danger" (click)="deleteDelivery(delivery.idD)">
                <i class="bi bi-trash"></i> Delete
              </button>

            </td>
          </tr>
          </tbody>
        </table>

        <!-- Show message if no deliveries are found -->
        <div *ngIf="deliveries.length === 0" class="text-center p-3">
          <p class="text-muted">📭 No deliveries available.</p>
        </div>

      </div>
    </div>
  </div>
</div>
