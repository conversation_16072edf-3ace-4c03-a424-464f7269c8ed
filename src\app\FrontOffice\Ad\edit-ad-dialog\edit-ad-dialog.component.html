<div class="edit-ad-dialog">
  <h2 class="text-center">Modify Advertisement</h2>

  <form [formGroup]="adForm" (ngSubmit)="updateAd()">
    <div class="form-section">
      <div class="section-header">
        <i class="fas fa-info-circle me-2"></i>
        <span>Basic Information</span>
      </div>

      <!-- Ad Title -->
      <div class="form-group">
        <label for="title" class="form-label">
          <i class="fas fa-heading me-1"></i> Title
        </label>
        <input id="title" type="text" formControlName="title" class="form-control" required
               placeholder="Enter a descriptive title">
        <small class="text-danger" *ngIf="adForm.get('title')?.invalid && adForm.get('title')?.touched">
          Title is required (minimum 5 characters)
        </small>
      </div>

      <!-- Ad Description -->
      <div class="form-group">
        <label for="description" class="form-label">
          <i class="fas fa-align-left me-1"></i> Description
        </label>
        <textarea id="description" formControlName="description" class="form-control" rows="4" required
                  placeholder="Provide details about your advertisement"></textarea>
        <small class="text-danger" *ngIf="adForm.get('description')?.invalid && adForm.get('description')?.touched">
          Description is required (minimum 10 characters)
        </small>
      </div>

      <!-- Ad Category -->
      <div class="form-group">
        <label for="category" class="form-label">
          <i class="fas fa-tag me-1"></i> Category
        </label>
        <input id="category" type="text" formControlName="category" class="form-control"
               placeholder="e.g., CARPOOLING, FASTPOST, PRODUCT">
      </div>

      <!-- Price -->
      <div class="form-group">
        <label for="price" class="form-label">
          <i class="fas fa-money-bill-wave me-1"></i> Price
        </label>
        <div class="input-group">
          <input id="price" type="number" step="0.01" min="0" formControlName="price" class="form-control"
                 placeholder="Enter price">
          <span class="input-group-text">TND</span>
        </div>
        <small class="text-muted">Leave empty if not applicable</small>
      </div>
    </div>

    <div class="form-section">
      <div class="section-header">
        <i class="fas fa-image me-2"></i>
        <span>Image</span>
      </div>

      <!-- Ad Image Preview -->
      <div class="form-group">
        <label for="image" class="form-label">Current Image</label>
        <div class="image-preview mb-2" *ngIf="adForm.get('image')?.value">
          <img [src]="'data:image/jpeg;base64,' + adForm.get('image')?.value"
               class="img-fluid" alt="Ad image">
        </div>
        <div *ngIf="!adForm.get('image')?.value" class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i> No image available
        </div>
        <input type="hidden" id="image" formControlName="image">
      </div>

      <!-- New Image Upload -->
      <div class="form-group">
        <label for="newImage" class="form-label">
          <i class="fas fa-upload me-1"></i> Upload New Image
        </label>
        <input type="file" id="newImage" class="form-control" (change)="onFileSelected($event)" accept="image/*">
        <small class="text-muted">Leave empty to keep the current image</small>
      </div>
    </div>

    <div class="form-section">
      <div class="section-header">
        <i class="fas fa-calendar-alt me-2"></i>
        <span>Date and Time</span>
      </div>

      <!-- Date and Time Selection -->
      <div class="row">
        <!-- Start Date -->
        <div class="col-md-6">
          <div class="form-group">
            <label for="startDate" class="form-label">
              <i class="fas fa-calendar me-1"></i> Start Date
            </label>
            <input id="startDate" type="date" formControlName="startDate" class="form-control" required>
          </div>
        </div>

        <!-- Start Time -->
        <div class="col-md-6">
          <div class="form-group">
            <label for="startTime" class="form-label">
              <i class="fas fa-clock me-1"></i> Start Time
            </label>
            <input id="startTime" type="time" formControlName="startTime" class="form-control" required>
          </div>
        </div>
      </div>

      <div class="row">
        <!-- End Date -->
        <div class="col-md-6">
          <div class="form-group">
            <label for="endDate" class="form-label">
              <i class="fas fa-calendar-check me-1"></i> End Date
            </label>
            <input id="endDate" type="date" formControlName="endDate" class="form-control" required>
          </div>
        </div>

        <!-- End Time -->
        <div class="col-md-6">
          <div class="form-group">
            <label for="endTime" class="form-label">
              <i class="fas fa-hourglass-end me-1"></i> End Time
            </label>
            <input id="endTime" type="time" formControlName="endTime" class="form-control" required>
          </div>
        </div>
      </div>

      <!-- Toggle for using End Date as Expiry Date -->
      <div class="form-group expiry-toggle">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="useEndDateAsExpiry" formControlName="useEndDateAsExpiry">
          <label class="form-check-label" for="useEndDateAsExpiry">
            Use End Date as Expiration Date
            <small class="text-muted ms-2">(When the ad will no longer be visible)</small>
          </label>
        </div>
        <small class="form-text text-muted">When enabled, the ad will automatically expire on the end date.</small>
      </div>
    </div>

    <!-- Submit & Cancel Buttons -->
    <div class="action-buttons">
      <button type="button" class="btn btn-secondary" (click)="cancelEdit()" [disabled]="isSubmitting">
        <i class="fas fa-times me-1"></i> Cancel
      </button>
      <button type="submit" class="btn btn-primary" [disabled]="adForm.invalid || isSubmitting">
        <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        <i *ngIf="!isSubmitting" class="fas fa-save me-1"></i>
        {{ isSubmitting ? 'Updating...' : 'Update Ad' }}
      </button>
    </div>
  </form>
</div>
