import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { RouterModule } from '@angular/router';
import { NotificationService } from '../../services/notification.service';
import { ExtendedNotification } from '../../models/notification.model';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-notification-bell',
  standalone: true,
  imports: [
    CommonModule,
    MatBadgeModule,
    MatMenuModule,
    MatListModule,
    MatDividerModule,
    RouterModule
  ],
  templateUrl: './notification-bell.component.html',
  styleUrls: ['./notification-bell.component.css']
})
export class NotificationBellComponent implements OnInit, OnDestroy {
  unreadCount: number = 0;
  notifications: ExtendedNotification[] = [];
  userId: string = '';
  private subscriptions: Subscription[] = [];

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService,
    private http: HttpClient
  ) {}

  async ngOnInit(): Promise<void> {
    console.log('NotificationBellComponent: Initializing...');

    // Subscribe to unread count
    this.subscriptions.push(
      this.notificationService.unreadCount$.subscribe(count => {
        console.log('NotificationBellComponent: Received unread count:', count);
        this.unreadCount = count;
      })
    );

    // Subscribe to notifications
    this.subscriptions.push(
      this.notificationService.notifications$.subscribe(notifications => {
        console.log('NotificationBellComponent: Received notifications:', notifications);
        this.notifications = notifications;
      })
    );

    // Get the current user ID and start polling for notifications
    try {
      this.userId = await this.getCurrentUserId();
      console.log('NotificationBellComponent: Got user ID:', this.userId);

      if (this.userId) {
        console.log('NotificationBellComponent: Starting polling for user:', this.userId);
        this.notificationService.startPolling(this.userId);
      } else {
        console.warn('NotificationBellComponent: No user ID available, cannot start polling');
      }
    } catch (error) {
      console.error('NotificationBellComponent: Error getting user ID:', error);
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());

    // Stop polling
    this.notificationService.stopPolling();
  }

  /**
   * Mark a notification as read
   * @param notification The notification to mark as read
   * @param event The click event
   */
  async markAsRead(notification: ExtendedNotification, event: Event): Promise<void> {
    event.stopPropagation();

    if (notification.id && notification.read === false) {
      const userId = await this.getCurrentUserId();
      if (userId) {
        this.notificationService.markAsRead(notification.id, userId).subscribe();
      }
    }
  }

  /**
   * Mark all notifications as read
   * @param event The click event
   */
  async markAllAsRead(event: Event): Promise<void> {
    event.stopPropagation();

    console.log('NotificationBellComponent: Marking all notifications as read');

    if (this.userId) {
      console.log('NotificationBellComponent: Using stored userId:', this.userId);
      // Use the service method to mark all as read for the user
      this.notificationService.markAllAsReadForUser(this.userId);
    } else {
      // Fallback to getting the user ID if it's not stored
      const userId = await this.getCurrentUserId();
      console.log('NotificationBellComponent: Got userId from getCurrentUserId:', userId);

      if (userId) {
        // Use the service method to mark all as read for the user
        this.notificationService.markAllAsReadForUser(userId);
      } else {
        console.error('NotificationBellComponent: Cannot mark all as read - userId is empty');
      }
    }
  }

  /**
   * Get the current user ID
   * @returns The current user ID or a default value for testing
   */
  private async getCurrentUserId(): Promise<string> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();
      console.log('getCurrentUserId: User is logged in:', isLoggedIn);

      if (isLoggedIn) {
        // Get the user info from Keycloak token
        const token = this.authService.getToken();
        if (token) {
          // Extract user ID from the token if possible
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            console.log('getCurrentUserId: Token data:', tokenData);

            // Try different possible user ID fields in the token
            if (tokenData) {
              // Option 1: sub field (standard JWT subject)
              if (tokenData.sub) {
                console.log('getCurrentUserId: Using sub as user ID:', tokenData.sub);
                return tokenData.sub;
              }

              // Option 2: email field
              if (tokenData.email) {
                console.log('getCurrentUserId: Using email from token as user ID:', tokenData.email);
                return tokenData.email;
              }

              // Option 3: preferred_username field
              if (tokenData.preferred_username) {
                console.log('getCurrentUserId: Using preferred_username as user ID:', tokenData.preferred_username);
                return tokenData.preferred_username;
              }
            }
          } catch (e) {
            console.warn('getCurrentUserId: Error parsing token:', e);
          }
        }

        // Try to get user profile
        const userProfile = await this.authService.getUserProfile();
        console.log('getCurrentUserId: User profile:', userProfile);

        if (userProfile) {
          // Try email first
          if (userProfile.email) {
            console.log('getCurrentUserId: Using email from profile as user ID:', userProfile.email);
            return userProfile.email;
          }

          // Try name as fallback
          if (userProfile.name) {
            console.log('getCurrentUserId: Using name as user ID:', userProfile.name);
            return userProfile.name;
          }
        }

        // If we couldn't get the user ID from Keycloak, use the default
        console.log('getCurrentUserId: Could not get user ID from Keycloak, using default');
        return 'test-user-id';
      } else {
        console.log('getCurrentUserId: User is not logged in');
        return '';
      }
    } catch (error) {
      // If there's an error or the auth service is not available, return a default value
      console.warn('getCurrentUserId: Could not get user ID from auth service, using default', error);
      return 'test-user-id';
    }
  }

  /**
   * Format the notification date
   * @param dateString The date string from the notification
   * @returns A formatted date string
   */
  formatDate(dateString: string | undefined): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Create a test notification for debugging purposes
   * @param event The click event
   */
  createTestNotification(event: Event): void {
    event.stopPropagation();

    if (!this.userId) {
      console.error('NotificationBellComponent: Cannot create test notification - userId is empty');
      return;
    }

    console.log('NotificationBellComponent: Creating test notification for user:', this.userId);

    // Call the test notification endpoint
    this.http.post(`${this.notificationService.getApiUrl()}/testNotification/createTestNotification`, {
      userId: this.userId
    }).subscribe({
      next: (response) => {
        console.log('NotificationBellComponent: Test notification created:', response);
        // Refresh notifications
        this.notificationService.loadNotifications(this.userId);
        this.notificationService.loadUnreadCount(this.userId);
      },
      error: (error) => {
        console.error('NotificationBellComponent: Error creating test notification:', error);

        // Try the fallback URL
        this.http.post(`${this.notificationService.getFallbackApiUrl()}/testNotification/createTestNotification`, {
          userId: this.userId
        }).subscribe({
          next: (response) => {
            console.log('NotificationBellComponent: Test notification created using fallback URL:', response);
            // Refresh notifications
            this.notificationService.loadNotifications(this.userId);
            this.notificationService.loadUnreadCount(this.userId);
          },
          error: (fallbackError) => {
            console.error('NotificationBellComponent: Error creating test notification using fallback URL:', fallbackError);
          }
        });
      }
    });
  }
}
