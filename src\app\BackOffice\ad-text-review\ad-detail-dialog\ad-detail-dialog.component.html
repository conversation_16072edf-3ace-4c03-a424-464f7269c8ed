<div class="ad-detail-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 class="dialog-title">
      <i class="fas fa-ad"></i> {{ data.ad.title }}
    </h2>
    <div class="header-status">
      <span [ngClass]="{
        'badge bg-success': data.ad.status === 'APPROVED',
        'badge bg-warning': data.ad.status === 'PENDING_ADMIN_REVIEW',
        'badge bg-danger': data.ad.status === 'REJECTED',
        'badge bg-secondary': !data.ad.status
      }">
        {{ data.ad.status || 'Unknown' }}
      </span>
    </div>
    <button class="close-button" (click)="close()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <div class="tab-item active">
        <i class="fas fa-info-circle"></i> Details
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content-area">
      <!-- Two-column layout for Images and Description -->
      <div class="content-row">
        <div class="content-column">
          <h3 class="section-title">Images</h3>
          <div class="ad-images-container">
            <div *ngIf="data.ad.image" class="ad-image-main">
              <img [src]="getImageUrl(data.ad.image)" alt="Ad Image" class="main-image">
            </div>
            <div *ngIf="data.ad.images && data.ad.images.length > 0" class="ad-images-gallery">
              <div *ngFor="let img of data.ad.images" class="gallery-image-container">
                <img [src]="getImageUrl(img)" alt="Ad Image" class="gallery-image">
              </div>
            </div>
            <div *ngIf="!data.ad.image && (!data.ad.images || data.ad.images.length === 0)" class="no-images">
              <i class="fas fa-image"></i>
              <p>No images available</p>
            </div>
          </div>
        </div>

        <div class="content-column">
          <h3 class="section-title">Description</h3>
          <div class="description-content">
            {{ data.ad.description || 'No description provided' }}
          </div>
        </div>
      </div>

      <!-- Basic Information Section -->
      <div class="info-section">
        <h3 class="section-title">Basic Information</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label"><i class="fas fa-tag"></i> Category</div>
            <div class="info-value">
              <span class="badge-category" [attr.data-category]="data.ad.category">{{ data.ad.category }}</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label"><i class="fas fa-dollar-sign"></i> Price</div>
            <div class="info-value">
              <span *ngIf="data.ad.price" class="price-tag">{{ data.ad.price }} TND</span>
              <span *ngIf="!data.ad.price" class="price-na">N/A</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label"><i class="fas fa-eye"></i> Views</div>
            <div class="info-value">{{ data.ad.viewCount || 0 }}</div>
          </div>
          <div class="info-item">
            <div class="info-label"><i class="fas fa-calendar-alt"></i> Period</div>
            <div class="info-value">{{ formatDateTime(data.ad.startDate) }} - {{ formatDateTime(data.ad.endDate) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label"><i class="fas fa-clock"></i> Time</div>
            <div class="info-value">{{ data.ad.startTime || '08:00' }} - {{ data.ad.endTime || '18:00' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label"><i class="fas fa-map-marker-alt"></i> Location</div>
            <div class="info-value">{{ data.ad.locationName || 'Not specified' }}</div>
          </div>
        </div>
      </div>

      <!-- Tags Section -->
      <div *ngIf="data.ad.tags && data.ad.tags.length > 0" class="tags-section">
        <h3 class="section-title">Tags</h3>
        <div class="tags-container">
          <span *ngFor="let tag of data.ad.tags" class="tag-badge">{{ tag }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Rejection Reason Form -->
  <div *ngIf="data.ad.status === 'PENDING_ADMIN_REVIEW'" class="rejection-form">
    <div class="form-container">
      <h3 class="section-title">Rejection Reason</h3>
      <div class="form-field">
        <label for="rejectionReason">Please provide a reason for rejection:</label>
        <textarea
          id="rejectionReason"
          [(ngModel)]="rejectionReason"
          placeholder="Enter reason for rejection (will be shown to the user)..."
          rows="3"
          class="rejection-textarea"></textarea>
        <small class="form-hint">This reason will be visible to the user who posted the ad.</small>
      </div>
    </div>
  </div>

  <!-- Dialog Footer -->
  <div class="dialog-footer">
    <div class="footer-actions" *ngIf="data.ad.status === 'PENDING_ADMIN_REVIEW'">
      <button class="btn btn-success" [disabled]="isProcessing" (click)="approve()">
        <i class="fas fa-check me-1"></i>
        <span *ngIf="!isProcessing">Approve</span>
        <span *ngIf="isProcessing">Processing...</span>
      </button>
      <button class="btn btn-danger" [disabled]="isProcessing" (click)="reject()">
        <i class="fas fa-times me-1"></i>
        <span *ngIf="!isProcessing">Reject</span>
        <span *ngIf="isProcessing">Processing...</span>
      </button>
    </div>
    <button class="btn btn-primary" [disabled]="isProcessing" (click)="close()">
      <i class="fas fa-times-circle me-1"></i> Close
    </button>
  </div>
</div>
