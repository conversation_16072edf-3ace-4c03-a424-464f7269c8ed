{"name": "frontspeedygo", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.6", "@angular/cdk": "^19.2.9", "@angular/common": "^19.1.6", "@angular/compiler": "^19.1.6", "@angular/core": "^19.1.6", "@angular/forms": "^19.1.6", "@angular/material": "^19.2.9", "@angular/platform-browser": "^19.1.6", "@angular/platform-browser-dynamic": "^19.1.6", "@angular/router": "^19.1.6", "@iconify/iconify": "^3.1.1", "@types/leaflet": "^1.9.17", "bootstrap": "^5.3.3", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "jwt-decode": "^4.0.0", "keycloak-angular": "^19.0.2", "keycloak-js": "^26.2.0", "leaflet": "^1.9.4", "rxjs": "~7.8.0", "tslib": "^2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.7", "@angular/cli": "^19.1.7", "@angular/compiler-cli": "^19.1.6", "@types/jasmine": "~4.3.0", "@types/jwt-decode": "^3.1.0", "@types/keycloak-js": "^3.4.1", "@types/node": "^22.13.4", "jasmine": "^5.6.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.3"}}