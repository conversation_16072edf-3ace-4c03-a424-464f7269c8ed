/* Dialog Container */
.complaint-dialog {
  max-width: 450px;
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  z-index: 9999 !important;
  animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Ensure complaint dialog appears in the foreground */
:host ::ng-deep .complaint-dialog-panel {
  z-index: 9999 !important;
}

:host ::ng-deep .complaint-dialog-panel .mat-mdc-dialog-container {
  border-radius: 16px !important;
  overflow: hidden !important;
}

:host ::ng-deep .complaint-dialog-panel .mat-mdc-dialog-surface {
  border-radius: 16px !important;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa) !important;
  overflow: hidden !important;
}

:host ::ng-deep .stylish-backdrop {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px) !important;
  transition: all 0.3s ease !important;
}

/* Dialog Header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  color: white;
  box-shadow: 0 2px 10px rgba(255, 82, 82, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.header-icon:hover {
  transform: rotate(15deg);
  background-color: rgba(255, 255, 255, 0.3);
}

.header-icon mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.header-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.close-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Progress Bar */
.progress-container {
  height: 4px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #ff5252, #ff9800, #ff5252);
  background-size: 200% 100%;
  animation: progress-animation 1.5s infinite linear;
  box-shadow: 0 1px 3px rgba(255, 82, 82, 0.3);
}

@keyframes progress-animation {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: 0 0;
  }
}

/* Form Content */
mat-dialog-content {
  padding: 0 !important;
  max-height: 400px !important;
  overflow-x: hidden;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

.full-width {
  width: 100%;
}

/* Tabs */
.tabs {
  display: flex;
  border-radius: 24px;
  background-color: #f0f0f0;
  padding: 5px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab:hover::before {
  opacity: 1;
}

.tab mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  color: #757575;
  transition: all 0.3s ease;
  margin-bottom: 4px;
}

.tab span {
  font-size: 12px;
  color: #757575;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.tab.active {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  box-shadow: 0 3px 8px rgba(255, 82, 82, 0.3);
  transform: translateY(-2px);
}

.tab.active mat-icon,
.tab.active span {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.tab-content {
  animation: fadeInUp 0.4s ease;
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ad Title Compact */
.ad-title-compact {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(to right, rgba(255, 82, 82, 0.08), rgba(255, 82, 82, 0.02));
  padding: 12px 16px;
  border-radius: 10px;
  margin-bottom: 20px;
  border-left: 3px solid #ff5252;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.ad-title-compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.ad-title-compact mat-icon {
  color: #ff5252;
  font-size: 20px;
  height: 20px;
  width: 20px;
  filter: drop-shadow(0 1px 2px rgba(255, 82, 82, 0.2));
}

.title-text {
  font-size: 15px;
  color: #333;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.2px;
}

/* Section Label Compact */
.section-label-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #d32f2f;
  font-weight: 600;
  font-size: 15px;
  position: relative;
  padding-bottom: 8px;
}

.section-label-compact::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, #ff5252, transparent);
  border-radius: 2px;
}

.section-label-compact mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  color: #ff5252;
}

/* Complaint Types Compact */
.complaint-types-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.complaint-type-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  border-radius: 24px;
  background-color: #f8f8f8;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.complaint-type-compact:hover {
  background-color: #f0f0f0;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
}

.complaint-type-compact.selected {
  background: linear-gradient(to right, #ffebee, #fff5f5);
  border-color: #ff5252;
  box-shadow: 0 4px 10px rgba(255, 82, 82, 0.15);
}

.complaint-type-compact mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  color: #666;
  transition: all 0.3s ease;
}

.complaint-type-compact.selected mat-icon {
  color: #ff5252;
  transform: scale(1.1);
}

.complaint-type-compact span {
  font-size: 13px;
  color: #555;
  font-weight: 500;
  transition: all 0.3s ease;
}

.complaint-type-compact.selected span {
  color: #d32f2f;
  font-weight: 600;
  letter-spacing: 0.3px;
}

/* Description Section */
.description-section mat-form-field {
  margin-top: 8px;
  margin-bottom: 20px;
}

.warning {
  color: #ff9800 !important;
  font-weight: 500 !important;
}

/* Urgency Buttons */
.urgency-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.urgency-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  border-radius: 12px;
  background-color: #f8f8f8;
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.urgency-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.urgency-button:hover::before {
  opacity: 1;
}

.urgency-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.urgency-button mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  color: #666;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

.urgency-button span {
  font-size: 13px;
  color: #555;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
}

.urgency-button.selected[value="LOW"] {
  background: linear-gradient(to bottom, #e8f5e9, #f1f8f1);
  border-color: #4caf50;
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.15);
}

.urgency-button.selected[value="MEDIUM"] {
  background: linear-gradient(to bottom, #fff8e1, #fffbf2);
  border-color: #ff9800;
  box-shadow: 0 4px 10px rgba(255, 152, 0, 0.15);
}

.urgency-button.selected[value="HIGH"] {
  background: linear-gradient(to bottom, #ffebee, #fff5f5);
  border-color: #ff5252;
  box-shadow: 0 4px 10px rgba(255, 82, 82, 0.15);
}

.urgency-button.selected[value="LOW"] mat-icon,
.urgency-button.selected[value="LOW"] span {
  color: #2e7d32;
}

.urgency-button.selected[value="MEDIUM"] mat-icon,
.urgency-button.selected[value="MEDIUM"] span {
  color: #ef6c00;
}

.urgency-button.selected[value="HIGH"] mat-icon,
.urgency-button.selected[value="HIGH"] span {
  color: #d32f2f;
}

.urgency-button.selected {
  font-weight: 600;
  transform: translateY(-3px);
}

/* Evidence Upload Section */
.upload-container-compact {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
  margin-bottom: 12px;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.03);
}

.upload-container-compact:hover {
  border-color: #ff5252;
  background-color: rgba(255, 82, 82, 0.05);
  transform: translateY(-2px);
  box-shadow: inset 0 2px 5px rgba(255, 82, 82, 0.05), 0 4px 10px rgba(0, 0, 0, 0.05);
}

.upload-container-compact.has-file {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
  box-shadow: inset 0 2px 5px rgba(76, 175, 80, 0.05), 0 4px 10px rgba(0, 0, 0, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-content mat-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
  color: #757575;
  transition: all 0.3s ease;
}

.upload-container-compact:hover .upload-content mat-icon {
  color: #ff5252;
  transform: scale(1.1);
}

.upload-container-compact.has-file .upload-content mat-icon {
  color: #4caf50;
  transform: scale(1.1);
}

.upload-content span {
  font-size: 14px;
  font-weight: 500;
  color: #555;
  transition: all 0.3s ease;
}

.upload-container-compact:hover .upload-content span {
  color: #d32f2f;
}

.upload-container-compact.has-file .upload-content span {
  color: #2e7d32;
}

.remove-file-btn {
  margin-top: 10px;
  background: none;
  border: 1px solid rgba(255, 82, 82, 0.2);
  color: #ff5252;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(255, 82, 82, 0.1);
}

.remove-file-btn:hover {
  background-color: rgba(255, 82, 82, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 82, 82, 0.15);
}

/* Contact Section */
.contact-fields-compact {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-fields-compact mat-form-field {
  animation: fadeInUp 0.5s ease;
  animation-fill-mode: both;
}

.contact-fields-compact mat-form-field:nth-child(2) {
  animation-delay: 0.1s;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: linear-gradient(to right, rgba(255, 82, 82, 0.1), rgba(255, 82, 82, 0.05));
  border-radius: 10px;
  color: #d32f2f;
  font-size: 13px;
  font-weight: 500;
  border-left: 3px solid #ff5252;
  box-shadow: 0 2px 8px rgba(255, 82, 82, 0.1);
  animation: shake 0.5s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.error-message mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  color: #ff5252;
}

/* Dialog Actions */
mat-dialog-actions {
  display: flex;
  justify-content: space-between;
  padding: 16px 24px;
  background: linear-gradient(to bottom, #f8f9fa, #f0f0f0);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin: 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
}

.cancel-button {
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.cancel-button:hover:not([disabled]) {
  background-color: #f5f5f5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.submit-button {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 10px rgba(255, 82, 82, 0.2);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  border: none;
}

.submit-button:hover:not([disabled]) {
  background: linear-gradient(135deg, #ff3d3d, #c62828);
  box-shadow: 0 6px 15px rgba(255, 82, 82, 0.3);
  transform: translateY(-3px);
}

.submit-button[disabled] {
  background: linear-gradient(135deg, #e0e0e0, #d5d5d5);
  color: #9e9e9e;
  box-shadow: none;
}

.submit-button mat-icon,
.cancel-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Form Field Styling */
::ng-deep .mat-mdc-form-field-appearance-outline .mat-mdc-form-field-infix {
  padding: 10px 0;
  min-height: auto;
}

::ng-deep .mat-mdc-form-field-hint {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
  transition: all 0.3s ease;
}

::ng-deep .mat-mdc-form-field-error {
  font-size: 11px;
  margin-top: 2px;
  letter-spacing: 0.2px;
}

::ng-deep .mat-mdc-form-field-appearance-outline .mat-mdc-form-field-outline {
  color: rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline {
  color: #ff5252;
}

::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label {
  color: #d32f2f;
  font-weight: 500;
}

::ng-deep .mat-mdc-form-field-appearance-outline.mat-focused .mat-mdc-form-field-outline-thick {
  color: #ff5252;
}

::ng-deep .mat-mdc-form-field:hover .mat-mdc-form-field-outline {
  color: rgba(255, 82, 82, 0.3);
  opacity: 1;
}

::ng-deep .mat-mdc-form-field .mat-mdc-form-field-label {
  transition: all 0.3s ease;
}

::ng-deep .mat-mdc-form-field:hover .mat-mdc-form-field-label {
  color: #d32f2f;
}

::ng-deep .mat-mdc-text-field-wrapper {
  background-color: white !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03) !important;
}

::ng-deep .mat-mdc-form-field:hover .mat-mdc-text-field-wrapper {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-2px) !important;
}

/* Make textarea smaller */
::ng-deep .mat-mdc-form-field textarea.mat-mdc-input-element {
  padding: 2px 0;
  margin: 0;
  line-height: 1.4;
  font-size: 14px;
}

::ng-deep .mat-mdc-form-field input.mat-mdc-input-element {
  font-size: 14px;
}

/* Responsive Adjustments */
@media (max-width: 400px) {
  .complaint-dialog {
    max-width: 100%;
  }

  .urgency-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .tab span {
    display: none;
  }

  .complaint-type-compact {
    flex: 1 0 45%;
  }
}
