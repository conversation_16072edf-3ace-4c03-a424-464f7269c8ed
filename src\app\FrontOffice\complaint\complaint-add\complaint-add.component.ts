import { CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { Complaint, ComplaintControllerService } from 'src/app/openapi';
import { FooterFrontComponent } from '../../footer-front/footer-front.component';
import { HeaderFrontComponent } from '../../header-front/header-front.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthService } from 'src/app/services/auth.service';

interface FilePreview {
  name: string;
  size: string;
  type: string;
}

@Component({
  selector: 'app-complaint-add',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FooterFrontComponent,
    HeaderFrontComponent,
    RouterLink,
    MatDialogModule,
    MatSnackBarModule,
    MatIconModule,
    MatProgressBarModule,
    MatTooltipModule
  ],
  templateUrl: './complaint-add.component.html',
  styleUrl: './complaint-add.component.css'
})
export class ComplaintAddComponent implements OnInit {
  complaintForm!: FormGroup;
  isSubmitting = false;
  currentStep = 1;
  totalSteps = 3;
  selectedFiles: File[] = [];
  filePreviews: FilePreview[] = [];

  // Categories from the backend enum
  categories = [
    { value: 'RETARD_LIVRAISON', label: 'Delivery Delay' },
    { value: 'PRODUIT_ENDOMMAGE', label: 'Damaged Product' },
    { value: 'MAUVAIS_SERVICE_CLIENT', label: 'Poor Customer Service' },
    { value: 'ERREUR_FACTURATION', label: 'Billing Error' },
    { value: 'ARTICLE_MANQUANT', label: 'Missing Item' },
    { value: 'LIVRAISON_MAUVAISE_ADRESSE', label: 'Wrong Delivery Address' },
    { value: 'COMMANDE_INCOMPLETE', label: 'Incomplete Order' },
    { value: 'MAUVAISE_QUALITE', label: 'Poor Quality' },
    { value: 'COMPORTEMENT_PERSONNEL', label: 'Staff Behavior' },
    { value: 'PROBLEME_TECHNIQUE', label: 'Technical Issue' },
    { value: 'PROBLEME_REMBOURSEMENT', label: 'Refund Problem' },
    { value: 'FRAIS_INATTENDUS', label: 'Unexpected Fees' },
    { value: 'PUBLICITE_MENSONGERE', label: 'Misleading Advertisement' },
    { value: 'SECURITE_HARCELEMENT', label: 'Security/Harassment' },
    { value: 'AUTRE', label: 'Other' }
  ];

  // Priority options
  priorities = [
    { value: 'LOW', label: 'Low - Not urgent' },
    { value: 'MEDIUM', label: 'Medium - Needs attention' },
    { value: 'HIGH', label: 'High - Urgent issue' }
  ];

  // Contact preference options
  contactPreferences = [
    { value: 'EMAIL', label: 'Email' },
    { value: 'PHONE', label: 'Phone' },
    { value: 'EITHER', label: 'Either' }
  ];

  @ViewChild('fileInput') fileInput!: ElementRef;

  // Store the current user ID
  private currentUserId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private complaintService: ComplaintControllerService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private authService: AuthService
  ) {}

  async ngOnInit(): Promise<void> {
    // Get the current user ID
    this.currentUserId = await this.getCurrentUserId();
    console.log("Current user ID:", this.currentUserId);

    this.complaintForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]],
      category: ['AUTRE', Validators.required],
      priority: ['MEDIUM', Validators.required],
      contactPreference: ['EMAIL', Validators.required],
      contactEmail: ['', [Validators.email]],
      contactPhone: [''],
      status: ['PENDING', Validators.required],
      userId: [this.currentUserId] // Set the user ID in the form
    });

    // Add conditional validation for contact fields
    this.complaintForm.get('contactPreference')?.valueChanges.subscribe(preference => {
      const emailControl = this.complaintForm.get('contactEmail');
      const phoneControl = this.complaintForm.get('contactPhone');

      if (preference === 'EMAIL' || preference === 'EITHER') {
        emailControl?.setValidators([Validators.required, Validators.email]);
      } else {
        emailControl?.clearValidators();
        emailControl?.setValidators([Validators.email]);
      }

      if (preference === 'PHONE' || preference === 'EITHER') {
        phoneControl?.setValidators([Validators.required]);
      } else {
        phoneControl?.clearValidators();
      }

      emailControl?.updateValueAndValidity();
      phoneControl?.updateValueAndValidity();
    });
  }

  // Navigation between form steps
  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  prevStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  // File handling methods
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Limit to 3 files
      const newFiles = Array.from(files).slice(0, 3 - this.selectedFiles.length) as File[];

      // Add new files to the array
      this.selectedFiles = [...this.selectedFiles, ...newFiles];

      // Create previews for the files
      this.filePreviews = this.selectedFiles.map(file => ({
        name: file.name,
        size: this.formatFileSize(file.size),
        type: file.type
      }));
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    this.filePreviews.splice(index, 1);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  openFileSelector(): void {
    this.fileInput.nativeElement.click();
  }

  // Form submission
  onSubmit(): void {
    if (this.complaintForm.valid) {
      this.isSubmitting = true;

      // Process file uploads
      const processedFiles: string[] = [];

      // Convert files to base64 strings
      const filePromises = this.selectedFiles.map(file => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            const base64String = e.target?.result as string;
            resolve(base64String);
          };
          reader.readAsDataURL(file);
        });
      });

      // Wait for all files to be processed
      Promise.all(filePromises).then(base64Files => {
        // Create the complaint object with evidence files
        const formValues = this.complaintForm.value;
        console.log('Form values before submission:', formValues);

        // Ensure user ID is set and log it for debugging
        console.log('Current user ID before submission:', this.currentUserId);

        const newComplaint: Complaint = {
          ...formValues,
          evidence: base64Files,
          userId: this.currentUserId || 'anonymous-user' // Ensure user ID is set
        };

        console.log('Complaint to be submitted:', newComplaint);
        console.log('User ID in complaint:', newComplaint.userId);

        // Submit the complaint with evidence
        this.complaintService.createComplaint(newComplaint).subscribe({
          next: (response) => {
            this.isSubmitting = false;
            console.log('Complaint submitted successfully:', response);
            this.snackBar.open('Complaint submitted successfully!', 'Close', {
              duration: 5000,
              panelClass: ['success-snackbar']
            });

            // Wait a moment before redirecting to ensure the backend has time to process
            setTimeout(() => {
              this.router.navigate(['/complaint']); // Redirect to complaint list
            }, 1000);
          },
          error: (err) => {
            this.isSubmitting = false;
            console.error('Error submitting complaint', err);

            // More detailed error message
            let errorMessage = 'Failed to submit complaint. ';

            if (err.status === 401 || err.status === 403) {
              errorMessage += 'Authentication error. Please log in again.';
            } else if (err.status === 400) {
              errorMessage += 'Invalid complaint data.';
            } else if (err.status === 500) {
              errorMessage += 'Server error. Please try again later.';
            } else {
              errorMessage += 'Please try again.';
            }

            this.snackBar.open(errorMessage, 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
          }
        });
      });
    } else {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.complaintForm.controls).forEach(key => {
        this.complaintForm.get(key)?.markAsTouched();
      });

      this.snackBar.open('Please fix the errors in the form before submitting.', 'Close', {
        duration: 5000,
        panelClass: ['warning-snackbar']
      });
    }
  }

  // Helper method to check if the current step is valid
  isStepValid(step: number): boolean {
    if (step === 1) {
      return !!this.complaintForm.get('title')?.valid &&
             !!this.complaintForm.get('description')?.valid &&
             !!this.complaintForm.get('category')?.valid;
    } else if (step === 2) {
      return !!this.complaintForm.get('priority')?.valid;
    } else if (step === 3) {
      return !!this.complaintForm.get('contactPreference')?.valid &&
             (!!this.complaintForm.get('contactEmail')?.valid || !!this.complaintForm.get('contactPhone')?.valid);
    }
    return false;
  }

  // Get character count for textarea
  getCharacterCount(controlName: string): number {
    return this.complaintForm.get(controlName)?.value?.length || 0;
  }

  // Get maximum character count for a field
  getMaxCharacterCount(controlName: string): number {
    if (controlName === 'title') return 100;
    if (controlName === 'description') return 500;
    return 0;
  }

  /**
   * Get the current user ID from the auth service
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      // Check if the user is logged in
      const isLoggedIn = await this.authService.isLoggedIn();
      console.log('User is logged in:', isLoggedIn);

      if (isLoggedIn) {
        // Get the user info from Keycloak token
        const token = this.authService.getToken();
        if (token) {
          console.log('Token found, attempting to extract user ID');
          // Extract user ID from the token if possible
          try {
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            console.log('Token data:', tokenData);
            if (tokenData && tokenData.sub) {
              console.log('Found user ID in token:', tokenData.sub);
              return tokenData.sub;
            }
          } catch (e) {
            console.warn('Error parsing token:', e);
          }
        } else {
          console.warn('No token found');
        }

        // Try to get user profile
        console.log('Attempting to get user profile');
        const userProfile = await this.authService.getUserProfile();
        console.log('User profile:', userProfile);
        if (userProfile && userProfile.email) {
          console.log('Using email as user ID:', userProfile.email);
          return userProfile.email;
        }

        // If we couldn't get the user ID from Keycloak, use a default
        console.warn('Could not get user ID from token or profile, using default');
        return 'test-user-id';
      } else {
        console.warn('User is not logged in');
        return null;
      }
    } catch (error) {
      console.warn('Could not get user ID from auth service, using default', error);
      return 'test-user-id';
    }
  }
}
