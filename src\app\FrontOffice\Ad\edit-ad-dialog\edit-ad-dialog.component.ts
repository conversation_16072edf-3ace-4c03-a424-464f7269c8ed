import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { Ad, AdControllerService } from 'src/app/openapi';
import { CustomAdService } from 'src/app/services/custom-ad.service';

@Component({
  selector: 'app-edit-ad-dialog',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatDialogModule],
  templateUrl: './edit-ad-dialog.component.html',
  styleUrls: ['./edit-ad-dialog.component.css']
})
export class EditAdDialogComponent implements OnInit {
  adForm!: FormGroup;
  isSubmitting = false;
  originalAd: Ad | null = null;
  selectedFile: File | null = null;

  constructor(
    private fb: FormBuilder,
    private adService: AdControllerService,
    private customAdService: CustomAdService,
    public dialogRef: MatDialogRef<EditAdDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { adId: string }
  ) {}

  ngOnInit(): void {
    this.adForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(5)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      image: [''],
      category: [''],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      startTime: ['08:00', Validators.required],
      endTime: ['18:00', Validators.required],
      useEndDateAsExpiry: [true],
      price: ['', [Validators.min(0)]],
    });

    this.loadAdData();
  }

  loadAdData(): void {
    console.log('Loading ad data for ID:', this.data.adId);
    
    this.adService.getAdById(this.data.adId).subscribe({
      next: async (response) => {
        let adData: Ad;

        if (response instanceof Blob) {
          const text = await response.text();
          adData = JSON.parse(text);
        } else {
          adData = response;
        }

        // Format dates for the form
        if (adData.startDate) {
          const startDate = new Date(adData.startDate);
          adData.startDate = startDate.toISOString().split('T')[0];
        }

        if (adData.endDate) {
          const endDate = new Date(adData.endDate);
          adData.endDate = endDate.toISOString().split('T')[0];
        }

        // Store the original ad data for later use
        this.originalAd = { ...adData };

        console.log("Original ad data:", this.originalAd);
        console.log("Ad status:", this.originalAd.status);

        // Populate the form with ad data
        this.adForm.patchValue(adData);

        // Check if expiresAt matches endDate to set the toggle
        if (adData.expiresAt && adData.endDate) {
          // Convert to date strings for comparison (ignoring time)
          const expiresAtDate = new Date(adData.expiresAt).toDateString();
          const endDate = new Date(adData.endDate).toDateString();

          // Set the toggle based on whether dates match
          this.adForm.patchValue({
            useEndDateAsExpiry: expiresAtDate === endDate
          });
        }

        console.log("Ad data received for modification:", adData);
      },
      error: (err) => {
        console.error('Error retrieving ad data', err);
        alert('Failed to load ad data. Please try again.');
        this.dialogRef.close(false);
      }
    });
  }

  /**
   * Helper method to format a date with time
   * @param dateStr Date string in format YYYY-MM-DD
   * @param timeStr Time string in format HH:MM
   * @returns ISO string with correct date and time
   */
  formatDateWithTime(dateStr: string, timeStr: string): string {
    // Create a date object from the date string
    const date = new Date(dateStr);

    // Parse the time string (format: HH:MM)
    if (timeStr) {
      const [hours, minutes] = timeStr.split(':').map(Number);

      // Set the hours and minutes on the date
      date.setHours(hours, minutes, 0, 0);
    }

    // Return the ISO string
    return date.toISOString();
  }

  /**
   * Handle file selection for image upload
   */
  onFileSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.selectedFile = event.target.files[0];
      console.log('File selected:', this.selectedFile?.name || 'unknown');
    } else {
      this.selectedFile = null;
    }
  }

  /**
   * Update the ad with or without a new image
   */
  updateAd(): void {
    if (this.adForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      console.log('Starting ad update process...');

      // Create a copy of the form values
      const formValues = { ...this.adForm.value };

      // Set expiresAt based on the toggle
      if (formValues.useEndDateAsExpiry) {
        // Use the helper method to format the date with time
        formValues.expiresAt = this.formatDateWithTime(formValues.endDate, formValues.endTime);
        console.log('Setting expiration date:', formValues.expiresAt);
      } else {
        formValues.expiresAt = undefined;
      }

      // Remove the toggle field as it's not part of the Ad model
      delete formValues.useEndDateAsExpiry;

      // Format dates with their respective times
      formValues.startDate = this.formatDateWithTime(formValues.startDate, formValues.startTime);
      formValues.endDate = this.formatDateWithTime(formValues.endDate, formValues.endTime);

      // Create the updated ad object, preserving important fields from the original ad
      const updatedAd: Ad = {
        ...formValues,
        id: this.data.adId,  // Include the ad ID for the backend to identify the record
        status: this.originalAd?.status || 'APPROVED', // Preserve the original status or default to APPROVED
        createdAt: this.originalAd?.createdAt, // Preserve the creation date
        userId: this.originalAd?.userId // Preserve the user ID
      };

      // Log the updated ad for debugging
      console.log('Updating ad with data:', updatedAd);

      // If a new image was selected, convert it to base64 and include it in the update
      if (this.selectedFile) {
        console.log('Including new image in update:', this.selectedFile?.name || 'file');

        // Read the file as base64
        const reader = new FileReader();
        reader.onload = (e: any) => {
          if (e.target && e.target.result) {
            // Extract the base64 data (remove the data:image/xxx;base64, prefix)
            let base64String = e.target.result;
            if (typeof base64String === 'string' && base64String.includes(',')) {
              base64String = base64String.split(',')[1];
            }

            // Update the ad with the new image
            updatedAd.image = base64String;
          }

          // Now send the update
          this.sendUpdateRequest(updatedAd);
        };

        reader.onerror = (error) => {
          console.error('Error reading file:', error);
          this.isSubmitting = false;
          alert('Failed to process the selected image. Please try again.');
        };

        // Start reading the file
        reader.readAsDataURL(this.selectedFile);
      } else {
        // No new image, just send the update
        this.sendUpdateRequest(updatedAd);
      }
    } else if (!this.adForm.valid) {
      console.error('Form is invalid!');
      // Show which fields are invalid
      Object.keys(this.adForm.controls).forEach(key => {
        const control = this.adForm.get(key);
        if (control?.invalid) {
          console.log(`Field ${key} is invalid:`, control.errors);
        }
      });
      alert('Please fill in all required fields correctly.');
    }
  }

  /**
   * Send the update request to the server
   * @param updatedAd The ad to update
   */
  private sendUpdateRequest(updatedAd: Ad): void {
    console.log('Sending update request with data:', updatedAd);

    // Log the status for debugging
    console.log('Ad status before update:', updatedAd.status);

    // Ensure the status is set to APPROVED to make it visible in the list
    if (!updatedAd.status) {
      updatedAd.status = 'APPROVED';
      console.log('Setting status to APPROVED to ensure visibility');
    }

    this.adService.updateAd(updatedAd).subscribe({
      next: (response) => {
        console.log('Ad updated successfully:', response);

        // Store the updated ad ID in localStorage to help with debugging
        localStorage.setItem('lastUpdatedAdId', updatedAd.id || '');
        localStorage.setItem('lastUpdateTime', new Date().toISOString());

        this.isSubmitting = false;
        alert('Ad updated successfully!');

        // Close the dialog with success result
        this.dialogRef.close(true);
      },
      error: (err) => {
        console.error("Error updating ad", err);
        this.isSubmitting = false;
        alert('Failed to update ad. Please try again.');
      }
    });
  }

  /**
   * Cancel editing and close the dialog
   */
  cancelEdit(): void {
    this.dialogRef.close(false);
  }
}
