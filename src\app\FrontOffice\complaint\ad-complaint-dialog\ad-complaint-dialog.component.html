<div class="complaint-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>report_problem</mat-icon>
      </div>
      <h2 class="header-title">Report Ad</h2>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()" [disabled]="submitting">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Progress Indicator -->
  <div class="progress-container" *ngIf="submitting">
    <div class="progress-bar"></div>
  </div>

  <!-- Form Content -->
  <form [formGroup]="complaintForm" (ngSubmit)="onSubmit()">
    <mat-dialog-content>
      <div class="form-content">
        <!-- Tabs for different sections -->
        <div class="tabs">
          <div class="tab" [class.active]="activeTab === 'details'" (click)="setActiveTab('details')">
            <mat-icon>description</mat-icon>
            <span>Details</span>
          </div>
          <div class="tab" [class.active]="activeTab === 'evidence'" (click)="setActiveTab('evidence')">
            <mat-icon>attach_file</mat-icon>
            <span>Evidence</span>
          </div>
          <div class="tab" [class.active]="activeTab === 'contact'" (click)="setActiveTab('contact')">
            <mat-icon>contact_mail</mat-icon>
            <span>Contact</span>
          </div>
        </div>

        <!-- Details Tab -->
        <div class="tab-content" *ngIf="activeTab === 'details'">
          <!-- Ad Title -->
          <div class="ad-title-compact">
            <mat-icon>info</mat-icon>
            <span class="title-text">{{ data.adTitle }}</span>
          </div>

          <!-- Complaint Type Section -->
          <div class="complaint-type-section">
            <div class="section-label-compact">
              <mat-icon>category</mat-icon>
              <span>Complaint Type</span>
            </div>
            <div class="complaint-types-compact">
              <div class="complaint-type-compact"
                   *ngFor="let type of complaintTypes"
                   [class.selected]="selectedComplaintType === type.value"
                   (click)="selectComplaintType(type.value)">
                <mat-icon>{{ type.icon }}</mat-icon>
                <span>{{ type.label }}</span>
              </div>
            </div>
          </div>

          <!-- Description Section -->
          <div class="description-section">
            <div class="section-label-compact">
              <mat-icon>description</mat-icon>
              <span>Description</span>
            </div>
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Describe your complaint</mat-label>
              <textarea
                matInput
                formControlName="description"
                rows="3"
                placeholder="Please explain why you're reporting this ad..."></textarea>
              <mat-hint align="end" [class.warning]="isDescriptionLengthWarning()">
                {{complaintForm.get('description')?.value?.length || 0}}/500
              </mat-hint>
              <mat-error *ngIf="complaintForm.get('description')?.hasError('required')">
                Description is required
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Urgency Level Section -->
          <div class="urgency-section">
            <div class="section-label-compact">
              <mat-icon>priority_high</mat-icon>
              <span>Urgency</span>
            </div>
            <div class="urgency-buttons">
              <button type="button" class="urgency-button"
                      [class.selected]="complaintForm.get('urgency')?.value === 'LOW'"
                      (click)="setUrgency('LOW')">
                <mat-icon>arrow_downward</mat-icon>
                <span>Low</span>
              </button>
              <button type="button" class="urgency-button"
                      [class.selected]="complaintForm.get('urgency')?.value === 'MEDIUM'"
                      (click)="setUrgency('MEDIUM')">
                <mat-icon>remove</mat-icon>
                <span>Medium</span>
              </button>
              <button type="button" class="urgency-button"
                      [class.selected]="complaintForm.get('urgency')?.value === 'HIGH'"
                      (click)="setUrgency('HIGH')">
                <mat-icon>arrow_upward</mat-icon>
                <span>High</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Evidence Tab -->
        <div class="tab-content" *ngIf="activeTab === 'evidence'">
          <div class="evidence-section">
            <div class="section-label-compact">
              <mat-icon>attach_file</mat-icon>
              <span>Upload Evidence</span>
            </div>
            <div class="upload-container-compact" (click)="fileInput.click()" [class.has-file]="hasFile">
              <input type="file" #fileInput hidden (change)="onFileSelected($event)">
              <div class="upload-content">
                <mat-icon>{{ hasFile ? 'check_circle' : 'cloud_upload' }}</mat-icon>
                <span *ngIf="!hasFile">Click to upload evidence</span>
                <span *ngIf="hasFile">{{ fileName }}</span>
              </div>
            </div>
            <button *ngIf="hasFile" type="button" class="remove-file-btn" (click)="removeFile($event)">
              <mat-icon>delete</mat-icon>
              Remove file
            </button>
          </div>
        </div>

        <!-- Contact Tab -->
        <div class="tab-content" *ngIf="activeTab === 'contact'">
          <div class="contact-section">
            <div class="section-label-compact">
              <mat-icon>contact_mail</mat-icon>
              <span>Contact Information</span>
            </div>
            <div class="contact-fields-compact">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email" placeholder="Your email address">
                <mat-icon matSuffix>email</mat-icon>
                <mat-error *ngIf="complaintForm.get('email')?.hasError('email')">
                  Please enter a valid email address
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Phone</mat-label>
                <input matInput formControlName="phone" placeholder="Your phone number">
                <mat-icon matSuffix>phone</mat-icon>
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div *ngIf="error" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ error }}</span>
        </div>
      </div>
    </mat-dialog-content>

    <!-- Form Actions -->
    <mat-dialog-actions>
      <button mat-button type="button" class="cancel-button" (click)="onCancel()" [disabled]="submitting">
        <mat-icon>close</mat-icon>
        Cancel
      </button>
      <button
        mat-raised-button
        class="submit-button"
        type="submit"
        [disabled]="complaintForm.invalid || submitting">
        <mat-icon>{{ submitting ? 'hourglass_empty' : 'send' }}</mat-icon>
        {{ submitting ? 'Submitting...' : 'Submit' }}
      </button>
    </mat-dialog-actions>
  </form>
</div>
