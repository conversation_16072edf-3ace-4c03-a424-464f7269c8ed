<app-header-front></app-header-front>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">

<div class="ads-page-wrapper">
  <!-- Page Header with Background -->
  <div class="ads-page-header">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h1 class="ads-title">Advertisements</h1>
          <div class="ads-subtitle">Find what you're looking for or create your own ad</div>
        </div>
        <div class="col-md-6 text-md-end">
          <button class="btn btn-primary me-2" (click)="openCreateAdDialog()">
            <i class="fas fa-plus"></i> Create Ad
          </button>
          <button class="btn btn-outline-light" (click)="reloadAds()">
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="container custom-container mt-4">

  <!-- Main Content Area -->
  <div class="row">
    <!-- Left Sidebar - Filters -->
    <div class="col-md-3 mb-4 ps-0">
      <div class="filter-sidebar">
        <!-- Filter Header -->
        <div class="filter-header">
          <div class="d-flex align-items-center">
            <i class="fas fa-filter me-2"></i>
            <h5 class="mb-0">Filters</h5>
          </div>
          <button type="button" class="btn-reset-all" (click)="resetFilters()" title="Reset all filters">
            <i class="fas fa-undo-alt"></i>
          </button>
        </div>

        <!-- Search Box -->
        <div class="search-box mb-3">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0">
              <i class="fas fa-search text-muted"></i>
            </span>
            <input type="text" class="form-control border-start-0" placeholder="Search ads..."
                  (input)="onSearchInput($event)">
          </div>
        </div>

        <!-- Category Filter Buttons -->
        <div class="filter-section">
          <div class="filter-section-title">Categories</div>
          <div class="category-filters">
            <button class="filter-btn all-btn" [class.active]="selectedCategories.length === 0" (click)="selectAllCategories()">
              <i class="fas fa-check-circle me-1"></i> All
            </button>

            <button *ngFor="let category of categories"
                   class="filter-btn"
                   [class.active]="selectedCategories.includes(category)"
                   [ngClass]="'category-' + category.toLowerCase() + '-btn'"
                   (click)="toggleCategory(category)">
              <i class="fas fa-circle me-1"></i> {{ category }}
            </button>
          </div>
        </div>

        <!-- Advanced Filters Toggle -->
        <button class="btn-toggle-filters" type="button" (click)="toggleFilters()">
          {{ showFilters ? 'Hide Advanced Filters' : 'Show Advanced Filters' }}
          <i class="fas" [ngClass]="showFilters ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
        </button>

        <!-- Advanced Filters -->
        <div class="advanced-filters" *ngIf="showFilters">
          <form [formGroup]="filterForm">
            <!-- Price Range Filter -->
            <div class="filter-section">
              <div class="filter-section-title">Price Range (TND)</div>
              <div class="row g-2">
                <div class="col-6">
                  <div class="input-group input-group-sm">
                    <span class="input-group-text">Min</span>
                    <input type="number" class="form-control" formControlName="priceMin" placeholder="Min">
                  </div>
                </div>
                <div class="col-6">
                  <div class="input-group input-group-sm">
                    <span class="input-group-text">Max</span>
                    <input type="number" class="form-control" formControlName="priceMax" placeholder="Max">
                  </div>
                </div>
              </div>
            </div>

            <!-- Date Filter -->
            <div class="filter-section">
              <div class="filter-section-title">Date Posted</div>
              <div class="date-filters">
                <div class="form-check">
                  <input class="form-check-input" type="radio" id="dateFilter-last24h"
                         formControlName="dateFilter" value="last24h">
                  <label class="form-check-label" for="dateFilter-last24h">Last 24 hours</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" id="dateFilter-lastWeek"
                         formControlName="dateFilter" value="lastWeek">
                  <label class="form-check-label" for="dateFilter-lastWeek">Last week</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" id="dateFilter-lastMonth"
                         formControlName="dateFilter" value="lastMonth">
                  <label class="form-check-label" for="dateFilter-lastMonth">Last month</label>
                </div>
              </div>
            </div>

            <!-- Location Filter -->
            <div class="filter-section">
              <div class="filter-section-title">
                Location
                <i class="fas fa-info-circle ms-1 text-muted" title="Filter ads by location. Only ads with location data will appear in results. Look for the green location icon on ads."></i>
              </div>
              <div class="input-group input-group-sm mb-2">
                <input type="text" class="form-control" formControlName="location" placeholder="Enter location">
                <button class="btn btn-outline-secondary" type="button" (click)="useCurrentLocationForFilter()" [disabled]="isLocationLoading" title="Use my current location">
                  <i class="fas" [ngClass]="isLocationLoading ? 'fa-spinner fa-spin' : 'fa-map-marker-alt'"></i>
                </button>
              </div>
              <div class="radius-slider">
                <label class="form-label small">Radius: <span class="radius-value">{{ filterForm.get('locationRadius')?.value }} km</span></label>
                <input type="range" class="form-range" min="1" max="50" formControlName="locationRadius">
                <div class="d-flex justify-content-between">
                  <small>1 km</small>
                  <small>50 km</small>
                </div>
              </div>
              <div class="location-help small text-muted mt-1">
                <i class="fas fa-info-circle me-1"></i> Enter a location name or use your current location
              </div>
              <div class="d-flex justify-content-between mt-2" *ngIf="isAdmin()">
                <button type="button" class="btn btn-sm btn-outline-primary" (click)="setManualLocation(36.8065, 10.1815)" title="Set test location to Tunis">
                  <i class="fas fa-map-pin"></i> Test Tunis
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" (click)="clearLocationCache()" title="Clear location cache">
                  <i class="fas fa-broom"></i> Clear Cache
                </button>
              </div>
            </div>

            <!-- Filter Actions -->
            <div class="filter-actions">
              <button type="button" class="btn btn-primary btn-apply-filters" (click)="applyFilters()">
                <i class="fas fa-search me-1"></i> Apply Filters
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Right Content Area - Ad Cards -->
    <div class="col-md-9">
      <!-- Results Control Bar -->
      <div class="results-control-bar">
        <div class="row align-items-center">
          <div class="col-md-8">
            <!-- New Ads Counter -->
            <div class="new-ads-counter mb-3">{{ getNewAdsCount() }} new today</div>

            <!-- Active Filters Display -->
            <div class="active-filters-wrapper" *ngIf="hasActiveFilters()">
              <div class="active-filters-label">Active filters:</div>
              <div class="active-filters-chips">
                <!-- Category Chips -->
                <span class="filter-chip" *ngFor="let category of selectedCategories">
                  <span class="chip-icon"><i class="fas fa-tag"></i></span>
                  <span class="chip-text">{{ category }}</span>
                  <button class="chip-remove" (click)="removeCategory(category)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- Price Range Chip -->
                <span class="filter-chip" *ngIf="filterForm.get('priceMin')?.value || filterForm.get('priceMax')?.value">
                  <span class="chip-icon"><i class="fas fa-dollar-sign"></i></span>
                  <span class="chip-text">{{ filterForm.get('priceMin')?.value || '0' }} - {{ filterForm.get('priceMax')?.value || '∞' }} TND</span>
                  <button class="chip-remove" (click)="clearPriceFilter()">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- Date Filter Chip -->
                <span class="filter-chip" *ngIf="filterForm.get('dateFilter')?.value">
                  <span class="chip-icon"><i class="fas fa-calendar"></i></span>
                  <span class="chip-text">{{ getDateFilterLabel() }}</span>
                  <button class="chip-remove" (click)="clearDateFilter()">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- Location Chip -->
                <span class="filter-chip" *ngIf="filterForm.get('location')?.value">
                  <span class="chip-icon"><i class="fas fa-map-marker-alt"></i></span>
                  <span class="chip-text">{{ filterForm.get('location')?.value }} ({{ filterForm.get('locationRadius')?.value }} km)</span>
                  <button class="chip-remove" (click)="clearLocationFilter()">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- Clear All Button -->
                <button class="btn-clear-filters" (click)="resetFilters()">
                  Clear All <i class="fas fa-times-circle ms-1"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <!-- Sort Options -->
            <div class="sort-controls">
              <div class="sort-dropdown">
                <label class="sort-label">Sort by:</label>
                <select class="form-select" (change)="sortAds($event)">
                  <option value="newest">Newest</option>
                  <option value="oldest">Oldest</option>
                  <option value="priceAsc">Price: Low to High</option>
                  <option value="priceDesc">Price: High to Low</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Ad Cards Grid -->
      <div class="ads-grid">
        <!-- Message when no ads are available -->
        <div *ngIf="ads.length === 0" class="no-results">
          <div class="no-ads-message">
            <i class="fas fa-search fa-3x mb-3"></i>
            <h4>No ads available</h4>
            <p>Try adjusting your filters or create a new ad</p>
            <button class="btn btn-primary mt-3" (click)="resetFilters()">
              <i class="fas fa-undo me-1"></i> Reset Filters
            </button>
          </div>
        </div>

        <!-- Ad Cards -->
        <div class="row">
          <!-- Ad Cards -->
          <div *ngFor="let ad of ads" class="col-lg-6 mb-4">
            <div class="ad-card" [class.my-ad]="isAdOwner(ad)">
              <!-- Category Badge -->
              <div class="category-badge" [ngClass]="getCategoryClass(ad.category)">
                {{ ad.category }}
              </div>

              <!-- Price Badge -->
              <div class="price-tag" *ngIf="getPrice(ad) !== 'N/A'" [ngClass]="{'price-na': getPrice(ad) === 'N/A'}">
                {{ getPrice(ad) }}
              </div>

              <!-- Image Section -->
              <div class="ad-image-wrapper">
                <div class="ad-image-container">
                  <div [id]="getCarouselId(ad.id)" class="carousel slide" data-bs-ride="false">
                    <!-- Carousel items -->
                    <div class="carousel-inner">
                      <div *ngFor="let image of getImages(ad); let i = index"
                           class="carousel-item"
                           [class.active]="i === 0">
                        <img [src]="'data:image/jpeg;base64,' + image"
                             class="d-block w-100"
                             alt="{{ ad.title }} image {{ i+1 }}"
                             onerror="this.onerror=null; this.src='assets/images/placeholder-image.png'; this.alt='Image could not be loaded';">
                      </div>

                      <!-- Fallback if no images -->
                      <div *ngIf="getImages(ad).length === 0" class="carousel-item active">
                        <div class="no-image-placeholder">
                          <i class="fas fa-image"></i>
                        </div>
                      </div>
                    </div>

                    <!-- Controls -->
                    <button *ngIf="getImages(ad).length > 1" class="carousel-control-prev" type="button" (click)="navigateCarousel(getCarouselId(ad.id), 'prev')">
                      <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    </button>
                    <button *ngIf="getImages(ad).length > 1" class="carousel-control-next" type="button" (click)="navigateCarousel(getCarouselId(ad.id), 'next')">
                      <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Content Section -->
              <div class="ad-content">
                <!-- Report Flag Button -->
                <button class="flag-button" (click)="openComplaintDialog(ad, $event)" aria-label="Report this ad" title="Report this ad">
                  <i class="fas fa-flag"></i>
                </button>

                <!-- Admin Actions (only shown to ad owner or admin) -->
                <div class="admin-actions" *ngIf="canEditOrDeleteAd(ad)">
                  <button class="admin-action-btn edit-btn" (click)="editAd(ad.id!)" title="Edit ad">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="admin-action-btn delete-btn" (click)="ad.id ? deleteAd(ad.id) : null" title="Delete ad">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>

                <!-- Ad Title -->
                <h3 class="ad-title">{{ ad.title }}</h3>

                <!-- Ad Description -->
                <p class="ad-description">
                  {{ (ad.description || '') | slice:0:100 }}{{ ad.description && ad.description.length > 100 ? '...' : '' }}
                </p>

                <!-- Timeline Section -->
                <div class="ad-timeline">
                  <div class="timeline-item">
                    <div class="timeline-icon start-icon">
                      <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="timeline-content">
                      <span class="timeline-date">{{ getFormattedStartDateTime(ad) }}</span>
                      <span class="timeline-label">Start</span>
                    </div>
                  </div>
                  <div class="timeline-item">
                    <div class="timeline-icon end-icon">
                      <i class="fas fa-stop-circle"></i>
                    </div>
                    <div class="timeline-content">
                      <span class="timeline-date">{{ getFormattedEndDateTime(ad) }}</span>
                      <span class="timeline-label">End</span>
                    </div>
                  </div>
                </div>

                <!-- Ad Stats and Actions -->
                <div class="ad-actions-row">
                  <div class="ad-stats">
                    <!-- Like Button -->
                    <button class="stat-btn like-btn" [class.active]="isLiked(ad)" (click)="toggleLike(ad, $event)">
                      <i class="fas fa-heart" [class.text-danger]="isLiked(ad)"></i>
                      <span class="stat-count">{{ isLiked(ad) ? 'Liked' : 'Like' }}</span>
                    </button>

                    <!-- Views -->
                    <div class="stat-item">
                      <i class="fas fa-eye"></i>
                      <span class="stat-count">{{ ad.viewCount || 0 }}</span>
                    </div>

                    <!-- Posted Date -->
                    <div class="stat-item posted-date">
                      <i class="fas fa-calendar-alt"></i>
                      <span class="stat-count">{{ ad.createdAt | date:'MMM d' }}</span>
                    </div>

                    <!-- Location Indicator (if available) -->
                    <div class="stat-item location-indicator" *ngIf="ad.location && ad.location.length === 2" title="This ad has location data">
                      <i class="fas fa-map-marker-alt text-success"></i>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="action-buttons">
                    <!-- Contact Button -->
                    <button class="contact-btn" (click)="$event.stopPropagation()">
                      <i class="fas fa-envelope"></i>
                    </button>

                    <!-- Quick View Button -->
                    <button class="quick-view-btn" (click)="goToAdDetails(ad.id!)">
                      <i class="fas fa-eye"></i> Quick View
                    </button>

                    <!-- Debug Button (only visible for admins) -->
                    <button *ngIf="isAdmin()" class="debug-btn" (click)="debugAdLocation(ad); $event.stopPropagation()" title="Debug location">
                      <i class="fas fa-map-pin"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <div class="pagination-dots">
          <span class="dot active"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>
  </div>
</div>
</div>

<app-footer-front></app-footer-front>
