import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, throwError, timer } from 'rxjs';
import { catchError, map, retry, timeout, delay, switchMap } from 'rxjs/operators';

export interface GeoLocation {
  lat: number;
  lng: number;
  name?: string;
  accuracy?: number;
  source?: string;
}

@Injectable({
  providedIn: 'root'
})
export class LocationService {
  // Cache for geocoding results to avoid repeated API calls
  private geocodeCache: Map<string, GeoLocation> = new Map();

  constructor(private http: HttpClient) {}

  /**
   * Geocode a location name to coordinates using Nominatim (OpenStreetMap)
   * @param locationName The name of the location to geocode
   * @returns Observable of GeoLocation object
   */
  geocodeLocation(locationName: string): Observable<GeoLocation | null> {
    console.log('Geocoding location:', locationName);

    if (!locationName || locationName.trim() === '') {
      console.warn('Empty location name provided');
      return of(null);
    }

    // Normalize the location name for caching
    const normalizedName = locationName.trim().toLowerCase();

    // Check cache first
    if (this.geocodeCache.has(normalizedName)) {
      console.log('Using cached geocode result for:', locationName);
      return of(this.geocodeCache.get(normalizedName)!);
    }

    // Check if the input is already coordinates in the format "lat, lng"
    const coordsMatch = locationName.match(/^(-?\d+(\.\d+)?),\s*(-?\d+(\.\d+)?)$/);
    if (coordsMatch) {
      console.log('Input appears to be coordinates, parsing directly');
      const lat = parseFloat(coordsMatch[1]);
      const lng = parseFloat(coordsMatch[3]);
      const result = { lat, lng, source: 'direct-parse' };

      // Cache the result
      this.geocodeCache.set(normalizedName, result);

      return of(result);
    }

    // Use Nominatim for geocoding with improved error handling
    console.log('Calling Nominatim API for:', locationName);
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(locationName)}&limit=1&addressdetails=1`;

    const headers = new HttpHeaders({
      'Accept-Language': 'en',
      'User-Agent': 'SpeedyGoApp'
    });

    return this.http.get<any[]>(url, { headers }).pipe(
      timeout(10000), // 10 second timeout
      retry(2), // Retry failed requests up to 2 times
      map(response => {
        console.log('Nominatim API response:', response);

        if (response && response.length > 0) {
          const result = response[0];
          const geoLocation: GeoLocation = {
            lat: parseFloat(result.lat),
            lng: parseFloat(result.lon),
            name: result.display_name,
            accuracy: result.importance * 10, // Convert importance to an approximate accuracy value
            source: 'nominatim'
          };

          console.log('Successfully geocoded to:', geoLocation);

          // Cache the result
          this.geocodeCache.set(normalizedName, geoLocation);

          return geoLocation;
        }

        console.warn('No results found for location:', locationName);
        return null;
      }),
      catchError(error => {
        console.error('Error geocoding location with Nominatim:', error);

        // Try fallback geocoding service after a short delay
        return timer(500).pipe(
          switchMap(() => this.fallbackGeocode(locationName))
        );
      })
    );
  }

  /**
   * Fallback geocoding method when Nominatim fails
   * @param locationName The name of the location to geocode
   * @returns Observable of GeoLocation object
   */
  private fallbackGeocode(locationName: string): Observable<GeoLocation | null> {
    console.log('Using fallback geocoding for:', locationName);

    // For now, we'll just return null as a fallback
    // In a production app, you could implement a different geocoding service here
    return of(null);
  }

  /**
   * Calculate the distance between two points using the Haversine formula
   * @param lat1 Latitude of point 1
   * @param lng1 Longitude of point 1
   * @param lat2 Latitude of point 2
   * @param lng2 Longitude of point 2
   * @returns Distance in kilometers
   */
  calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    // Validate inputs
    if (isNaN(lat1) || isNaN(lng1) || isNaN(lat2) || isNaN(lng2)) {
      console.error('Invalid coordinates provided to calculateDistance:', { lat1, lng1, lat2, lng2 });
      return Infinity; // Return Infinity for invalid inputs
    }

    const R = 6371; // Radius of the earth in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lng2 - lng1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km

    return distance;
  }

  /**
   * Convert degrees to radians
   * @param deg Degrees
   * @returns Radians
   */
  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  /**
   * Parse location input which could be a location name or coordinates
   * @param locationInput The location input to parse
   * @returns Observable of GeoLocation object
   */
  parseLocationInput(locationInput: string): Observable<GeoLocation | null> {
    console.log('Parsing location input:', locationInput);

    if (!locationInput || locationInput.trim() === '') {
      console.warn('Empty location input provided');
      return of(null);
    }

    // Check if the input is coordinates in the format "lat, lng"
    const coordsMatch = locationInput.match(/^(-?\d+(\.\d+)?),\s*(-?\d+(\.\d+)?)$/);
    if (coordsMatch) {
      console.log('Input appears to be coordinates, parsing directly');
      const lat = parseFloat(coordsMatch[1]);
      const lng = parseFloat(coordsMatch[3]);
      return of({ lat, lng, source: 'direct-parse' });
    }

    // Otherwise, treat as a location name and geocode it
    console.log('Input appears to be a location name, geocoding');
    return this.geocodeLocation(locationInput);
  }

  /**
   * Clear the geocoding cache
   */
  clearCache(): void {
    console.log('Clearing geocode cache');
    this.geocodeCache.clear();
  }
}
