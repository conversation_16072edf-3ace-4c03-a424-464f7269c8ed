<app-navbar-back></app-navbar-back>

<div class="d-flex">
  <!-- Sidebar (Fixed on the Left) -->
  <div class="sidebar">
    <app-sidebar-back></app-sidebar-back>
  </div>

  <!-- Main Content (Takes remaining space) -->
  <div class="container mt-4 content">
    <h2 class="text-primary text-center mb-4">Leave Requests List</h2>

    <div class="table-responsive">
      <table class="table table-bordered table-hover shadow-sm">
        <thead class="table-dark">
          <tr>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Reason</th>
            <th>Status</th>
            <th class="text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let leave of leaves">
            <td>{{ leave.startDate | date:'dd/MM/yyyy' }}</td>
            <td>{{ leave.endDate | date:'dd/MM/yyyy' }}</td>
            <td>{{ leave.reason }}</td>
            <td>
              <span 
                class="badge" 
                [ngClass]="{
                  'bg-warning text-dark': leave.status === 'PENDING',
                  'bg-success': leave.status === 'APPROVED',
                  'bg-danger': leave.status === 'REJECTED'
                }">
                {{ leave.status }}
              </span>
            </td>
            <td class="text-center">
              <!-- ✅ Approve Button (Visible only if leave is PENDING) -->
              <button *ngIf="leave.status === 'PENDING'" class="btn btn-success btn-sm me-2" (click)="approveLeave(leave)">
                <i class="fas fa-check-circle"></i> Approve
              </button>
              
              <!-- ❌ Reject Button (Visible only if leave is PENDING) -->
              <button *ngIf="leave.status === 'PENDING'" class="btn btn-danger btn-sm" (click)="rejectLeave(leave)">
                <i class="fas fa-times-circle"></i> Reject
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<app-footer-back></app-footer-back>
