<app-header-front></app-header-front>

<div class="container mt-4">
  <div class="card shadow-lg p-4">
    <h2 class="text-primary text-center mb-4">
      <i class="fas fa-ad"></i> Ad Details
    </h2>

    <div class="row">
      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-file-alt"></i> Title:</label>
        <p class="text-dark border rounded p-2 bg-light">{{ ad.title }}</p>
      </div>

      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-align-left"></i> Description:</label>
        <p class="text-muted border rounded p-2 bg-light">{{ ad.description }}</p>
      </div>

      <div class="col-md-6 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-images"></i> Images:</label>

        <!-- Image Carousel -->
        <div id="adImagesCarousel" class="carousel slide" data-bs-ride="false">
          <!-- Indicators -->
          <div class="carousel-indicators">
            <button *ngFor="let image of getImages(); let i = index"
                    type="button"
                    [attr.data-bs-target]="'#adImagesCarousel'"
                    [attr.data-bs-slide-to]="i"
                    [class.active]="i === 0"
                    [attr.aria-current]="i === 0 ? 'true' : null"
                    [attr.aria-label]="'Slide ' + (i+1)"></button>
          </div>

          <!-- Carousel items -->
          <div class="carousel-inner rounded">
            <div *ngFor="let image of getImages(); let i = index"
                 class="carousel-item"
                 [class.active]="i === 0">
              <img [src]="'data:image/jpeg;base64,' + image"
                   class="d-block w-100"
                   alt="{{ ad.title }} image {{ i+1 }}"
                   style="max-height: 400px; object-fit: contain;">
            </div>

            <!-- Fallback if no images -->
            <div *ngIf="getImages().length === 0" class="carousel-item active">
              <div class="no-image-placeholder d-flex align-items-center justify-content-center bg-light" style="height: 300px;">
                <i class="fas fa-image fa-3x text-muted"></i>
              </div>
            </div>
          </div>

          <!-- Controls -->
          <button *ngIf="getImages().length > 1" class="carousel-control-prev" type="button" (click)="navigateCarousel('prev')">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button *ngIf="getImages().length > 1" class="carousel-control-next" type="button" (click)="navigateCarousel('next')">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-tags"></i> Category:</label>
        <p class="text-dark border rounded p-2 bg-light">{{ ad.category }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-tag"></i> Price:</label>
        <p class="text-dark border rounded p-2 bg-light">
          <span class="price-badge">{{ getPrice() }}</span>
        </p>
      </div>

      <!-- Date and Time Section -->
      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-calendar-alt"></i> Start Date:</label>
        <p class="border rounded p-2 bg-light">{{ ad.startDate | date:'mediumDate' }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-clock"></i> Start Time:</label>
        <p class="border rounded p-2 bg-light">{{ ad.startTime || 'Not specified' }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-calendar-alt"></i> End Date:</label>
        <p class="border rounded p-2 bg-light">{{ ad.endDate | date:'mediumDate' }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-clock"></i> End Time:</label>
        <p class="border rounded p-2 bg-light">{{ ad.endTime || 'Not specified' }}</p>
      </div>

      <!-- Tags Section -->
      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-tags"></i> Tags:</label>
        <div class="border rounded p-2 bg-light">
          <span *ngIf="!ad.tags || ad.tags.length === 0" class="text-muted">No tags</span>
          <span *ngFor="let tag of ad.tags" class="badge bg-primary me-1">{{ tag }}</span>
        </div>
      </div>

      <!-- Location Section -->
      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-map-marker-alt"></i> Location:</label>
        <p class="border rounded p-2 bg-light">{{ ad.locationName || 'Location not specified' }}</p>

        <!-- Show Map if location coordinates are available -->
        <div *ngIf="shouldShowMap()" class="mt-2">
          <div class="map-container border rounded" style="overflow: hidden;">
            <div #map style="width: 100%; height: 300px; max-height: 300px; border-radius: 5px;"></div>
          </div>
        </div>
      </div>

      <div class="col-md-12 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-info-circle"></i> Status:</label>
        <p class="border rounded p-2 bg-light">{{ ad.status }}</p>
      </div>

      <!-- Enhanced metadata fields -->
      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-eye"></i> Views:</label>
        <p class="border rounded p-2 bg-light">{{ ad.viewCount || 0 }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-calendar-plus"></i> Created:</label>
        <p class="border rounded p-2 bg-light">{{ ad.createdAt | date:'medium' }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-calendar-check"></i> Updated:</label>
        <p class="border rounded p-2 bg-light">{{ ad.updatedAt | date:'medium' }}</p>
      </div>

      <div class="col-md-3 mb-3">
        <label class="fw-bold text-secondary"><i class="fas fa-hourglass-end"></i> Expires:</label>
        <p class="border rounded p-2 bg-light" [ngClass]="{'text-danger': isExpiringSoon()}">
          {{ formatExpiryDate() }}
          <span *ngIf="isExpiringSoon()" class="badge bg-warning text-dark ms-2">Expiring soon!</span>
        </p>
      </div>
    </div>

    <div class="d-flex justify-content-center mt-4">
      <button class="btn btn-secondary btn-lg px-4" (click)="goBack()">
        <i class="fas fa-arrow-left"></i> Back to Ads
      </button>
    </div>
  </div>
</div>

<app-footer-front></app-footer-front>
