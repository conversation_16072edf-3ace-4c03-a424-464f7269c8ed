{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"frontspeedygo": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/frontspeedygo"}, "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/leaflet/dist/images/", "output": "./assets/leaflet/"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/leaflet/dist/leaflet.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "frontspeedygo:build:production"}, "development": {"buildTarget": "frontspeedygo:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "src/proxy.conf.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "frontspeedygo:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}