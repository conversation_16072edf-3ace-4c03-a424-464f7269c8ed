import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SidebarBackComponent } from '../sidebar-back/sidebar-back.component';
import { Ad, AdControllerService } from 'src/app/openapi';
import { NavbarBackComponent } from '../navbar-back/navbar-back.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-ad-text-review',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    SidebarBackComponent,
    NavbarBackComponent,
    MatDialogModule,
    MatButtonModule
  ],
  templateUrl: './ad-text-review.component.html',
  styleUrls: ['./ad-text-review.component.css']
})
export class AdAdminReviewComponent implements OnInit {
  pendingAds: Ad[] = [];

  constructor(
    private adService: AdControllerService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadPendingAds();
  }

  loadPendingAds(): void {
    this.adService.getPendingAds().subscribe({
      next: (ads: any) => {
        if (ads instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const parsed = JSON.parse(reader.result as string);
              this.pendingAds = parsed;
            } catch (err) {
              console.error('❌ Erreur de parsing JSON depuis Blob', err);
              this.pendingAds = [];
            }
          };
          reader.readAsText(ads);
        } else {
          this.pendingAds = ads;
        }
      },
      error: (err) => console.error('❌ Erreur lors de la récupération des annonces', err)
    });
  }


  approve(adId: string): void {
    this.adService.approveAd(adId).subscribe({
      next: () => this.loadPendingAds(),
      error: (err) => console.error('Erreur lors de l’approbation', err)
    });
  }

  reject(adId: string): void {
    this.adService.rejectAd(adId).subscribe({
      next: () => this.loadPendingAds(),
      error: (err) => console.error('Erreur lors du rejet', err)
    });
  }

  getStatusLabel(status: Ad.StatusEnum | undefined): string {
    switch (status) {
      case Ad.StatusEnum.Approved:
        return 'Approved';
      case Ad.StatusEnum.PendingAdminReview:
        return 'Pending Review';
      case Ad.StatusEnum.Rejected:
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  getStatusClass(status: Ad.StatusEnum | undefined): string {
    switch (status) {
      case Ad.StatusEnum.Approved:
        return 'badge bg-success';
      case Ad.StatusEnum.PendingAdminReview:
        return 'badge bg-warning';
      case Ad.StatusEnum.Rejected:
        return 'badge bg-danger';
      default:
        return 'badge bg-secondary';
    }
  }

  /**
   * Opens a dialog to view detailed information about an ad
   * @param ad The ad to view details for
   */
  viewDetails(ad: Ad): void {
    import('./ad-detail-dialog/ad-detail-dialog.component').then(({ AdDetailDialogComponent }) => {
      const dialogRef = this.dialog.open(AdDetailDialogComponent, {
        width: '90%',
        maxWidth: '1000px',
        maxHeight: '90vh',
        data: { ad },
        panelClass: 'ad-detail-dialog-container',
        disableClose: true
      });

      // Handle dialog close result
      dialogRef.afterClosed().subscribe(result => {
        if (result && result.action) {
          if (result.action === 'approved' || result.action === 'rejected') {
            // Refresh the list of pending ads
            this.loadPendingAds();

            // Show a success message
            if (result.action === 'approved') {
              alert('Ad approved successfully');
            } else if (result.action === 'rejected') {
              alert(`Ad rejected successfully\nReason: ${result.rejectionReason}`);

              // Note: In a real application, you would want to update the rejection reason
              // in the database. Since the API doesn't support this directly, we're just
              // displaying it to the user here.
            }
          }
        }
      });
    });
  }

}