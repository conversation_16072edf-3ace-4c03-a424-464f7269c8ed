<app-header-front></app-header-front>
<div class="container">
  <h2>Créer une commande</h2>

  <!-- Affichage de la liste des produits -->
  <div>
    <h3>Liste des produits</h3>
    <div *ngFor="let product of products" class="col-md-4 mb-4">
      <img *ngIf="product.image" [src]="'data:image/jpeg;base64,' + product.image" class="card-img-top" alt="{{ product.name }}">
      <p>{{ product.name }} - {{ product.price }} €</p>
      <input type="number" [(ngModel)]="product.selectedQuantity" placeholder="Quantité" min="1"/>
      <button (click)="addProductToOrder(product, product.selectedQuantity ?? 0)">Ajouter</button>
    </div>
  </div>

  <!-- Récapitulatif de la commande -->
  <div>
    <h3>Votre commande</h3>
    <div *ngFor="let item of order.items">
      <p>Produit : {{ item.productId }} - Quantité : {{ item.quantity }} - Prix unitaire : {{ item.price }} €</p>
    </div>
    <p><strong>Total : {{ order.price }} €</strong></p>
    <button (click)="submitOrder()">Valider la commande</button>
  </div>
</div>
<app-footer-front></app-footer-front>
