import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Ad, AdControllerService } from 'src/app/openapi';
import { DomSanitizer } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-ad-detail-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    RouterModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './ad-detail-dialog.component.html',
  styleUrl: './ad-detail-dialog.component.css'
})
export class AdDetailDialogComponent implements OnInit {
  ad: Ad | null = null;
  loading = true;
  error = false;
  currentImageIndex = 0;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { adId: string },
    private dialogRef: MatDialogRef<AdDetailDialogComponent>,
    private adService: AdControllerService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.loadAdDetails();
  }

  loadAdDetails(): void {
    this.loading = true;
    this.error = false;

    this.adService.getAdById(this.data.adId).subscribe({
      next: (ad) => {
        this.ad = ad;
        this.loading = false;

        // Increment view count
        this.incrementViewCount();
      },
      error: (err) => {
        console.error('Error loading ad details:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }

  incrementViewCount(): void {
    if (this.ad && this.ad.id) {
      this.adService.incrementViewCount(this.ad.id).subscribe({
        next: () => console.log('View count incremented'),
        error: (err) => console.error('Error incrementing view count:', err)
      });
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  getImages(): string[] {
    if (!this.ad) return [];

    // If ad has images array, return it
    if (this.ad.images && this.ad.images.length > 0) {
      return this.ad.images;
    }

    // If ad only has a single image, return it as an array
    if (this.ad.image) {
      return [this.ad.image];
    }

    // If no images, return empty array
    return [];
  }

  nextImage(): void {
    const images = this.getImages();
    if (images.length > 0) {
      this.currentImageIndex = (this.currentImageIndex + 1) % images.length;
    }
  }

  prevImage(): void {
    const images = this.getImages();
    if (images.length > 0) {
      this.currentImageIndex = (this.currentImageIndex - 1 + images.length) % images.length;
    }
  }

  getCurrentImage(): string | null {
    const images = this.getImages();
    if (images.length > 0) {
      return images[this.currentImageIndex];
    }
    return null;
  }

  getFormattedDate(date: string | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }

  getFormattedDateTime(date: string | undefined, time: string | undefined): string {
    if (!date) return 'N/A';

    const formattedDate = new Date(date).toLocaleDateString();
    return time ? `${formattedDate} ${time}` : formattedDate;
  }

  getPrice(): string {
    if (!this.ad || this.ad.price === undefined || this.ad.price === null) {
      return 'N/A';
    }
    return `${Number(this.ad.price).toFixed(2)} TND`;
  }

  getCategoryClass(): string {
    if (!this.ad || !this.ad.category) return 'category-other';

    switch (this.ad.category.toUpperCase()) {
      case 'CARPOOLING':
        return 'category-carpooling';
      case 'FASTPOST':
        return 'category-fastpost';
      case 'PRODUCT':
        return 'category-product';
      default:
        return 'category-other';
    }
  }

  getCategoryIcon(): string {
    if (!this.ad || !this.ad.category) return 'fa-question-circle';

    switch (this.ad.category.toUpperCase()) {
      case 'CARPOOLING':
        return 'fa-car';
      case 'FASTPOST':
        return 'fa-shipping-fast';
      case 'PRODUCT':
        return 'fa-shopping-bag';
      default:
        return 'fa-tag';
    }
  }

  getTagsArray(): string[] {
    if (!this.ad || !this.ad.tags) return [];

    // If tags is already an array, return it
    if (Array.isArray(this.ad.tags)) {
      return this.ad.tags as string[];
    }

    // If tags is a string, split it by commas
    if (typeof this.ad.tags === 'string') {
      return (this.ad.tags as string).split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0);
    }

    return [];
  }

  viewOnMap(): void {
    if (!this.ad || !this.ad.location || this.ad.location.length !== 2) {
      alert('No location data available for this ad.');
      return;
    }

    // Open Google Maps with the location
    const lat = this.ad.location[1];
    const lng = this.ad.location[0];
    const url = `https://www.google.com/maps?q=${lat},${lng}`;
    window.open(url, '_blank');
  }

  getStatusClass(): string {
    if (!this.ad) return 'status-unknown';

    if (this.ad.status === 'APPROVED') {
      return 'status-approved';
    } else if (this.ad.status === 'PENDING_ADMIN_REVIEW') {
      return 'status-pending';
    } else if (this.ad.status === 'REJECTED') {
      return 'status-rejected';
    } else if (this.ad.status === 'EXPIRED') {
      return 'status-expired';
    } else {
      return 'status-unknown';
    }
  }

  getStatusIcon(): string {
    if (!this.ad) return 'fa-question-circle';

    if (this.ad.status === 'APPROVED') {
      return 'fa-check-circle';
    } else if (this.ad.status === 'PENDING_ADMIN_REVIEW') {
      return 'fa-clock';
    } else if (this.ad.status === 'REJECTED') {
      return 'fa-times-circle';
    } else if (this.ad.status === 'EXPIRED') {
      return 'fa-calendar-times';
    } else {
      return 'fa-question-circle';
    }
  }

  getStatusText(): string {
    if (!this.ad) return 'Unknown';

    if (this.ad.status === 'APPROVED') {
      return 'Approved';
    } else if (this.ad.status === 'PENDING_ADMIN_REVIEW') {
      return 'Pending Review';
    } else if (this.ad.status === 'REJECTED') {
      return 'Rejected';
    } else if (this.ad.status === 'EXPIRED') {
      return 'Expired';
    } else {
      return 'Unknown';
    }
  }
}
