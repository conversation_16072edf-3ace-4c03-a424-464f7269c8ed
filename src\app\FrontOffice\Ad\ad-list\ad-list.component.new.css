/* General Styles */
body {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #333;
  background-color: #f9f9f9;
}

/* Page Wrapper */
.ads-page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Section with Background */
.ads-page-header {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ads-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ads-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.new-ads-counter {
  font-size: 0.9rem;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  display: inline-block;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Custom container */
.custom-container {
  padding: 0 15px;
  max-width: 1200px;
}

/* Button Styles */
.btn.btn-primary {
  background-color: #4CAF50; /* Green */
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 0.6rem 1.4rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s, transform 0.3s;
}

.btn.btn-primary:hover {
  background-color: #45a049;
  transform: translateY(-2px);
}

.btn.btn-outline-light {
  border-radius: 20px;
  padding: 0.6rem 1.4rem;
  transition: all 0.3s ease;
}

.btn.btn-outline-light:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Filter Sidebar */
.filter-sidebar {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 20px;
}

/* Filter Header */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.filter-header h5 {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.btn-reset-all {
  background: none;
  border: none;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-reset-all:hover {
  color: #F44336;
  background-color: rgba(244, 67, 54, 0.1);
}

/* Search Box */
.search-box {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.search-box .input-group {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-box .input-group-text,
.search-box .form-control {
  border-color: #e9ecef;
}

.search-box .form-control:focus {
  box-shadow: none;
  border-color: #4CAF50;
}

/* Filter Sections */
.filter-section {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.filter-section-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.8rem;
}

/* Category Filter Buttons */
.category-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid #e9ecef;
  background-color: white;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.filter-btn:hover {
  background-color: #f8f9fa;
}

.filter-btn.active {
  background-color: #e8f5e9;
  border-color: #4CAF50;
  color: #4CAF50;
  font-weight: 500;
}

.all-btn.active i {
  color: #4CAF50;
}

.category-carpooling-btn.active {
  background-color: #e8f5e9;
  border-color: #4CAF50;
  color: #4CAF50;
}

.category-fastpost-btn.active {
  background-color: #e3f2fd;
  border-color: #2196F3;
  color: #2196F3;
}

.category-product-btn.active {
  background-color: #f3e5f5;
  border-color: #9c27b0;
  color: #9c27b0;
}

.category-other-btn.active {
  background-color: #f5f5f5;
  border-color: #9E9E9E;
  color: #9E9E9E;
}

/* Toggle Filters Button */
.btn-toggle-filters {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.8rem 1rem;
  background-color: #f8f9fa;
  border: none;
  border-bottom: 1px solid #eee;
  font-size: 0.85rem;
  font-weight: 500;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-toggle-filters:hover {
  background-color: #f0f0f0;
}

.btn-toggle-filters i {
  font-size: 0.8rem;
  transition: transform 0.2s ease;
}

/* Advanced Filters */
.advanced-filters {
  padding: 0;
}

.date-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radius-slider {
  margin-top: 0.8rem;
}

.radius-value {
  font-weight: 600;
  color: #4CAF50;
}

/* Filter Actions */
.filter-actions {
  padding: 1rem;
  display: flex;
  justify-content: center;
}

.btn-apply-filters {
  width: 100%;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Results Control Bar */
.results-control-bar {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Active Filters */
.active-filters-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.active-filters-label {
  font-size: 0.85rem;
  color: #666;
  margin-right: 0.5rem;
}

.active-filters-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-chip {
  display: inline-flex;
  align-items: center;
  background-color: #4CAF50;
  color: white;
  padding: 0.3rem 0.5rem;
  border-radius: 20px;
  font-size: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chip-icon {
  margin-right: 5px;
  font-size: 0.7rem;
}

.chip-text {
  margin-right: 5px;
}

.chip-remove {
  background: none;
  border: none;
  color: white;
  font-size: 0.7rem;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.chip-remove:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.btn-clear-filters {
  background: none;
  border: none;
  color: #666;
  font-size: 0.75rem;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 20px;
  transition: all 0.2s ease;
  margin-left: auto;
}

.btn-clear-filters:hover {
  background-color: #f0f0f0;
  color: #F44336;
}

/* Sort Controls */
.sort-controls {
  display: flex;
  justify-content: flex-end;
}

.sort-dropdown {
  display: flex;
  align-items: center;
}

.sort-label {
  font-size: 0.85rem;
  color: #666;
  margin-right: 0.5rem;
}

.sort-dropdown .form-select {
  border-radius: 20px;
  border-color: #e9ecef;
  font-size: 0.85rem;
  padding: 0.4rem 2rem 0.4rem 0.8rem;
  cursor: pointer;
  background-position: right 0.5rem center;
}

/* Ads Grid */
.ads-grid {
  margin-bottom: 2rem;
}

/* No Results Message */
.no-results {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 3rem 1rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.no-ads-message {
  max-width: 400px;
  margin: 0 auto;
}

.no-ads-message i {
  color: #e0e0e0;
}

.no-ads-message h4 {
  margin: 1rem 0 0.5rem;
  color: #555;
}

.no-ads-message p {
  color: #888;
  margin-bottom: 1.5rem;
}

/* Ad Cards */
.ad-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.ad-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

/* Style for ads owned by the current user */
.ad-card.my-ad {
  border-left: 4px solid #4CAF50;
}

.ad-card.my-ad:after {
  content: "My Ad";
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #4CAF50;
  color: white;
  padding: 3px 8px;
  font-size: 0.65rem;
  border-radius: 20px;
  z-index: 11;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Card Top Section */
.ad-card-top {
  position: relative;
}

/* Image Section */
.ad-image-wrapper {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.carousel,
.carousel-inner,
.carousel-item {
  height: 100%;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.ad-card:hover .carousel-item img {
  transform: scale(1.05);
}

/* Category Badge */
.category-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.65rem;
  font-weight: 600;
  text-transform: uppercase;
  color: white;
  z-index: 1;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.category-carpooling {
  background-color: #4CAF50; /* Green */
}

.category-fastpost {
  background-color: #2196F3; /* Blue */
}

.category-product {
  background-color: #FF9800; /* Orange */
}

.category-other {
  background-color: #9E9E9E; /* Grey */
}

/* Price Tag */
.price-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #FF9800; /* Orange */
  color: white;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  z-index: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.price-na {
  background-color: #9E9E9E !important; /* Grey */
}

/* Card Action Buttons */
.card-action-buttons {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e0e0e0;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.85rem;
  padding: 0;
}

.action-btn:hover {
  transform: scale(1.05);
}

.flag-btn {
  color: #F44336;
}

.flag-btn:hover {
  background-color: #F44336;
  color: white;
  border-color: #F44336;
  box-shadow: 0 3px 6px rgba(244, 67, 54, 0.3);
}

.edit-btn:hover {
  background-color: #FF9800;
  color: white;
  border-color: #FF9800;
  box-shadow: 0 3px 6px rgba(255, 152, 0, 0.3);
}

.delete-btn:hover {
  background-color: #F44336;
  color: white;
  border-color: #F44336;
  box-shadow: 0 3px 6px rgba(244, 67, 54, 0.3);
}

.admin-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Card Content */
.ad-card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

/* Ad Title and Description */
.ad-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ad-description {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 0.8rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Timeline Section */
.ad-timeline {
  display: flex;
  gap: 8px;
  margin-bottom: 0.8rem;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px dashed #e0e0e0;
  position: relative;
}

.ad-timeline:before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 12px;
  border: 1px dashed #4CAF50;
  opacity: 0.3;
  pointer-events: none;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.timeline-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.timeline-item:hover .timeline-icon {
  transform: scale(1.1);
}

.start-icon {
  background-color: #e3f2fd;
}

.start-icon i {
  color: #2196F3;
  font-size: 0.8rem;
}

.end-icon {
  background-color: #fff3e0;
}

.end-icon i {
  color: #FF9800;
  font-size: 0.8rem;
}

.timeline-content {
  display: flex;
  flex-direction: column;
}

.timeline-date {
  font-weight: 600;
  color: #333;
  font-size: 0.75rem;
}

.timeline-label {
  color: #757575;
  font-size: 0.65rem;
  margin-top: 1px;
}

/* Card Footer */
.ad-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 10px;
  border-top: 1px dashed #e0e0e0;
}

/* Stats */
.ad-stats {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-btn, .stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #757575;
}

.stat-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 15px;
  transition: all 0.2s ease;
}

.stat-btn:hover {
  background-color: #f5f5f5;
}

.like-btn.active {
  color: #F44336;
}

.like-btn.active i {
  color: #F44336;
}

.stat-item i {
  font-size: 0.8rem;
}

.posted-date {
  color: #9C27B0;
}

.posted-date i {
  color: #9C27B0;
}

/* Card Buttons */
.card-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e3f2fd;
  color: #2196F3;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.8rem;
}

.contact-btn:hover {
  background-color: #2196F3;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(33, 150, 243, 0.3);
}

.quick-view-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-view-btn:hover {
  background-color: #43a047;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(76, 175, 80, 0.3);
}

/* Carousel Customization */
.carousel-control-prev,
.carousel-control-next {
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-control-prev {
  left: 10px;
}

.carousel-control-next {
  right: 10px;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 15px;
  height: 15px;
  background-size: 100%;
  filter: invert(1) grayscale(100%) brightness(30%);
}

.ad-card:hover .carousel-control-prev,
.ad-card:hover .carousel-control-next {
  opacity: 1;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-50%) scale(1.1);
}

/* No Image Placeholder */
.no-image-placeholder {
  height: 100%;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-image-placeholder i {
  font-size: 2.5rem;
  color: #9e9e9e;
  opacity: 0.7;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.pagination-dots {
  display: flex;
  gap: 8px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: inline-block;
  transition: all 0.3s ease;
}

.dot.active {
  background-color: #4CAF50;
  transform: scale(1.2);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .ad-timeline {
    flex-direction: column;
    gap: 6px;
  }

  .timeline-item {
    width: 100%;
  }
}

@media (max-width: 992px) {
  .ads-title {
    font-size: 2rem;
  }

  .ad-image-wrapper {
    height: 150px;
  }

  .ad-card-content {
    padding: 0.8rem;
  }

  .ad-title {
    font-size: 0.9rem;
  }

  .ad-description {
    font-size: 0.8rem;
    margin-bottom: 0.6rem;
  }
}

@media (max-width: 768px) {
  .ads-page-header {
    padding: 1.5rem 0;
  }

  .ads-title {
    font-size: 1.8rem;
    text-align: center;
  }

  .ads-subtitle {
    text-align: center;
    margin-bottom: 1rem;
  }

  .filter-sidebar {
    margin-bottom: 1.2rem;
    position: static;
  }

  .results-control-bar .row {
    flex-direction: column;
  }

  .active-filters-wrapper {
    margin-bottom: 1rem;
  }

  .sort-controls {
    justify-content: flex-start;
  }

  .ad-image-wrapper {
    height: 140px;
  }

  .ad-timeline {
    padding: 8px;
    margin-bottom: 0.6rem;
  }

  .timeline-icon {
    width: 24px;
    height: 24px;
  }

  .timeline-date {
    font-size: 0.7rem;
  }

  .timeline-label {
    font-size: 0.6rem;
  }

  .ad-card-footer {
    flex-direction: row;
    gap: 8px;
    padding-top: 8px;
  }

  .ad-stats {
    gap: 6px;
  }

  .stat-btn, .stat-item {
    font-size: 0.7rem;
  }

  .card-buttons {
    gap: 6px;
  }

  .contact-btn {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .quick-view-btn {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .ads-page-header {
    padding: 1.2rem 0;
  }

  .ads-title {
    font-size: 1.6rem;
  }

  .ads-subtitle {
    font-size: 0.9rem;
  }

  .new-ads-counter {
    font-size: 0.8rem;
    padding: 3px 10px;
  }

  .category-badge, .price-tag {
    font-size: 0.6rem;
    padding: 3px 8px;
  }

  .ad-card-content {
    padding: 0.8rem;
  }

  .ad-title {
    font-size: 0.85rem;
    margin-bottom: 0.4rem;
  }

  .ad-description {
    font-size: 0.75rem;
    margin-bottom: 0.6rem;
    -webkit-line-clamp: 2;
    line-height: 1.4;
  }

  .ad-timeline {
    padding: 6px;
    margin-bottom: 0.6rem;
    gap: 4px;
  }

  .timeline-icon {
    width: 22px;
    height: 22px;
  }

  .timeline-icon i {
    font-size: 0.7rem;
  }

  .timeline-date {
    font-size: 0.65rem;
  }

  .timeline-label {
    font-size: 0.55rem;
  }

  .stat-btn, .stat-item {
    font-size: 0.65rem;
    gap: 3px;
  }

  .stat-item i {
    font-size: 0.7rem;
  }

  .contact-btn {
    width: 22px;
    height: 22px;
    font-size: 0.65rem;
  }

  .quick-view-btn {
    padding: 3px 6px;
    font-size: 0.65rem;
  }
}
