.notifications-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notifications-header h1 {
  margin: 0;
}

.notifications-content {
  margin-top: 20px;
}

.notification-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.notification-card.unread {
  background-color: rgba(33, 150, 243, 0.05);
  border-left: 4px solid #2196F3;
}

/* Styling for different notification types */
.notification-card[data-type="AD_REPORT"] {
  border-left: 4px solid #2196F3; /* Primary color for reports */
}

.notification-card[data-type="AD_WARNING"] {
  border-left: 4px solid #FF9800; /* Accent color for warnings */
}

.notification-card[data-type="AD_DELETION"] {
  border-left: 4px solid #F44336; /* Warn color for deletions */
}

.notification-card[data-type="AD_LIKE"] {
  border-left: 4px solid #E91E63; /* Pink color for likes */
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.empty-state, .error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: rgba(0, 0, 0, 0.54);
  text-align: center;
}

.empty-state mat-icon, .error-message mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.error-message {
  color: #f44336;
}
