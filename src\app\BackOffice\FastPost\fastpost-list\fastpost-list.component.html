<app-navbar-back></app-navbar-back>
<app-sidebar-back></app-sidebar-back>
<div class="container mt-4">
  <div class="row">
    <!-- The offset adds left space -->
    <div class="offset-md-2 col-md-10">
  <h2 class="mb-3">Fast Posts</h2>
      <div class="d-flex justify-content-between align-items-center mb-3">
        <a class="btn btn-danger px-4 py-2" routerLink="/admin">
          <i class="bi bi-speedometer2"></i> Dashboard
        </a>
      </div>

  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead class="table-dark">
      <tr>
        <th>Receiver Name</th>
        <th>Address</th>
        <th>Telephone</th>
        <th>Package Weight with KG</th>
        <th>Status</th>

        <th class="text-center">Actions</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let fastPost of fastPosts">
        <td>{{ fastPost.receiverName }}</td>
        <td>{{ fastPost.receiverAddress }}</td>
        <td>{{ fastPost.receiverTelNbr }}</td>
        <td>{{ fastPost.packageWeight }}</td>
        <td>
              <span
                class="badge"
                [ngClass]="{
                  'bg-warning text-dark': fastPost.fastPostStatus === 'PENDING',
                  'bg-success': fastPost.fastPostStatus === 'APPROVED',
                  'bg-danger': fastPost.fastPostStatus === 'REJECTED'
                }">{{fastPost.fastPostStatus}}
              </span>
        </td>
        <td class="text-center">
          <!-- ✅ Approve Button (Visible only if leave is PENDING) -->
          <button *ngIf="fastPost.fastPostStatus === 'PENDING'" class="btn btn-success btn-sm me-2" (click)="approveFastPost(fastPost)">
            <i class="fas fa-check-circle"></i> Approve
          </button>

          <!-- ❌ Reject Button (Visible only if leave is PENDING) -->
          <button *ngIf="fastPost.fastPostStatus === 'PENDING'" class="btn btn-danger btn-sm" (click)="rejectFastPost(fastPost)">
            <i class="fas fa-times-circle"></i> Reject
          </button>

          <!-- Edit Button -->
          <a class="btn btn-sm btn-info me-2" [routerLink]="['/fastposts/edit', fastPost.idF]">
            <i class="bi bi-pencil"></i> Edit
          </a>

          <!-- Delete Button -->
          <button class="btn btn-sm btn-danger" (click)="deleteFastPost(fastPost.idF)">
            <i class="bi bi-trash"></i> Delete
          </button>
        </td>


      </tr>
      </tbody>
    </table>
  </div>
    </div>
    </div>
</div>
