import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval, of } from 'rxjs';
import { switchMap, tap, catchError } from 'rxjs/operators';
import { NotificationControllerService } from '../openapi';
import { Notification } from '../openapi/model/notification';
import { ExtendedNotification } from '../models/notification.model';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private notificationsSubject = new BehaviorSubject<ExtendedNotification[]>([]);
  private pollingSubscription: any;

  // API URL for notification endpoints
  // Note: We're using both 8089 and 8091 to try both possible API URLs
  private apiUrl = 'http://localhost:8089/speedygo';
  private fallbackApiUrl = 'http://localhost:8091/speedygo';

  // Observable streams that components can subscribe to
  public unreadCount$ = this.unreadCountSubject.asObservable();
  public notifications$ = this.notificationsSubject.asObservable();

  constructor(
    private notificationControllerService: NotificationControllerService,
    private http: HttpClient
  ) { }

  /**
   * Start polling for notifications for a specific user
   * @param userId The ID of the user
   * @param pollingInterval The interval in milliseconds (default: 30 seconds)
   */
  startPolling(userId: string, pollingInterval: number = 30000): void {
    // Stop any existing polling
    this.stopPolling();

    // Initial load
    this.loadNotifications(userId);
    this.loadUnreadCount(userId);

    // Set up polling
    this.pollingSubscription = interval(pollingInterval).pipe(
      tap(() => this.loadNotifications(userId)),
      tap(() => this.loadUnreadCount(userId))
    ).subscribe();
  }

  /**
   * Stop polling for notifications
   */
  stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }
  }

  /**
   * Load notifications for a specific user
   * @param userId The ID of the user
   */
  loadNotifications(userId: string): void {
    if (!userId) {
      console.error('NotificationService: Cannot load notifications - userId is empty');
      this.notificationsSubject.next([]);
      return;
    }

    console.log(`NotificationService: Loading notifications for user: ${userId}`);

    // Try the primary API URL first
    const url = `${this.apiUrl}/notification/user/${userId}`;
    console.log(`NotificationService: Request URL: ${url}`);

    // Use direct HTTP call instead of the generated service
    this.http.get<ExtendedNotification[]>(url).pipe(
      catchError((error: any) => {
        console.error('NotificationService: Error loading notifications from primary URL', error);

        // If the primary URL fails, try the fallback URL
        console.log(`NotificationService: Trying fallback URL for notifications`);
        const fallbackUrl = `${this.fallbackApiUrl}/notification/user/${userId}`;

        return this.http.get<ExtendedNotification[]>(fallbackUrl).pipe(
          catchError((fallbackError: any) => {
            console.error('NotificationService: Error loading notifications from fallback URL', fallbackError);
            return of([]);
          })
        );
      })
    ).subscribe((notifications: ExtendedNotification[]) => {
      console.log(`NotificationService: Received ${notifications.length} notifications:`, notifications);

      // Sort notifications by date (newest first)
      const sortedNotifications = [...notifications].sort((a, b) => {
        const dateA = a.date ? new Date(a.date).getTime() : 0;
        const dateB = b.date ? new Date(b.date).getTime() : 0;
        return dateB - dateA;
      });

      this.notificationsSubject.next(sortedNotifications);
    });
  }

  /**
   * Load unread notification count for a specific user
   * @param userId The ID of the user
   */
  loadUnreadCount(userId: string): void {
    if (!userId) {
      console.error('NotificationService: Cannot load unread count - userId is empty');
      this.unreadCountSubject.next(0);
      return;
    }

    console.log(`NotificationService: Loading unread count for user: ${userId}`);

    // Try the primary API URL first
    const url = `${this.apiUrl}/notification/unreadCount/${userId}`;
    console.log(`NotificationService: Request URL: ${url}`);

    // Use direct HTTP call instead of the generated service
    this.http.get<{userId: string, unreadCount: number}>(url).pipe(
      catchError((error: any) => {
        console.error('NotificationService: Error loading unread count from primary URL', error);

        // If the primary URL fails, try the fallback URL
        console.log(`NotificationService: Trying fallback URL for unread count`);
        const fallbackUrl = `${this.fallbackApiUrl}/notification/unreadCount/${userId}`;

        return this.http.get<{userId: string, unreadCount: number}>(fallbackUrl).pipe(
          catchError((fallbackError: any) => {
            console.error('NotificationService: Error loading unread count from fallback URL', fallbackError);
            return of({userId, unreadCount: 0});
          })
        );
      })
    ).subscribe((response: {userId: string, unreadCount: number}) => {
      console.log(`NotificationService: Received unread count:`, response);
      this.unreadCountSubject.next(response.unreadCount);
    });
  }

  /**
   * Mark a notification as read
   * @param id The ID of the notification
   * @param userId The ID of the user (to refresh counts after marking as read)
   */
  markAsRead(id: string, userId: string): Observable<ExtendedNotification> {
    console.log(`NotificationService: Marking notification ${id} as read for user ${userId}`);

    // Try the primary API URL first
    const url = `${this.apiUrl}/notification/markAsRead/${id}`;

    // Use direct HTTP call instead of the generated service
    return this.http.put<ExtendedNotification>(url, {}).pipe(
      tap(() => {
        console.log(`NotificationService: Successfully marked notification ${id} as read`);
        // Refresh notifications and unread count
        this.loadNotifications(userId);
        this.loadUnreadCount(userId);
      }),
      catchError((error: any) => {
        console.error('NotificationService: Error marking notification as read from primary URL', error);

        // If the primary URL fails, try the fallback URL
        console.log(`NotificationService: Trying fallback URL for marking notification as read`);
        const fallbackUrl = `${this.fallbackApiUrl}/notification/markAsRead/${id}`;

        return this.http.put<ExtendedNotification>(fallbackUrl, {}).pipe(
          tap(() => {
            console.log(`NotificationService: Successfully marked notification ${id} as read using fallback URL`);
            // Refresh notifications and unread count
            this.loadNotifications(userId);
            this.loadUnreadCount(userId);
          }),
          catchError((fallbackError: any) => {
            console.error('NotificationService: Error marking notification as read from fallback URL', fallbackError);
            return of({} as ExtendedNotification);
          })
        );
      })
    );
  }

  /**
   * Get notifications for a specific user
   * @param userId The ID of the user
   */
  getNotificationsByUserId(userId: string): Observable<ExtendedNotification[]> {
    console.log(`Getting notifications for user: ${userId}`);
    const url = `${this.apiUrl}/notification/user/${userId}`;
    console.log(`Request URL: ${url}`);

    return this.http.get<ExtendedNotification[]>(url).pipe(
      catchError((error: any) => {
        console.error('Error getting notifications', error);
        return of([]);
      })
    );
  }

  /**
   * Mark a notification as read
   * @param id The ID of the notification
   */
  markNotificationAsRead(id: string): Observable<ExtendedNotification> {
    return this.http.put<ExtendedNotification>(`${this.apiUrl}/notification/markAsRead/${id}`, {}).pipe(
      catchError((error: any) => {
        console.error('Error marking notification as read', error);
        return of({} as ExtendedNotification);
      })
    );
  }

  /**
   * Mark all notifications as read
   * @param ids Array of notification IDs to mark as read
   */
  markAllNotificationsAsRead(ids: string[]): Observable<any> {
    console.log(`NotificationService: Marking all notifications as read:`, ids);

    // Try the primary API URL first
    const url = `${this.apiUrl}/notification/markAllAsRead`;

    return this.http.put<any>(url, ids).pipe(
      tap(() => {
        console.log(`NotificationService: Successfully marked all notifications as read`);
      }),
      catchError((error: any) => {
        console.error('NotificationService: Error marking all notifications as read from primary URL', error);

        // If the primary URL fails, try the fallback URL
        console.log(`NotificationService: Trying fallback URL for marking all notifications as read`);
        const fallbackUrl = `${this.fallbackApiUrl}/notification/markAllAsRead`;

        return this.http.put<any>(fallbackUrl, ids).pipe(
          tap(() => {
            console.log(`NotificationService: Successfully marked all notifications as read using fallback URL`);
          }),
          catchError((fallbackError: any) => {
            console.error('NotificationService: Error marking all notifications as read from fallback URL', fallbackError);
            return of({});
          })
        );
      })
    );
  }

  /**
   * Delete a notification
   * @param id The ID of the notification
   */
  deleteNotification(id: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/notification/${id}`).pipe(
      catchError((error: any) => {
        console.error('Error deleting notification', error);
        return of({});
      })
    );
  }

  /**
   * Mark all notifications as read for a specific user
   * @param userId The ID of the user
   */
  markAllAsReadForUser(userId: string): void {
    if (!userId) {
      console.error('NotificationService: Cannot mark all as read - userId is empty');
      return;
    }

    console.log(`NotificationService: Marking all notifications as read for user: ${userId}`);

    // Get all notifications for the user
    const notifications = this.notificationsSubject.getValue();

    // Get IDs of unread notifications
    const unreadIds = notifications
      .filter(notification => !notification.read)
      .map(notification => notification.id)
      .filter(id => id !== undefined) as string[]; // Filter out undefined IDs and cast to string[]

    if (unreadIds.length === 0) {
      console.log('NotificationService: No unread notifications to mark as read');
      return;
    }

    console.log('NotificationService: Marking these IDs as read:', unreadIds);

    // Mark all as read
    this.markAllNotificationsAsRead(unreadIds).subscribe(() => {
      // Refresh notifications and unread count
      this.loadNotifications(userId);
      this.loadUnreadCount(userId);
    });
  }

  /**
   * Get the primary API URL
   * @returns The primary API URL
   */
  getApiUrl(): string {
    return this.apiUrl;
  }

  /**
   * Get the fallback API URL
   * @returns The fallback API URL
   */
  getFallbackApiUrl(): string {
    return this.fallbackApiUrl;
  }
}
