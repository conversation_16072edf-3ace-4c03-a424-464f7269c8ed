import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

export interface LikeData {
  adId: string;
  count: number;
  userLiked: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LikeService {
  private apiUrl = 'http://localhost:8080/api/likes';
  private readonly STORAGE_KEY = 'ad_likes_data';

  // Store like data in memory
  private likesData = new BehaviorSubject<Map<string, LikeData>>(new Map());

  constructor(private http: HttpClient) {
    this.loadInitialLikes();
  }

  // Get all likes for the current user
  private loadInitialLikes(): void {
    const userId = localStorage.getItem('userId');
    if (!userId) {
      console.log('No user ID found, skipping initial likes load');
      return;
    }

    console.log('Loading initial likes for user:', userId);

    // Try to load likes data from localStorage
    const storedLikesData = localStorage.getItem(this.STORAGE_KEY);
    let likesMap = new Map<string, LikeData>();

    if (storedLikesData) {
      try {
        // Convert the stored JSON back to a Map
        const parsedData = JSON.parse(storedLikesData);

        // Create a new Map from the parsed data
        likesMap = new Map();

        // Handle different possible formats of the stored data
        if (Array.isArray(parsedData)) {
          // If it's an array of entries
          parsedData.forEach((entry) => {
            if (Array.isArray(entry) && entry.length === 2) {
              // It's in the format of [key, value]
              likesMap.set(entry[0], entry[1]);
            }
          });
        } else if (typeof parsedData === 'object') {
          // If it's an object with keys
          Object.keys(parsedData).forEach(key => {
            likesMap.set(key, parsedData[key]);
          });
        }

        console.log('Loaded likes data from localStorage:', likesMap);
      } catch (error) {
        console.error('Error parsing stored likes data:', error);
        // If there's an error, we'll use the default empty map
      }
    } else {
      console.log('No stored likes data found, initializing with default data');

      // Initialize with a few sample ads that have some likes
      likesMap.set('1', { adId: '1', count: 5, userLiked: false });
      likesMap.set('2', { adId: '2', count: 3, userLiked: false });
      likesMap.set('3', { adId: '3', count: 7, userLiked: false });

      // Save the initial data to localStorage
      this.saveToLocalStorage(likesMap);
    }

    console.log('Initial likes data loaded:', likesMap);
    this.likesData.next(likesMap);
  }

  // Save likes data to localStorage
  private saveToLocalStorage(likesMap: Map<string, LikeData>): void {
    try {
      // Convert Map to array for JSON serialization
      const serializedData = JSON.stringify(Array.from(likesMap.entries()));
      localStorage.setItem(this.STORAGE_KEY, serializedData);
      console.log('Likes data saved to localStorage');
    } catch (error) {
      console.error('Error saving likes data to localStorage:', error);
    }
  }

  // Get like data for a specific ad
  getLikeData(adId: string): Observable<LikeData> {
    console.log(`Getting like data for ad ${adId}`);

    // Get current user ID
    const userId = localStorage.getItem('userId');

    // Create a unique key for this user's like on this ad
    const userLikeKey = userId ? `user_like_${userId}_ad_${adId}` : null;

    // Check if this user has already liked this ad
    const hasUserLiked = userLikeKey ? localStorage.getItem(userLikeKey) === 'true' : false;

    return this.likesData.pipe(
      map(likesMap => {
        if (likesMap.has(adId)) {
          const data = likesMap.get(adId)!;

          // Update the userLiked property based on whether this specific user has liked the ad
          const updatedData = {
            ...data,
            userLiked: hasUserLiked
          };

          console.log(`Found existing like data for ad ${adId}:`, updatedData);
          return updatedData;
        } else {
          // If we don't have data for this ad, create default data with 0 likes
          const defaultData: LikeData = {
            adId,
            count: 0, // Start with 0 likes for new ads
            userLiked: hasUserLiked
          };

          console.log(`Created new like data for ad ${adId}:`, defaultData);

          // Store it for future reference
          const updatedMap = new Map(likesMap);
          updatedMap.set(adId, defaultData);
          this.likesData.next(updatedMap);

          // Save the updated data to localStorage
          this.saveToLocalStorage(updatedMap);

          return defaultData;
        }
      })
    );
  }

  // Toggle like status for an ad
  toggleLike(adId: string): Observable<LikeData> {
    const userId = localStorage.getItem('userId');
    if (!userId) {
      console.log('No user ID found, cannot toggle like');
      return of({ adId, count: 0, userLiked: false });
    }

    console.log(`Toggling like for ad ${adId} by user ${userId}`);

    // Create a unique key for this user's like on this ad
    const userLikeKey = `user_like_${userId}_ad_${adId}`;

    // Check if this user has already liked this ad
    const hasUserLiked = localStorage.getItem(userLikeKey) === 'true';

    console.log(`User ${userId} has ${hasUserLiked ? 'already liked' : 'not liked'} ad ${adId}`);

    return this.likesData.pipe(
      map(likesMap => {
        const currentData = likesMap.get(adId) || { adId, count: 0, userLiked: false };
        console.log('Current like data:', currentData);

        // Debug log to see the current state of the likesMap
        console.log('Current likes map:', Array.from(likesMap.entries()));

        // Update like status based on whether this user has already liked the ad
        const newUserLiked = !hasUserLiked;

        // Update the count based on the user's action
        let newCount = currentData.count;
        if (newUserLiked) {
          // User is liking the ad
          newCount += 1;
          // Store that this user has liked this ad
          localStorage.setItem(userLikeKey, 'true');
        } else {
          // User is unliking the ad
          newCount = Math.max(0, newCount - 1);
          // Remove the record that this user liked this ad
          localStorage.removeItem(userLikeKey);
        }

        const updatedData: LikeData = {
          ...currentData,
          count: newCount,
          userLiked: newUserLiked
        };

        console.log('Updated like data:', updatedData);

        // Update the map
        const updatedMap = new Map(likesMap);
        updatedMap.set(adId, updatedData);
        this.likesData.next(updatedMap);

        // Save the updated data to localStorage
        this.saveToLocalStorage(updatedMap);

        // Save individual like data for this ad to localStorage for quick access
        const likeKey = `ad_like_${adId}`;
        localStorage.setItem(likeKey, JSON.stringify(updatedData));

        // Call the backend API to update the like in the database
        // This would be implemented in a real backend
        const apiUrl = 'http://localhost:8089/speedygo';
        const fallbackApiUrl = 'http://localhost:8091/speedygo';

        // Try primary URL first
        this.http.post(`${apiUrl}/likes/toggle`, {
          adId: adId,
          userId: userId,
          liked: newUserLiked
        }).subscribe({
          next: (response) => {
            console.log('Like updated in backend:', response);
          },
          error: (error) => {
            console.error('Error updating like in primary backend:', error);

            // Try fallback URL if primary fails
            this.http.post(`${fallbackApiUrl}/likes/toggle`, {
              adId: adId,
              userId: userId,
              liked: newUserLiked
            }).subscribe({
              next: (response) => {
                console.log('Like updated in fallback backend:', response);
              },
              error: (fallbackError) => {
                console.error('Error updating like in fallback backend:', fallbackError);
              }
            });
          }
        });

        return updatedData;
      })
    );
  }

  // Get all ads liked by the current user
  getLikedAdIds(): Observable<string[]> {
    return this.likesData.pipe(
      map(likesMap => {
        const likedAdIds: string[] = [];
        likesMap.forEach((data, adId) => {
          if (data.userLiked) {
            likedAdIds.push(adId);
          }
        });
        return likedAdIds;
      })
    );
  }

  // Check if an ad is liked by the current user
  isAdLiked(adId: string): Observable<boolean> {
    // Get current user ID
    const userId = localStorage.getItem('userId');
    if (!userId) return of(false);

    // Create a unique key for this user's like on this ad
    const userLikeKey = `user_like_${userId}_ad_${adId}`;

    // Check if this user has already liked this ad
    const hasUserLiked = localStorage.getItem(userLikeKey) === 'true';

    return of(hasUserLiked);
  }

  // Get the like count for an ad
  getLikeCount(adId: string): Observable<number> {
    return this.likesData.pipe(
      map(likesMap => {
        const data = likesMap.get(adId);
        return data ? data.count : 0;
      })
    );
  }
}
