.ad-detail-dialog {
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  width: 100%;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dialog-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-container p, .error-container p {
  margin-top: 16px;
  color: #6c757d;
}

.error-icon {
  font-size: 3rem;
  color: #dc3545;
  margin-bottom: 16px;
}

.ad-content {
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow-y: auto;
  position: relative;
}

.category-badge {
  position: absolute;
  top: 16px;
  left: 16px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  z-index: 1;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-carpooling {
  background-color: #28a745;
}

.category-fastpost {
  background-color: #007bff;
}

.category-product {
  background-color: #fd7e14;
}

.category-other {
  background-color: #6c757d;
}

.price-tag {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 6px 12px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 20px;
  font-weight: 600;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.image-gallery {
  width: 100%;
  position: relative;
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.image-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.ad-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.5s ease;
}

.image-container:hover .ad-image {
  transform: scale(1.05);
}

.image-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
}

.nav-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #4361ee;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: 0.8;
}

.nav-button:hover {
  background-color: white;
  transform: scale(1.1);
  opacity: 1;
}

.image-counter {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background-color: rgba(67, 97, 238, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.no-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #6c757d;
  border-radius: 8px;
}

.no-image-placeholder i {
  font-size: 3.5rem;
  margin-bottom: 12px;
  color: #4361ee;
  opacity: 0.7;
}

.ad-details {
  padding: 24px;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

.detail-section {
  margin-bottom: 28px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 24px;
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.ad-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #343a40;
  position: relative;
  padding-bottom: 12px;
  letter-spacing: -0.5px;
}

.ad-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #4361ee, #3a0ca3);
  border-radius: 3px;
}

.ad-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
  animation: fadeIn 0.6s ease;
}

.metadata-item {
  display: flex;
  align-items: center;
  color: #6c757d;
  background-color: rgba(67, 97, 238, 0.08);
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.metadata-item:hover {
  background-color: rgba(67, 97, 238, 0.12);
  transform: translateY(-2px);
}

.metadata-item i {
  margin-right: 8px;
  color: #4361ee;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #343a40;
  display: flex;
  align-items: center;
  position: relative;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background: linear-gradient(to bottom, #4361ee, #3a0ca3);
  margin-right: 10px;
  border-radius: 2px;
}

.section-title::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
  margin-left: 10px;
}

.detail-content {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.category-display, .price-display {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.category-display i, .price-display i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.category-display {
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  display: inline-flex;
}

.price-display {
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  display: inline-flex;
}

.timeline {
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 0 12px;
  margin: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e9ecef;
  z-index: 0;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.timeline-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: white;
}

.start-icon {
  background-color: #28a745;
}

.end-icon {
  background-color: #dc3545;
}

.timeline-content {
  text-align: center;
}

.timeline-date {
  display: block;
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-label {
  display: block;
  font-size: 0.85rem;
  color: #6c757d;
}

.description-content {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  color: #495057;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.tag-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #e9ecef;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #495057;
}

.tag-badge i {
  margin-right: 6px;
  font-size: 0.8rem;
  color: #6c757d;
}

.location-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.location-name {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.location-name i {
  margin-right: 10px;
  color: #dc3545;
}

.coordinates {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 8px;
}

.coordinate {
  display: flex;
  flex-direction: column;
}

.coordinate-label {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 4px;
}

.coordinate-value {
  font-weight: 500;
}

.view-on-map-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  color: #495057;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-on-map-btn:hover {
  background-color: #e9ecef;
}

.view-on-map-btn i {
  margin-right: 8px;
}

.status-display {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
}

.status-badge i {
  margin-right: 8px;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-expired {
  background-color: #e2e3e5;
  color: #383d41;
}

.status-unknown {
  background-color: #e9ecef;
  color: #6c757d;
}

.expiry-info {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-size: 0.9rem;
}

.expiry-info i {
  margin-right: 8px;
}

.contact-section {
  display: flex;
  gap: 16px;
  margin-top: 32px;
  animation: fadeInUp 0.7s ease;
}

.contact-button, .view-full-button {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.contact-button {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
}

.contact-button:hover {
  background: linear-gradient(135deg, #3a56e0, #2f0b82);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(67, 97, 238, 0.3);
}

.view-full-button {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

.view-full-button:hover {
  background: linear-gradient(135deg, #5a6268, #3d4246);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
}

.contact-button i, .view-full-button i {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .ad-content {
    flex-direction: row;
    max-height: calc(90vh - 60px); /* Adjust for header height */
  }

  .image-gallery {
    width: 50%;
    max-height: calc(90vh - 60px);
  }

  .image-container {
    height: 100%;
    max-height: calc(90vh - 60px);
  }

  .ad-details {
    width: 50%;
    overflow-y: auto;
    max-height: calc(90vh - 60px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}