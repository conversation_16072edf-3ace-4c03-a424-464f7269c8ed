import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { Complaint, ComplaintControllerService } from 'src/app/openapi';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';

// Interface for file preview
interface FilePreview {
  name: string;
  size: string;
  type: string;
}

@Component({
  selector: 'app-complaint-edit-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatRadioModule
  ],
  templateUrl: './complaint-edit-dialog.component.html',
  styleUrls: ['./complaint-edit-dialog.component.css']
})
export class ComplaintEditDialogComponent implements OnInit {
  complaintForm!: FormGroup;
  loading = true;
  error = '';
  submitting = false;
  selectedFiles: File[] = [];
  filePreviews: FilePreview[] = [];
  existingEvidence: string[] = [];

  // Categories from the backend enum
  categories = [
    { value: 'RETARD_LIVRAISON', label: 'Delivery Delay' },
    { value: 'PRODUIT_ENDOMMAGE', label: 'Damaged Product' },
    { value: 'MAUVAIS_SERVICE_CLIENT', label: 'Poor Customer Service' },
    { value: 'ERREUR_FACTURATION', label: 'Billing Error' },
    { value: 'ARTICLE_MANQUANT', label: 'Missing Item' },
    { value: 'LIVRAISON_MAUVAISE_ADRESSE', label: 'Wrong Delivery Address' },
    { value: 'COMMANDE_INCOMPLETE', label: 'Incomplete Order' },
    { value: 'MAUVAISE_QUALITE', label: 'Poor Quality' },
    { value: 'COMPORTEMENT_PERSONNEL', label: 'Staff Behavior' },
    { value: 'PROBLEME_TECHNIQUE', label: 'Technical Issue' },
    { value: 'PROBLEME_REMBOURSEMENT', label: 'Refund Problem' },
    { value: 'FRAIS_INATTENDUS', label: 'Unexpected Fees' },
    { value: 'PUBLICITE_MENSONGERE', label: 'Misleading Advertisement' },
    { value: 'SECURITE_HARCELEMENT', label: 'Security/Harassment' },
    { value: 'AUTRE', label: 'Other' }
  ];

  // Priority options
  priorities = [
    { value: 'LOW', label: 'Low - Not urgent' },
    { value: 'MEDIUM', label: 'Medium - Needs attention' },
    { value: 'HIGH', label: 'High - Urgent issue' }
  ];

  // Contact preference options
  contactPreferences = [
    { value: 'EMAIL', label: 'Email' },
    { value: 'PHONE', label: 'Phone' },
    { value: 'EITHER', label: 'Either' }
  ];

  @ViewChild('fileInput') fileInput!: ElementRef;

  constructor(
    private dialogRef: MatDialogRef<ComplaintEditDialogComponent>,
    private complaintService: ComplaintControllerService,
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: { complaintId: string }
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadComplaintData();
  }

  initForm(): void {
    this.complaintForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]],
      category: ['', Validators.required],
      priority: ['MEDIUM', Validators.required],
      contactPreference: ['EMAIL', Validators.required],
      contactEmail: ['', [Validators.email]],
      contactPhone: [''],
      status: ['PENDING'] // Status is typically not editable by users
    });

    // Add conditional validation for contact fields
    this.complaintForm.get('contactPreference')?.valueChanges.subscribe(preference => {
      const emailControl = this.complaintForm.get('contactEmail');
      const phoneControl = this.complaintForm.get('contactPhone');

      if (preference === 'EMAIL' || preference === 'EITHER') {
        emailControl?.setValidators([Validators.required, Validators.email]);
      } else {
        emailControl?.clearValidators();
        emailControl?.setValidators([Validators.email]);
      }

      if (preference === 'PHONE' || preference === 'EITHER') {
        phoneControl?.setValidators([Validators.required]);
      } else {
        phoneControl?.clearValidators();
      }

      emailControl?.updateValueAndValidity();
      phoneControl?.updateValueAndValidity();
    });
  }

  loadComplaintData(): void {
    if (!this.data.complaintId) {
      this.error = 'No complaint ID provided';
      this.loading = false;
      return;
    }

    this.complaintService.getComplaint(this.data.complaintId).subscribe({
      next: async (response) => {
        try {
          let complaint: Complaint;

          if (response instanceof Blob) {
            const text = await response.text();
            complaint = JSON.parse(text);
          } else {
            complaint = response;
          }

          console.log("Complaint data received for modification:", complaint);

          // Store existing evidence files
          if (complaint.evidence && complaint.evidence.length > 0) {
            this.existingEvidence = complaint.evidence;

            // Create previews for existing evidence files
            this.filePreviews = this.existingEvidence.map((file, index) => {
              const isImage = this.isImageFile(file);
              return {
                name: `Evidence File ${index + 1}`,
                size: isImage ? 'Image file' : 'Document file',
                type: isImage ? 'image/jpeg' : 'application/octet-stream'
              };
            });
          }

          // Patch form values
          this.complaintForm.patchValue({
            title: complaint.title,
            description: complaint.description,
            category: complaint.category,
            status: complaint.status,
            priority: complaint.priority || 'MEDIUM',
            contactPreference: complaint.contactPreference || 'EMAIL',
            contactEmail: complaint.contactEmail || '',
            contactPhone: complaint.contactPhone || ''
          });

          // Trigger validation for contact fields
          this.complaintForm.get('contactPreference')?.updateValueAndValidity();
        } catch (error) {
          console.error('Error processing complaint data', error);
          this.error = 'Failed to process complaint data';
        } finally {
          this.loading = false;
        }
      },
      error: (err: any) => {
        console.error('Error retrieving complaint data', err);
        this.error = 'Failed to load complaint data';
        this.loading = false;
      }
    });
  }

  // Check if a file is an image (based on base64 data)
  isImageFile(base64String: string): boolean {
    if (!base64String) return false;
    return base64String.startsWith('data:image/');
  }

  updateComplaint(): void {
    if (this.complaintForm.valid && !this.submitting) {
      this.submitting = true;

      // Process file uploads
      const processFiles = async () => {
        let evidenceFiles: string[] = [...this.existingEvidence]; // Start with existing files

        // Convert new files to base64 strings
        if (this.selectedFiles.length > 0) {
          const filePromises = this.selectedFiles.map(file => {
            return new Promise<string>((resolve) => {
              const reader = new FileReader();
              reader.onload = (e) => {
                const base64String = e.target?.result as string;
                resolve(base64String);
              };
              reader.readAsDataURL(file);
            });
          });

          // Wait for all files to be processed
          const newBase64Files = await Promise.all(filePromises);
          evidenceFiles = [...evidenceFiles, ...newBase64Files];
        }

        // Create the updated complaint object
        const updatedComplaint: Complaint = {
          ...this.complaintForm.value,
          id: this.data.complaintId,
          evidence: evidenceFiles
        };

        console.log('Updating complaint with data:', updatedComplaint);

        // Submit the updated complaint
        this.complaintService.updateComplaint(updatedComplaint).subscribe({
          next: () => {
            this.submitting = false;
            this.dialogRef.close(true); // Close with success result
          },
          error: (err: any) => {
            console.error("Error updating complaint", err);
            this.error = 'Failed to update complaint';
            this.submitting = false;
          }
        });
      };

      // Start the file processing
      processFiles().catch(error => {
        console.error("Error processing files", error);
        this.error = 'Failed to process files';
        this.submitting = false;
      });
    }
  }

  // File handling methods
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Limit to 3 files total (existing + new)
      const maxNewFiles = 3 - this.existingEvidence.length;
      const newFiles = Array.from(files).slice(0, maxNewFiles) as File[];

      if (newFiles.length === 0) {
        this.error = 'You have already reached the maximum of 3 evidence files';
        return;
      }

      // Add new files to the array
      this.selectedFiles = [...this.selectedFiles, ...newFiles];

      // Create previews for the new files
      const newPreviews = newFiles.map(file => ({
        name: file.name,
        size: this.formatFileSize(file.size),
        type: file.type
      }));

      // Add new previews to existing previews
      this.filePreviews = [...this.filePreviews, ...newPreviews];
    }
  }

  removeFile(index: number): void {
    // Check if it's an existing file or a new file
    if (index < this.existingEvidence.length) {
      // Remove existing file
      this.existingEvidence.splice(index, 1);
      this.filePreviews.splice(index, 1);
    } else {
      // Remove new file
      const newIndex = index - this.existingEvidence.length;
      this.selectedFiles.splice(newIndex, 1);
      this.filePreviews.splice(index, 1);
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  openFileSelector(): void {
    this.fileInput.nativeElement.click();
  }

  // Get character count for textarea
  getCharacterCount(controlName: string): number {
    return this.complaintForm.get(controlName)?.value?.length || 0;
  }

  // Get maximum character count for a field
  getMaxCharacterCount(controlName: string): number {
    if (controlName === 'title') return 100;
    if (controlName === 'description') return 500;
    return 0;
  }

  close(): void {
    this.dialogRef.close(false);
  }
}
