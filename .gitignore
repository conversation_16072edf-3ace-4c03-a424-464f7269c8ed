# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
/dist
/tmp
/out-tsc
/bazel-out
/coverage
/build
target/
.mvn/
.gradle/

# Node dependencies
/node_modules
npm-debug.log
yarn-error.log
pnpm-lock.yaml
package-lock.json
yarn.lock

# Angular cache
/.angular/
.angular/cache/

# Environment files (prevent exposing API keys)
src/environments/environment*.ts

# Logs
*.log
testem.log
libpeerconnection.log

# Linter and formatter reports
.eslintcache
.prettierignore
.prettiercache

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.iml
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/

# Miscellaneous
.sass-cache/
connect.lock
typings/

# System files
.DS_Store
Thumbs.db

src/app/openapi/
